import { Component, Input } from '@angular/core';
import { InputTextComponent } from '../input-text/input-text.component';
import { InputTextAreaComponent } from '../input-text-area/input-text-area.component';

@Component({
  selector: 'free-form-text-option',
  standalone: true,
  imports: [
    InputTextComponent,
    InputTextAreaComponent,
  ],
  templateUrl: './free-form-text-option.component.html',
  styleUrl: './free-form-text-option.component.scss'
})
export class FreeFormTextOptionComponent {

  @Input() option: any;

}
