:host {
  position: relative;
  height: auto !important;

  .form-step-container {
    justify-content: flex-start;

    form {
      
      .input-container {
        
        
            position: relative;
            margin-bottom: 20px;
          
            &.error-shake {
              animation: shake 0.82s cubic-bezier(.36,.07,.19,.97) both;
            }
          
            .error-message {
              color: #dc3545;
              font-size: 12px;
              margin-top: 5px;
              position: absolute;
              bottom: -20px;
              left: 0;
            }
          
          
          @keyframes shake {
            10%, 90% {
              transform: translate3d(-1px, 0, 0);
            }
            
            20%, 80% {
              transform: translate3d(2px, 0, 0);
            }
          
            30%, 50%, 70% {
              transform: translate3d(-4px, 0, 0);
            }
          
            40%, 60% {
              transform: translate3d(4px, 0, 0);
            }
          }


        .table-container {
          padding: 20px;
          background-color: #f7f7f7;
          border-radius: 12px;
          border: solid #d8d8d8 1px;

          .tabs-container {
            display: flex;
            flex-direction: row;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 16px;
            width: 100%;
            height: 50px;
            background-color: #cbe5fd;
            border-radius: 12px;
            transition: background-color 0.3s ease;

            &.samples-tab {
              background-color: #f5d9ff; // Pink shade for samples
            }
            &.panelists-tab {
              background-color: #fcd5e6; // Green shade for panelists
            }

            .tab-button {
              flex: 1;
              border: none;
              height: 100%;
              background: none;
              padding: 6px 12px;
              border-radius: 12px;
              cursor: pointer;
              font-size: 12px;
              font-weight: 500;
              color: #00519c;
              transition: all 0.3s ease;

              &.active {
                background-color: #0084ff;
                color: white;
                box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
              }

              &:hover:not(.active) {
                background-color: #6db4ff;
              }
            }
            .samples-tab {
              color: #f5d9ff;
              &.active {
                background-color: #9d3cfe;
              }
              &:hover:not(.samples-tab) {
                background-color: #f5d9ff;
              }
            }

            .panelists-tab {
              color: #fcd5e6;
              &.active {
                background-color: #d0397a;
              }
              &:hover:not(.panelists-tab) {
                background-color: #fcd5e6;
              }
            }
          }
        }
        .header-elem {
          display: flex;
          flex-direction: row;
          justify-content: space-between;
          align-items: center;
          font-size: 0.875rem;
          font-weight: 700;

          p {
            margin: 0 0 0 8px;
          }
        }

        .table-elem {
          //   width: 100%;
          margin-top: 30px;

          .table-form-container {
          }

          // Table Headers
          .table-header {
            display: grid;
            align-items: center;
            height: 30px;
            grid-template-columns: 100px 150px 1fr 100px;
            padding: 5px 20px;
            border-bottom: 1px solid #e2e8f0;
            background-color: #cbe5fd;
            color: #00519c;
            font-weight: 700;
            font-size: 12px;
            border-radius: 16px;

            &.samples-tab {
              background-color: #f5d9ff;
              color: #5b1e98;
            }

            // Panelists tab styling
            &.panelists-tab {
              background-color: #fcd5e6;
              color: #7e1353;
            }
          }

          // Table Body
          .table-body {
            .table-row {
              display: grid;
              grid-template-columns: 100px 150px 1fr 100px;
              padding: 16px 20px;
              align-items: center;
              background-color: #f7f7f7;
              border-radius: 18px;
              color: #292727;
              font-weight: 700;
              font-size: 12px;

              &:nth-child(even) {
                background-color: var(
                  --table-row-even-background,
                  #eeeeee
                ); // Using the global variable with fallback
              }

              //   &:hover {
              //     background-color: #f8fafc;
              //   }
            }
          }

          // Column styles
          .select-column {
            display: flex;
            align-items: center;

            input[type="radio"] {
              width: 15px;
              height: 15px;
              cursor: pointer;
              border: white solid 2px;
              border-radius: 6px;
              background-color: white;
              &:checked {
                background-color: #007bff;
                padding: 5px;
              }
            }
          }

          .status-column {
            display: flex;
            align-items: center;
            gap: 10px;

            .status-dot {
              width: 8px;
              height: 8px;
              border-radius: 50%;
              background-color: #10b981; // Green for "Ongoing"
            }

            .status-text {
              color: #4b5563;
            }
          }

          //   .name-column {
          //     font-size: 12px;
          //   }
          //   .type-column {
          //     font-size: 12px;
          //   }
          //   .organization-column {
          //     font-size: 12px;
          //   }
        }
        .submit-button {
          border: none;
          background: none;
          padding: 6px 12px;
          border-radius: 20px;
          cursor: pointer;
          font-size: 14px;
          font-weight: 500;
          color: #007bff;
        }
      }
      .title {
        flex: 1;
        // margin-right: 30px;
      }
      .search-container {
        flex: 2;
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-right: 10px;

        .search-input {
          width: 100%;

          ::ng-deep input {
            background-image: url("data:image/svg+xml,%3Csvg width='10' height='13' viewBox='0 0 10 13' fill='none' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath fill-rule='evenodd' clip-rule='evenodd' d='M7.23259 9.71276C5.05989 10.8734 2.33687 10.2213 0.896706 8.09053C-0.673857 5.76576 -0.127797 2.55724 2.11586 0.929125C4.35868 -0.698219 7.45525 -0.132417 9.02592 2.19236C10.4703 4.32995 10.1246 7.21541 8.3157 8.9317L9.87965 11.23C10.0892 11.5386 10.0182 11.965 9.72045 12.183C9.42266 12.4001 9.01023 12.3266 8.80071 12.018L7.23259 9.71276ZM7.0503 8.23243C8.69758 7.03757 9.09764 4.68192 7.94529 2.97613C6.7913 1.27015 4.51867 0.854776 2.8724 2.04963C1.22512 3.24449 0.825061 5.59927 1.97741 7.30593C3.1281 9.00762 5.39242 9.42472 7.0389 8.24096C7.04219 8.2384 7.04617 8.23499 7.0503 8.23243Z' fill='%23292727'/%3E%3C/svg%3E");
            background-repeat: no-repeat;
            background-position: 10px center;
            background-size: 12px;
            padding-left: 30px;
          }
        }
      }
      .filter-container {
        flex: 1;
        display: flex;
        justify-content: space-between;
        align-items: center;

        .filter-input {
          width: 100%;

          ::ng-deep input {
            background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='12' height='12' viewBox='0 0 12 12' fill='none'%3E%3Cpath d='M11.4369 0H0.563154C0.0635258 0 -0.188827 0.606234 0.165792 0.960853L4.5 5.29506V10.125C4.5 10.3085 4.58955 10.4805 4.73993 10.5858L6.61493 11.8979C6.93811 12.1273 7.375 11.8927 7.375 11.437V5.29506L11.7092 0.960853C12.0627 0.607264 11.8119 0 11.4369 0Z' fill='%23292727'/%3E%3C/svg%3E");
            background-repeat: no-repeat;
            background-position: 10px center;
            background-size: 12px;
            padding-left: 30px;
          }
        }
      }

      &.textarea-container {
        align-items: flex-start;

        label {
          line-height: 40px;
        }
      }

      &.radio-container {
        grid-template-columns: var(--input-container-label-width) 1fr 1fr;

        input-radio-block {
          width: 100%;
        }
      }

      &.date-picker-container {
        grid-template-columns: var(--input-container-label-width) 1fr 1fr;
        margin-top: -15px;

        svg {
          grid-column: 2;
          justify-self: end;
          top: -12px;
          right: -50px;
          position: relative;
        }

        input-bs-datepicker {
          justify-self: left;
          grid-column: 3;
          right: -35px;
          position: relative;
          // margin-right: 10px;
        }
      }

      input-text,
      input-text-area {
        width: 100%;
      }
    }
  }
}

.load-more-btn {
  margin-top: 1rem;
  padding: 0.5rem 1rem;
  color: #292727;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  width: 100%;
  text-align: center;

  &:hover {
      text-decoration: underline;
  }
}

.loading-spinner {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 30px;
  margin: 0 auto;
  
  .spinner {
    animation: rotate 2s linear infinite;
    width: 20px;
    height: 20px;
    
      & .path {
          stroke: currentColor;
          stroke-linecap: round;
          animation: dash 1.5s ease-in-out infinite;
      }
  }
}

@keyframes rotate {
100% {
  transform: rotate(360deg);
}
}

@keyframes dash {
0% {
  stroke-dasharray: 1, 150;
  stroke-dashoffset: 0;
}
50% {
  stroke-dasharray: 90, 150;
  stroke-dashoffset: -35;
}
100% {
  stroke-dasharray: 90, 150;
  stroke-dashoffset: -124;
}
}
