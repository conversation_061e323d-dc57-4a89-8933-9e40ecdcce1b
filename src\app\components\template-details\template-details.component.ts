import { Component, OnInit, inject } from '@angular/core';
import { CommonModule } from '@angular/common';
import { ActivatedRoute, Router, RouterModule } from '@angular/router';
import { ButtonStandardComponent } from '../button-standard/button-standard.component';
import { ButtonTwoClickComponent } from '../button-two-click/button-two-click.component';
import { PopoverComponent } from '../popover/popover.component';
import { Template } from '../../interfaces/template';
import { TemplatesService } from '../../services/templates.service';
import { fadeScaleInOut, fadeInOut } from '../../modules/animations/animations.module';
import { PopupService } from '../../services/popup.service';
import { TemplateFormService } from '../../services/template-form.service';

@Component({
  selector: 'template-details',
  standalone: true,
  imports: [
    ButtonStandardComponent,
    ButtonTwoClickComponent,
    PopoverComponent,
    CommonModule,
    RouterModule
  ],
  templateUrl: './template-details.component.html',
  styleUrl: './template-details.component.scss',
  animations: [fadeScaleInOut, fadeInOut]
})
export class TemplateDetailsComponent implements OnInit {
  route = inject(ActivatedRoute);
  router = inject(Router);
  templatesService = inject(TemplatesService);
  popupService = inject(PopupService);
  templateFormService = inject(TemplateFormService);

  template: Template | null = null;
  showActionsPopover = false;
  questions: any[] = [];
  isLoadingQuestions = false;
  questionsError: string | null = null;

  constructor() {}

  ngOnInit(): void {
    this.route.data.subscribe((data) => {
      if (data['template']) {
        this.template = data['template'].templates.find((template: any) => template.id === this.route.snapshot.params['id']);

        console.log('Template data:', this.template);

        // Load questions if they exist
        if (this.template && this.template['steps'] && this.template['steps'].length > 0) {
          this.loadQuestions(this.template['steps']);
        }
      }
    });
  }

  /**
   * Loads question data for all question IDs in the template
   * @param questionIds Array of question IDs to load
   */
  async loadQuestions(questionIds: string[]): Promise<void> {
    try {
      this.isLoadingQuestions = true;
      this.questionsError = null;

      // Create an array of promises for each question
      const questionPromises = questionIds.map(questionId =>
       questionId!=='test-choose-multiple' && this.templatesService.getQuestionByIdAsync(questionId)
      );

      // Wait for all promises to resolve
      this.questions = await Promise.all(questionPromises);
    } catch (error) {
      console.error('Error loading questions:', error);
      this.questionsError = 'Failed to load questions. Please try again.';
      this.questions = [];
    } finally {
      this.isLoadingQuestions = false;
    }
  }

  toggleActionsPopover(): void {
    this.showActionsPopover = !this.showActionsPopover;
  }

  getDateFromSeconds(seconds: number): string {
    const date = new Date(seconds * 1000);
    if (date.getFullYear() > 4000) {
      return "Ongoing";
    }
    return date.toLocaleDateString();
  }

  goBack(): void {
    this.router.navigate(['/templates']);
  }

  deleteTemplate(){
    this.templatesService.removePanelTemplate(this.template?.id || '').subscribe((response: any) => {
      if (response.message === 'Panel template deleted successfully') {
        console.log('Template deleted successfully');
        this.router.navigate(['/templates']);
      }
    });
  }
  duplicateTemplate(){
    console.log('duplicateTemplate');
    let {id:_,template_id:__,created:___, ...template} = this.template || {};
    console.log(template);
    this.templatesService.createPanelTemplate({...template, name: `${this.template?.name} (copy)`}).subscribe((response: any) => {
      if (response.message === 'Panel template created successfully') {
        console.log('Template duplicated successfully');
        this.router.navigate(['/templates']);
      }
    });
  }

  useTemplateToCreatePanel(){
    this.popupService.openNewPanel('Use a Template', this.template);
    this.showActionsPopover=false;
  }

  editTemplate(){
    let updated =this.popupService.editTemplate(this.template || {}, 5);
    console.log(updated);
    // updated.then(res=>{
    //   this.template = (res);
    // });
    this.showActionsPopover = false;
  }
}


