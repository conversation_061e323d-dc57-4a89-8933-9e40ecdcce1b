import { Injectable, inject } from '@angular/core';
import { FormGroup, FormControl, FormArray } from '@angular/forms';
import { ConstantsService } from './constants.service';
import { PanelsService } from './panels.service';
import { ApiService } from './api.service';
import { async, firstValueFrom, Observable } from 'rxjs';
import { ToastService } from './toast.service';
import { EnvironmentService } from './environment.service';
import { ProfileService } from './profile.service';
import { HttpHeaders, HttpParams } from '@angular/common/http';
import { QuestionAdapter } from '../adapters/question.adapter';

@Injectable({
  providedIn: 'root'
})
export class QuestionsService {

  constantsService = inject(ConstantsService);
  panelsService = inject(PanelsService);
  apiService = inject(ApiService);
  toastService = inject(ToastService);
  environmentService = inject(EnvironmentService);
  profileService = inject(ProfileService);
  editing: boolean = false;
  addingExistingStep: boolean = false;
  stepKey: string = '';
  private originalStepData: any = null;
  stepForm: FormGroup = new FormGroup({
    name: new FormControl(''),
    stepInstructions: new FormControl(''),
    stepType: new FormControl('range-slider'),
    required: new FormControl(true),
    options: new FormArray([
      this.optionForm
    ]),
  })

  get optionForm(): FormGroup {
    return new FormGroup({
      label: new FormControl(''),
      instructions: new FormControl(''),
      sliderLeft: new FormControl(''),
      sliderCenter: new FormControl(''),
      sliderRight: new FormControl(''),
      id: new FormControl(''),
    })
  }

  stepTypeOptionsObject: any = {
    'range-slider': {
      name: 'Range Slider',
      description: 'A range slider to collect quantitative data.',
      key: 'range-slider'
    },
    'true-false': {
      name: 'True/False',
      description: 'Ask your panelists if a question is true or false.',
      key: 'true-false'
    },
    'free-form-text': {
      name: 'Free Form Text',
      description: 'An open text field for detailed, written input.',
      key: 'free-form-text'
    },
    'sensory-spectrum': {
      name: 'Sensory Spectrum',
      description: 'A novel way to measure sensory feedback.',
      key: 'sensory-spectrum'
    },
    'multiple-choice': {
      name: 'Multiple Choice',
      description: 'Select multiple answers from a list of options.',
      key: 'multiple-choice'
    },
    'select-one': {
      name: 'Select One',
      description: 'A single-choice question to pick one option from several.',
      key: 'select-one'
    }
  }
  
  stepTypeOptions: any[] = [
    {
      name: 'Range Slider',
      description: 'A range slider to collect quantitative data.',
      key: 'range-slider'
    },
    {
      name: 'True/False',
      description: 'Ask your panelists if a question is true or false.',
      key: 'true-false'
    },
    {
      name: 'Free Form Text',
      description: 'An open text field for detailed, written input.',
      key: 'free-form-text'
    },
    {
      name: 'Sensory Spectrum',
      description: 'A novel way to measure sensory feedback.',
      key: 'sensory-spectrum'
    },
    {
      name: 'Multiple Choice',
      description: 'Select multiple answers from a list of options.',
      key: 'multiple-choice'
    },
    {
      name: 'Select One',
      description: 'A single-choice question to pick one option from several.',
      key: 'select-one'
    }    
  ]

  defaultQuestions: any[] = this.constantsService.questions;
  defaultQuestionsObject: any = this.constantsService.questionsObject;
  customQuestions: any[] = [];
  customQuestionsObject: any = {};
  adaptedQuestions: any[] = [];
  adaptedQuestionsObject: any = {};
  questionAdapter = new QuestionAdapter();

  constructor() {
    // this.stepForm.valueChanges.subscribe((value: any) => {
    //   console.log(value);
    // })
  }

  get questionsArray(): FormArray {
    return this.stepForm.get('questions') as FormArray;
  }

  private getHeaders() {
    return new HttpHeaders({
      'Content-Type': 'application/json'
    });
  }

  /**
   * Get all questions for the user's organization
   * @param organizationId Optional organization ID to filter questions
   * @param includeOptions Whether to include detailed option data
   * @returns Observable with array of questions
   */
  getQuestions(organizationId?: string, includeOptions: boolean = false): Observable<any[]> {
    let params = new HttpParams();
    
    if (organizationId) {
      params = params.set('organization', organizationId);
    }
    
    params = params.set('includeOptions', includeOptions.toString());
    
    return this.apiService.http.get<any[]>(
      `${this.environmentService.getBaseUrl()}/questions`,
      {
        headers: this.getHeaders(),
        params,
        withCredentials: true
      }
    );
  }

  /**
   * Get a specific question by ID
   * @param questionId ID of the question to retrieve
   * @param includeOptions Whether to include detailed option data
   * @returns Observable with the question data
   */
  getQuestion(questionId: string, includeOptions: boolean = true): Observable<any> {
    const params = new HttpParams().set('includeOptions', includeOptions.toString());
    
    return this.apiService.http.get<any>(
      `${this.environmentService.getBaseUrl()}/questions/${questionId}`,
      {
        headers: this.getHeaders(),
        params,
        withCredentials: true
      }
    );
  }

  /**
   * Delete a question
   * @param questionId ID of the question to delete
   * @returns Observable with the deletion response
   */
  deleteQuestion(questionId: string): Observable<any> {
    return this.apiService.http.delete(
      `${this.environmentService.getBaseUrl()}/questions/${questionId}`,
      {
        headers: this.getHeaders(),
        withCredentials: true
      }
    );
  }

  /**
   * Load a question for editing
   * @param questionId ID of the question to edit
   */
  async loadQuestionForEditing(questionId: string): Promise<void> {
    try {
      this.resetForm();
      this.editing = true;
      this.stepKey = questionId;
      const adaptedQuestion = this.adaptedQuestionsObject[questionId];
      console.log('adaptedQuestion', adaptedQuestion);
      
      let question: any;
      // if(questionId in this.constantsService.questionsObject && this.constantsService.questionsObject[questionId].isDefault){
      //   console.log('question is default');
        
      //   question = this.constantsService.questionsObject[questionId];
      // }
      // else{
      //   question = await questionAdapter.adapt(await firstValueFrom(this.getQuestion(questionId)));
      // }
      if(adaptedQuestion.isDefault){
        question = adaptedQuestion;
      }
      else{
        question = await this.questionAdapter.customToPanelStep(await firstValueFrom(this.getQuestion(questionId)));        
      }
      console.log('questionX', question);
            
      // Populate form with question data
      this.stepForm.patchValue({
        name: question.name,
        stepInstructions: question.instructions,
        stepType: question.type,
        required: question.required || false
      });
      
      // Handle options based on question type
      if (question.options && question.options.length > 0) {
        const optionsArray = this.stepForm.get('options') as FormArray;
        optionsArray.clear();
        
        console.log('question.options', question.options);
        
        question.options.forEach(async (option: any) => {
          if(typeof option === 'string'){
            option = await this.questionAdapter.adaptOption(this.constantsService.optionsObject[option]);
          }
          console.log('option', option);
          
          const optionForm = new FormGroup({
            label: new FormControl(option.label || ''),
            instructions: new FormControl(option.instructions || ''),
            sliderLeft: new FormControl(option.sliderLeft || ''),
            sliderCenter: new FormControl(option.sliderCenter || ''),
            sliderRight: new FormControl(option.sliderRight || '')
          });
          
          optionsArray.push(optionForm);
        });
      }
      
      this.stepForm.disable();
      // this.originalStepData = structuredClone(this.stepForm.value); // Deep copy for later comparison
    } catch (error) {
      console.error('Error loading question for editing:', error);
      this.toastService.burntToast('Failed to load question data');
      this.resetForm();
    }
  }

  resetForm() {
    this.stepForm.reset({
      name: '',
      stepInstructions: '',
      stepType: 'range-slider',
      required: false
    });
    
    const optionsArray = this.stepForm.get('options') as FormArray;
    optionsArray.clear();
    optionsArray.push(this.optionForm);
    
    this.editing = false;
    this.stepForm.enable();
    this.stepKey = '';
  }

  populateSliderOptions(stepData: any){
    let optionsArray: FormArray = this.stepForm.get('options') as FormArray;
    optionsArray.clear();
    stepData['options'].forEach((optionKey: string) => {
      const optionData = this.constantsService.metricsObject[optionKey];
      let optionForm = this.optionForm;
      optionForm.controls['label'].setValue(optionData['name']);
      optionForm.controls['instructions'].setValue(optionData['instructions']);
      optionForm.controls['sliderLeft'].setValue(optionData['labels'][0]);
      optionForm.controls['sliderRight'].setValue(optionData['labels'].at(-1));
      if(optionData['labels'].length > 2){
        optionForm.controls['sliderCenter'].setValue(optionData['labels'][1]);
      }
      // optionForm.controls['id'].setValue(optionKey);
      optionsArray.push(optionForm);
    })
  }

  populateFormFromStepData(){
    if(this.stepKey in this.constantsService.questionsObject){
      const stepData = this.constantsService.questionsObject[this.stepKey];
      this.originalStepData = JSON.parse(JSON.stringify(stepData)); // Deep copy for later comparison
      this.stepForm.controls['name'].setValue(stepData.name);   
      this.stepForm.controls['stepType'].setValue(stepData['type']);
      this.stepForm.controls['stepInstructions'].setValue(stepData['instructions'])

      if(stepData['type'] === 'range-slider'){
        this.populateSliderOptions(stepData);
      }
    }
  }

  async updateQuestion(payload: any){
    try{
      let response = await firstValueFrom(this.apiService.http.put(
        `${this.environmentService.getBaseUrl()}/questions/${this.stepKey}`,
        payload,
        { headers: this.getHeaders(), withCredentials: true }
      ));
      this.toastService.goodToast('Question updated successfully');
      this.resetForm();
      return response;
    } catch (error) {
      console.error('Error updating question:', error);
      this.toastService.burntToast('Failed to update question');
      throw error;
    }
  }

  async createQuestion(payload: any){
    console.log('payload', payload);
    
    try{
      let response = await firstValueFrom(this.apiService.http.post(
        `${this.environmentService.getBaseUrl()}/questions`,
        payload,
        { headers: this.getHeaders(), withCredentials: true }
      ));
      // const response = await firstValueFrom(this.apiService.createQuestion(payload));
      console.log('response', response);
      this.toastService.goodToast('Question created successfully');
      this.resetForm();
      return response;
    } catch (error) {
      console.error('Error creating question:', error);
      this.toastService.burntToast('Failed to create question');
      throw error;
    }
  }

  async checkQuestionBeforeSave(){
    const formData = this.stepForm.value;
    const questionsArray = this.panelsService.step3Form.get('questions') as FormArray;
    let response: any;

    // Validate form data
    if (!formData.name) {
      this.toastService.burntToast('Please provide a name for the question');
      throw new Error('Please provide a name for the question');
    }

    if (!formData.stepType) {
      this.toastService.burntToast('Please select a question type');
      throw new Error('Please select a question type');
    }

    // Create payload for API
    const payload = {
      organization: this.profileService.profileData['organizations'][0],
      name: formData.name,
      stepInstructions: formData.stepInstructions === '' ? 'empty' : formData.stepInstructions,
      stepType: formData.stepType,
      required: formData.required || false,
      options: ['sensory-spectrum', 'free-form-text', 'true-false'].includes(formData.stepType) ? [] : formData.options.map((option:any) => {
        delete option['id'];
        const retOption = {
          label: option['label'],
          instructions: option['instructions'] === '' ? 'empty' :option['instructions'],
          sliderLeft: option['sliderLeft'] === '' ? 'empty' : option['sliderLeft'],
          sliderRight: option['sliderRight'] === '' ? 'empty' : option['sliderRight'],
          sliderCenter: option['sliderCenter'] === '' ? 'empty' : option['sliderCenter']
        }
        return retOption;
      })
    };

    response = await this.createQuestion(payload);
    this.customQuestions.push(response);
    this.customQuestionsObject[response['id']] = response;
    const adaptedResponse = await this.questionAdapter.customToPanelStep(response);
    console.log('adaptedResponse', adaptedResponse);
    
    this.adaptedQuestions.push(adaptedResponse);
    this.adaptedQuestionsObject[adaptedResponse['id']] = adaptedResponse;
    this.addStep(adaptedResponse['id']);
    return response;
  }

  addStep(stepKey: string){
    const questionsArray = this.panelsService.step3Form.get('questions') as FormArray;
    questionsArray.push(new FormControl(stepKey));

  }
}
