import { inject } from '@angular/core';
import { ResolveFn } from '@angular/router';
import { TemplatesService } from '../services/templates.service';
import { LoadingService } from '../services/loading.service';
import { Template } from '../interfaces/template';
import { finalize, firstValueFrom } from 'rxjs';

export const templateDetailsResolver: ResolveFn<Template> = (route, state) => {
  const templatesService = inject(TemplatesService);
  const loadingService = inject(LoadingService);
  const templateId = route.paramMap.get('id');

  if (!templateId) {
    return Promise.resolve({} as Template);
  }

  loadingService.setLoading('templates', true);

  return firstValueFrom(templatesService.templates$.pipe(
    finalize(() => loadingService.setLoading('templates', false))
  ));
};
