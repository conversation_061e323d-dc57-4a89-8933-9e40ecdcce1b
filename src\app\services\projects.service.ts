import { Injectable, inject } from '@angular/core';
import { FormBuilder, FormControl, Validators, FormGroup, FormArray } from '@angular/forms';
import { ProjectInterface } from '../interfaces/project.interface';
import { switchMap, map, BehaviorSubject } from 'rxjs';
import { PaginationParams } from '../interfaces/paginationParams';
import { ApiService } from './api.service';

@Injectable({
  providedIn: 'root'
})
export class ProjectsService {

  projectsArray: ProjectInterface[] = [];
  summaryMetrics: any = {
    openProjects: 0,
    endingSoon: 0,
    completed: 0,
    productTypeDistribution: []
  };
  editing: boolean = false;
  fb = inject(FormBuilder);
  apiService = inject(ApiService);
  form: FormGroup = this.fb.group({
    projectName: new FormControl('', Validators.required),
    description: new FormControl('', Validators.required),
    startOption: new FormControl('now', Validators.required),
    startDate: new FormControl(''),
    endOption: new FormControl('never', Validators.required),
    endDate: new FormControl(''),
    collaborators: new FormControl([]),
    privacy: new FormControl('private'),
  })

  selectedPanelArray: any[] = [];
  selectedGroupsArray: any[] = [];
  collaboratorsArray: any[] = [];

  formStep: number = 1;
  totalSteps: number = 1;
  nextStepFlavorTextArr: string[] = ['All done?'];

  hasError = false;
  errorMessage: string = '';
  formErrors: {[key: string]: boolean} = {
    projectName: false,
    description: false,
    startDate: false,
    endDate: false,
  }

  
  pageSubject = new BehaviorSubject<number>(1);
  limitSubject = new BehaviorSubject<number>(10);

  public projects$ = this.createProjectsStream();

  projects: any[] = [];
  projectsObject: { [key: string]: any } = {};

  page: number = 1;

  constructor() { }

  filter(event: any) {
    console.log(event);
  }

  search(event: any) {
    console.log(event);
  }

  clearForm(){
    // Reset the form with default values
    this.form.reset({
      projectName: '',
      description: '',
      startOption: 'now',
      startDate: '',
      endOption: 'never',
      endDate: '',
      collaborators: [],
      privacy: 'private'
    });
    
    this.selectedPanelArray = [];
    this.selectedGroupsArray = [];
    this.collaboratorsArray = [];
    this.formErrors = {
      projectName: false,
      description: false,
      startDate: false,
      endDate: false,
    };
  }

  setErrorMessage(){
    this.errorMessage = 'Please fill out all required fields';
  }

  save(){
    // try{
      // Mark form as touched to trigger validation
      Object.keys(this.form.controls).forEach(key => {
        this.form.get(key)?.markAsTouched();
      });

      // Log detailed form information
      console.log('Form valid status:', this.form.valid);
      console.log('Form values:', this.form.value);
      console.log('Form dirty status:', this.form.dirty);
      console.log('Form touched status:', this.form.touched);
      
      // Log each control's validation state
      Object.keys(this.form.controls).forEach(key => {
        const control = this.form.get(key);
        console.log(`Control [${key}]:`, {
          value: control?.value,
          valid: control?.valid,
          dirty: control?.dirty,
          touched: control?.touched,
          errors: control?.errors
        });
      });

      // Reset form errors
      this.formErrors = {
        projectName: false,
        description: false,
        startDate: false,
        endDate: false,
      };


      // Check for project name error
      this.formErrors['projectName'] = !this.form.get('projectName')?.value;

      // Check for description error
      this.formErrors['description'] = !this.form.get('description')?.value;

      // Check for start date error only if startOption is 'custom'
      if (this.form.get('startOption')?.value === 'custom') {
        this.formErrors['startDate'] = !this.form.get('startDate')?.value;
      }

      // Check for end date error only if endOption is 'custom'
      if (this.form.get('endOption')?.value === 'custom') {
        this.formErrors['endDate'] = !this.form.get('endDate')?.value;
      }

      console.log('Form errors:', this.formErrors);

      // Check if there are any errors
      if (Object.values(this.formErrors).some(error => error)) {
        console.error('Form has errors', this.formErrors);
        this.hasError = true;
        this.setErrorMessage();
        throw new Error('Form has errors');
      }

      // Calculate a default end date (100 years from now) if "never" is selected
      const defaultEndDate = new Date();
      defaultEndDate.setFullYear(defaultEndDate.getFullYear() + 100);

      // Prepare data for API
      const projectData: any = {
        name: this.form.get('projectName')?.value,
        description: this.form.get('description')?.value || '',
        organization_id: localStorage.getItem('currentOrgId'),
        status: 'draft',
        start_date: this.form.get('startOption')?.value === 'now' ? new Date().toISOString() : this.form.get('startDate')?.value,
        end_date: this.form.get('endOption')?.value === 'never' ? defaultEndDate.toISOString() : this.form.get('endDate')?.value,
        is_public: this.form.get('privacy')?.value === 'public'
      };

      console.log('Project data before adding related items:', projectData);

      // Add panels if selected
      if (this.selectedPanelArray.length > 0) {
        // Log each panel to debug
        this.selectedPanelArray.forEach((panel, index) => {
          console.log(`Panel ${index}:`, panel);
          console.log(`Panel ${index} ID properties:`, {
            panel_id: panel.panel_id,
            panelId: panel.panelId,
            id: panel.id
          });
        });

        // Map to panel IDs and filter out any null/undefined values
        projectData.assigned_panels = this.selectedPanelArray
          .map(panel => {
            // Try multiple potential ID properties
            const panelId = panel.panel_id || panel.panelId || panel.id;
            console.log('Extracted panel ID:', panelId);
            return panelId;
          })
          .filter(id => id !== null && id !== undefined);
        
        console.log('Selected panels:', this.selectedPanelArray);
        console.log('Panel IDs:', projectData.assigned_panels);
      }

      // Add groups if selected
      if (this.selectedGroupsArray.length > 0) {
        // Create assigned_groups structure with user_groups, panel_groups, sample_groups
        projectData.assigned_groups = {
          user_groups: [],
          panel_groups: [],
          sample_groups: []
        };
        
        // Process each group based on its type and add to the appropriate array
        this.selectedGroupsArray.forEach(group => {
          const groupId = group.group_id || group.id;
          if (groupId) {
            // For now, treat all groups as panel groups - in a real app, 
            // you would determine the group type from its data
            projectData.assigned_groups.panel_groups.push(groupId);
          }
        });
        
        console.log('Selected groups:', this.selectedGroupsArray);
        console.log('Group structure:', projectData.assigned_groups);
      }

      // Add samples/products from panels
      if (this.selectedPanelArray.length > 0) {
        // Log each panel's product info
        this.selectedPanelArray.forEach((panel, index) => {
          console.log(`Panel ${index} product properties:`, {
            product_id: panel.product_id,
            productId: panel.productId,
            sample_id: panel.sample_id
          });
        });

        const products = this.selectedPanelArray
          .filter(panel => panel.product_id || panel.productId || panel.sample_id)
          .map(panel => {
            // Try multiple potential ID properties
            const productId = panel.product_id || panel.productId || panel.sample_id;
            console.log('Extracted product ID:', productId);
            return productId;
          })
          .filter(id => id !== null && id !== undefined);
        
        if (products.length > 0) {
          projectData.assigned_samples = [...new Set(products)]; // Remove duplicates
          console.log('Product IDs:', projectData.assigned_samples);
        }
      }

      // Add users/collaborators if selected
      if (this.collaboratorsArray.length > 0) {
        // Extract user IDs - structure depends on your implementation
        projectData.collaborators = this.collaboratorsArray
          .map(user => user.uid || user.id)
          .filter(id => id !== null && id !== undefined);
        
        console.log('Selected collaborators:', this.collaboratorsArray);
        console.log('Collaborator IDs:', projectData.collaborators);
      }

      console.log('Final project data to be submitted:', projectData);
      // throw new Error('test');


      // Create project using Promise rather than Observable
      this.apiService.createProject(projectData).then(
        (observable) => {
          observable.subscribe({
            next: (response: any) => {
              console.log('Project created successfully', response);
              // Reset form and arrays
              this.clearForm();
              return response;
            },
            error: (error: any) => {
              console.error('Error creating project', error);
              this.hasError = true;
              this.errorMessage = error.message || 'Failed to create project';
              throw error;
            }
          });
        }
      );
    // } catch(error){
    //   console.log('error', error);
    //   return error;
    // }
  }

  getProjects(params: PaginationParams){
    return this.apiService.getProjects(params).pipe(
      map((response: any) => {
        console.log('response', response);
        
        this.projects = response.projects;
        this.projectsObject = response.projects.reduce((acc: { [key: string]: any }, project: any) => {
          acc[project.id] = project;
          return acc;
        }, {});
        return response;
      })
    );
  }

  getProjectById(id:string, hydrateReferences: boolean = false, hydrateFullDetails: boolean = false){
    return this.apiService.getProjectById(id, hydrateReferences, hydrateFullDetails);
  }

  private createProjectsStream() {
    return this.pageSubject.pipe(
      switchMap(page => 
        this.getProjects({ 
          page, 
          limit: this.limitSubject.getValue() 
        })
      )
    );
  }

  public setPage(page: number) {
    this.page = page;
    this.pageSubject.next(page);
  }

  public setLimit(limit: number) {
    this.limitSubject.next(limit);
  }
}
