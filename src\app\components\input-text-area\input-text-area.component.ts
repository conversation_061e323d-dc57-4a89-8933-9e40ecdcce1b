import { Component, Input } from '@angular/core';
import { FormControl, ReactiveFormsModule } from '@angular/forms';

@Component({
  selector: 'input-text-area',
  standalone: true,
  imports: [ReactiveFormsModule],
  templateUrl: './input-text-area.component.html',
  styleUrl: './input-text-area.component.scss'
})
export class InputTextAreaComponent {

  @Input() control!: FormControl;
  @Input() id: string = '';
  @Input() placeholder: string = '';
}
