<page-header [buttonClass]="'green'" [pageName]="'Projects'" [infoVarient]="1" [info]="'info'" (onFilter)="projectsService.filter($event)" (onSearch)="projectsService.search($event)" (onAdd)="popupService.openNewProject($event)" [addText]="'New Project'">
    <svg width="34" height="42" viewBox="0 0 34 42" fill="none" xmlns="http://www.w3.org/2000/svg">
        <path d="M14.2951 0H32L19.7049 42H2L14.2951 0Z" fill="#E6FFE9"/>
        <path d="M31.4047 16H16.7709L19.3662 21L16.7709 26H31.4047L34 21L31.4047 16ZM30.9982 25.3181H17.8919L19.9735 21.3317L20.1226 21L19.9559 20.6751L17.8743 16.6887L31.0024 16.6901L33.2372 20.9998L30.9982 25.3181Z" fill="#292727"/>
        <path d="M14.6338 16H0L2.59526 21L0 26H14.6338L17.2291 21L14.6338 16ZM14.2245 25.3101H1.12071L3.20233 21.3237L3.36903 20.9988L3.20233 20.6739L1.12071 16.6875L14.2245 16.6902L16.466 21L14.2245 25.3101Z" fill="#292727"/>
    </svg>        
</page-header>

<div class="projects-summary-metrics-container">
    <div class="projects-summary-metric open-projects">
        <div class="projects-summary-metric-label">Open Projects</div>
        <div class="projects-summary-metric-value">{{projectsService.summaryMetrics.openProjects}}</div>
    </div>
    <div class="projects-summary-metric ending-soon">
        <div class="projects-summary-metric-label">Ending Soon</div>
        <div class="projects-summary-metric-value">{{projectsService.summaryMetrics.endingSoon}}</div>
    </div>
    <div class="projects-summary-metric completed">
        <div class="projects-summary-metric-label">Completed</div>
        <div class="projects-summary-metric-value">{{projectsService.summaryMetrics.completed}}</div>
    </div>
    <div class="percentage-bar-container">
        <div class="percentage-bar-label">Product Type Distribution</div>
        <lamb-graph-tooltip 
            *ngIf="activeTooltipItem"
            [style.position]="'absolute'"
            [style.left.px]="tooltipPosition.x"
            [style.bottom.px]="tooltipPosition.y"
            [style.top]="'auto'"
            [config]="{
                mainText: {content: activeTooltipItem?.name + ' ' + activeTooltipItem?.value + '%' || 'Unknown'},
            }"
            [@fadeInOut]>
        </lamb-graph-tooltip>

        <percentage-bar 
            #percentageBar
            [style.--bar-padding]="'10px'" 
            [style.--bar-height]="'35px'" 
            [pieces]="projectsService.summaryMetrics.productTypeDistribution"
            (mouseover)="showTooltip($event)"
            (mouseleave)="hideTooltip()">
        </percentage-bar>
    </div>
</div>

<div class="table-container">
    <ng-container *ngIf="projects$ | async as projects; else loading">
        <lamb-table *ngIf="projectsService.projectsArray.length" [styles]="tableStyles" [headers]="tableHeaders" [data]="projectsService.projectsArray" [clickableRows]="true" (rowClick)="onRowClick($event)"></lamb-table>
    </ng-container>
    <ng-template #loading>
        <div *ngIf="!projectsService.projectsArray.length">No projects found</div>
    </ng-template>
</div>

<pagination [state]="paginationState$ | async" (pageChange)="onPageChange($event)"></pagination>

<ng-template #statusCellTemplate let-project>
    <div class="status-cell">
        <div class="color-dot" [style.background-color]="getColorFromStatus(project.status)"></div>
        <div class="status-text">{{project.status}}</div>
    </div>
</ng-template>

<ng-template #ownerCellTemplate let-project>
    <div class="owner-cell">
        <lamb-gp-icon [userInitials]="project.ownerInitials"></lamb-gp-icon>
        <div class="owner-text">{{project.owner}}</div>
    </div>
</ng-template>