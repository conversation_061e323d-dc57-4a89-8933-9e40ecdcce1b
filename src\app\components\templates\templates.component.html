<page-header
  [pageName]="'Sensory Panels'"
  [url]="this.router.url"
  [infoVarient]="3"
  [info]="panelsService.panelsArray.length"
  (onFilter)="panelsService.filter($event)"
  (onSearch)="panelsService.search($event)"
  (onAdd)="popupService.openNewTemplate($event)"
  [addText]="'New Template'"
>
  <svg
    width="30"
    height="42"
    viewBox="0 0 30 42"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path d="M12.2951 0H30L17.7049 42H0L12.2951 0Z" fill="#E6F3FF" />
    <path
      d="M24.9266 14.4616C24.1212 15.1305 23.3572 15.7145 21.7879 15.7145C20.2185 15.7145 19.4545 15.0881 18.6492 14.4616C17.8438 13.8352 16.8246 13 15 13C13.1754 13 12.1993 13.752 11.3508 14.4616C10.5024 15.1713 9.78148 15.7145 8.21212 15.7145C6.64276 15.7145 5.87879 15.0881 5.07341 14.4616C4.26802 13.8352 3.24882 13 1.42424 13H1V13.8352H1.42424C2.95219 13.8352 3.75758 14.4616 4.56296 15.0881C5.36834 15.7145 6.38754 16.5497 8.21212 16.5497C10.0367 16.5497 11.0128 15.7977 11.8613 15.0881C12.7098 14.3785 13.4306 13.8352 15 13.8352C16.5694 13.8352 17.3333 14.4616 18.1387 15.0881C18.9441 15.7145 19.9633 16.5497 21.7879 16.5497C23.6125 16.5497 24.5886 15.7977 25.437 15.0881C26.2855 14.3785 27.0064 13.8352 28.5758 13.8352H29V13H28.5758C26.7512 13 25.7751 13.7944 24.9266 14.4616Z"
      fill="black"
    />
    <path
      d="M24.9266 21.6868C24.1212 22.3556 23.3572 22.9396 21.7879 22.9396C20.2185 22.9396 19.4545 22.3132 18.6492 21.6868C17.8438 21.0604 16.8246 20.2251 15 20.2251C13.1754 20.2251 12.1993 20.9772 11.3508 21.6868C10.5024 22.3964 9.78148 22.9396 8.21212 22.9396C6.64276 22.9396 5.87879 22.3132 5.07341 21.6868C4.26802 21.0604 3.24882 20.2251 1.42424 20.2251H1V21.0604H1.42424C2.95219 21.0604 3.75758 21.6868 4.56296 22.3132C5.36834 22.9396 6.38754 23.7749 8.21212 23.7749C10.0367 23.7749 11.0128 23.0228 11.8613 22.3132C12.7098 21.6036 13.4306 21.0604 15 21.0604C16.5694 21.0604 17.3333 21.6868 18.1387 22.3132C18.9441 22.9396 19.9633 23.7749 21.7879 23.7749C23.6125 23.7749 24.5886 23.0228 25.437 22.3132C26.2855 21.6036 27.0064 21.0604 28.5758 21.0604H29V20.2251H28.5758C26.7512 20.2676 25.7751 21.0196 24.9266 21.6868Z"
      fill="black"
    />
    <path
      d="M24.9266 28.9119C24.1212 29.5808 23.3572 30.1648 21.7879 30.1648C20.2185 30.1648 19.4545 29.5384 18.6492 28.9119C17.8438 28.2855 16.8246 27.4503 15 27.4503C13.1754 27.4503 12.1993 28.2023 11.3508 28.9119C10.5024 29.6215 9.78148 30.1648 8.21212 30.1648C6.64276 30.1648 5.87879 29.5384 5.07341 28.9119C4.26802 28.2855 3.24882 27.4503 1.42424 27.4503H1V28.2855H1.42424C2.95219 28.2855 3.75758 28.9119 4.56296 29.5384C5.36834 30.1648 6.38754 31 8.21212 31C10.0367 31 11.0128 30.248 11.8613 29.5384C12.7098 28.8287 13.4306 28.2855 15 28.2855C16.5694 28.2855 17.3333 28.9119 18.1387 29.5384C18.9441 30.1648 19.9633 31 21.7879 31C23.6125 31 24.5886 30.248 25.437 29.5384C26.2855 28.8287 27.0064 28.2855 28.5758 28.2855H29V27.4503H28.5758C26.7512 27.4927 25.7751 28.2431 24.9266 28.9119Z"
      fill="black"
    />
  </svg>
</page-header>

<div class="table-container">
  <ng-container *ngIf="templates$ | async as templates; else loading">
    <lamb-table
      [styles]="tableStyles"
      [headers]="tableHeaders"
      [data]="data"
      [clickableRows]="true"
      (rowClick)="onRowClick($event)"
    >
</lamb-table>
<popover
  *ngIf="showActionsPopover"
  (clickOutside)="showActionsPopover = false"
  [@fadeInOut]

  [ngStyle]="{
    'position': 'absolute',
    'top.px': popoverPosition.top,
    'right.px': '50px',
    'mstgin-right': '50px',

    'z-index': 1000
  }"
>
  <div class="popover-item" (click)="editTemplate(currentTemplateId)">
    Edit Template
  </div>
  <div
    class="popover-item"
    (click)="duplicateTemplate(currentTemplateId)"
  >
    Duplicate Template
  </div>
  <div
    class="popover-item"
    (click)="useTemplateToCreatePanel(currentTemplateId)"
  >
    Create Panel from Template
  </div>
  <div
    class="popover-item delete"
    (click)="deleteTemplate(currentTemplateId)"
  >
    Delete Template
  </div>
</popover>
  </ng-container>
  <ng-template #loading>
    <div *ngIf="!data?.length">Loading templates...</div>
  </ng-template>
  <pagination
    [state]="paginationState$ | async"
    (pageChange)="onPageChange($event)"
  ></pagination>
</div>

<ng-template #templateOptions let-template>
  <button-standard (click)="onOptionsClick($event, template)">
    <svg
      width="16"
      height="4"
      viewBox="0 0 16 4"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M9.68421 1.77778C9.68421 2.75956 8.93011 3.55555 8 3.55555C7.06989 3.55555 6.31579 2.75956 6.31579 1.77778C6.31579 0.796 7.06989 0 8 0C8.93011 0 9.68421 0.796 9.68421 1.77778Z"
        fill="#292727"
      />
      <path
        d="M3.36842 1.77778C3.36842 2.75956 2.61432 3.55555 1.68421 3.55555C0.754105 3.55555 0 2.75956 0 1.77778C0 0.796 0.754105 0 1.68421 0C2.61432 0 3.36842 0.796 3.36842 1.77778Z"
        fill="#292727"
      />
      <path
        d="M16 1.77778C16 2.75956 15.2459 3.55555 14.3158 3.55555C13.3857 3.55555 12.6316 2.75956 12.6316 1.77778C12.6316 0.796 13.3857 0 14.3158 0C15.2459 0 16 0.796 16 1.77778Z"
        fill="#292727"
      /></svg>
    </button-standard>
</ng-template>
