import { Component, EventEmitter, Output, inject, computed, input, TemplateRef, ViewChild, signal } from '@angular/core';
import { CommonModule } from '@angular/common';
import { LambTableComponent, HeaderCell } from '@lamb-sensory/lamb-component-library';
import { TABLE_STYLES } from '../../config/table-styles.config';
import { ConstantsService } from '../../services/constants.service';
import { PanelStep } from '../../interfaces/panel-step.interface';
import { FormsModule, FormControl, ReactiveFormsModule } from '@angular/forms';
import { InputTextComponent } from '../input-text/input-text.component';
import { QuestionsService } from '../../services/questions.service';

@Component({
  selector: 'custom-step-list-popover',
  standalone: true,
  imports: [CommonModule, LambTableComponent, FormsModule, ReactiveFormsModule, InputTextComponent],
  templateUrl: './custom-step-list-popover.component.html',
  styleUrl: './custom-step-list-popover.component.scss'
})
export class CustomStepListPopoverComponent {
  private constantsService = inject(ConstantsService);
  private questionsService = inject(QuestionsService);
  addedSteps = input<string[]>([]);
  questions = input<any[]>([]);

  searchControl = new FormControl('');

  // All steps as array
  // steps = computed(() => [...this.constantsService.questions, ...this.orgQuestions()]);

  // Table headers
  tableHeaders: HeaderCell[] = [
    { value: 'Name', key: 'name' },
    { value: 'Type', key: 'type' },
    { value: '', key: 'action' }
  ];

  // Filtered steps based on search
  get filteredSteps() {
    const term = this.searchControl.value?.toLowerCase() ?? '';
    console.log('questions', this.questions());
    return this.questions().filter((step: PanelStep) =>
      (step.name.toLowerCase().includes(term) || step.type.toLowerCase().includes(term))
        && !this.addedSteps().includes(step.id)
    );
  }

  @ViewChild('viewButtonTemplate', { static: true }) viewButtonTemplate!: TemplateRef<any>;

  // Table data for lamb-table
  get tableData() {
    return this.filteredSteps.map((step: PanelStep) => ({
      name: step.name,
      type: this.questionsService.stepTypeOptionsObject[step.type]?.name,
      action: 'view',
      action__template: this.viewButtonTemplate,
      action__templateContext: {
        $implicit: step
      },
      id: step.id,
      _step: step
    }));
  }

  @Output() stepSelected = new EventEmitter<PanelStep>();
  @Output() back = new EventEmitter<void>();
  @Output() viewStep = new EventEmitter<PanelStep>();

  onRowClick(event: any) {
    console.log(event);
    if (event?.cell?.data?._step) {
      this.stepSelected.emit(event.cell.data._step);
    }
  }

  tableStyles = structuredClone(TABLE_STYLES);

  constructor() {
    this.tableStyles.header!.style!.background = '#EAC6EB';
    this.tableStyles.header!.style!.color = '#6D116F';
    this.tableStyles.header!.style!.borderRadius = '18px';
    this.tableStyles.body!.row!.style!.borderRadius = '18px';
  }

} 