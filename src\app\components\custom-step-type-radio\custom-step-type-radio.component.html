<label class="step-type-container" *ngFor="let option of options; let i = index" [for]="'step-type-option-' + i">
    <input-radio-standard [id]="'step-type-option-' + i" [control]="control" [value]="option.key"></input-radio-standard>
    <div class="step-type-option">
        <div class="step-type-option-name">{{option.name}}</div>
        <div class="step-type-option-description">{{option.description}}</div>
        <!-- preview button will go here -->
    </div>
</label>
