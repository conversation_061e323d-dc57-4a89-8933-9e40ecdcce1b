import { inject } from '@angular/core';
import { ResolveFn } from '@angular/router';
import { GroupsService } from '../services/groups.service';
import { LoadingService } from '../services/loading.service';
import { Observable, of, forkJoin, from } from 'rxjs';
import { map, catchError, finalize, tap, switchMap } from 'rxjs/operators';

// Resolver for basic group data
export const groupsResolver: ResolveFn<any> = (route, state) => {
  const groupsService = inject(GroupsService);
  const loadingService = inject(LoadingService);
  const groupId = route.paramMap.get('id');

  if (!groupId) {
    return of(null);
  }

  loadingService.setLoading('groups', true);

  return from(groupsService.getGroupById(groupId)).pipe(
    tap(group => {
      if (group) {
        groupsService.setSelectedGroup(group);
      }
    }),
    catchError(error => {
      console.error('Error fetching group:', error);
      return of(null);
    }),
    finalize(() => loadingService.setLoading('groups', false))
  );
};

// Resolver for group panels
export const groupPanelsResolver: ResolveFn<any> = (route, state) => {
  const groupsService = inject(GroupsService);
  const loadingService = inject(LoadingService);
  const groupId = route.paramMap.get('id');

  if (!groupId) {
    return of([]);
  }

  loadingService.setLoading('groupPanels', true);

  const includeReferences = true;
  return from(groupsService.getGroupById(groupId, includeReferences)).pipe(
    switchMap(group => {
      if (!group || !group.panels?.length) {
        return of([]);
      }

      const panelRequests = group.panels.map((panelId: string) => 
        from(groupsService.apiService.getPanel(panelId)).pipe(
          catchError(error => {
            console.error(`Error fetching panel ${panelId}:`, error);
            return of(null);
          })
        )
      );

      return forkJoin(panelRequests).pipe(
        map((panels: any) => panels.filter((panel : any) => panel !== null))
      );
    }),
    catchError(error => {
      console.error('Error in group panels resolver:', error);
      return of([]);
    }),
    finalize(() => loadingService.setLoading('groupPanels', false))
  );
};

// Resolver for group samples
export const groupSamplesResolver: ResolveFn<any> = (route, state) => {
  const groupsService = inject(GroupsService);
  const loadingService = inject(LoadingService);
  const groupId = route.paramMap.get('id');

  if (!groupId) {
    return of([]);
  }

  loadingService.setLoading('groupSamples', true);

  return from(groupsService.getGroupById(groupId)).pipe(
    switchMap(group => {
      if (!group || !group.samples?.length) {
        return of([]);
      }

      const sampleRequests = group.samples.map((sampleId: string) => 
        from(groupsService.apiService.getProductDetails(sampleId)).pipe(
          catchError(error => {
            console.error(`Error fetching sample ${sampleId}:`, error);
            return of(null);
          })
        )
      );

      return forkJoin(sampleRequests).pipe(
        map((samples: any) => samples.filter((sample: any) => sample !== null))
      );
    }),
    catchError(error => {
      console.error('Error in group samples resolver:', error);
      return of([]);
    }),
    finalize(() => loadingService.setLoading('groupSamples', false))
  );
};

// Resolver for group users
export const groupUsersResolver: ResolveFn<any> = (route, state) => {
  const groupsService = inject(GroupsService);
  const loadingService = inject(LoadingService);
  const groupId = route.paramMap.get('id');

  if (!groupId) {
    return of([]);
  }

  loadingService.setLoading('groupUsers', true);

  return from(groupsService.getGroupById(groupId)).pipe(
    switchMap(group => {
      if (!group || !group.users?.length) {
        return of([]);
      }

      const userRequests = group.users.map((userId: string) => 
        from(groupsService.apiService.getUserDetails(userId)).pipe(
          catchError(error => {
            console.error(`Error fetching user ${userId}:`, error);
            return of(null);
          })
        )
      );

      return forkJoin(userRequests).pipe(
        map((users: any) => users.filter( (user: any) => user !== null))
      );
    }),
    catchError(error => {
      console.error('Error in group users resolver:', error);
      return of([]);
    }),
    finalize(() => loadingService.setLoading('groupUsers', false))
  );
};
