import { CommonModule } from '@angular/common';
import { Component, inject, TemplateRef, ViewChild } from '@angular/core';
import { ButtonStandardComponent } from '../button-standard/button-standard.component';
import { ActivatedRoute, Router, RouterModule } from '@angular/router';
import { GroupsService } from '../../services/groups.service';
import { PopoverComponent } from '../popover/popover.component';
import { PopupService } from '../../services/popup.service';
import { InputTextComponent } from '../input-text/input-text.component';
import { FormControl, FormGroup } from '@angular/forms';
import { TABLE_STYLES } from '../../config/table-styles.config';
import { LambTableComponent, HeaderCell, LambGPIconComponent, GraphTooltipComponent } from '@lamb-sensory/lamb-component-library';
import { ConstantsService } from '../../services/constants.service';
import { fadeInOut, fadeScaleInOut } from '../../modules/animations/animations.module';
@Component({
  selector: 'group-details',
  standalone: true,
  imports: [
    CommonModule,
    ButtonStandardComponent,
    RouterModule,
    PopoverComponent,
    InputTextComponent,
    LambTableComponent,
    LambGPIconComponent,
    GraphTooltipComponent
  ],
  templateUrl: './group-details.component.html',
  styleUrl: './group-details.component.scss',
  animations: [fadeInOut, fadeScaleInOut]
})
export class GroupDetailsComponent {
  route = inject(ActivatedRoute);
  groupsService = inject(GroupsService);
  showActionsPopover = false;
  popupService = inject(PopupService);
  router = inject(Router);
  constantsService = inject(ConstantsService);

  group: any;
  groupPanelsData:any
  groupSamplesData:any
  groupUsersData:any

  editGroupForm = new FormGroup({
    groupNameControl: new FormControl(''),
    groupDescriptionControl: new FormControl(''),
  })

  searchControl = new FormControl('');
  filterControl = new FormControl('');

  // panelsTableConfig!: TableConfig;
  panelsTableHeaders:HeaderCell[] = [
    {
      value: 'Status',
      key: 'status',
    },
    {
      value: 'Panel Name',
      key: 'name',
    },
    {
      value: 'Sample',
      key: 'sample',
    },
    {
      value: '# Responses',
      key: 'responses',
    },
    {
      value: '',
      key: 'results',
    }
  ];
  panelsTableData:any[] = [];
  panelsTableStyles = structuredClone(TABLE_STYLES);
  // samplesTableConfig!: TableConfig;
  samplesTableHeaders:HeaderCell[] = [
    {
      value: 'Sample ID',
      key: 'sample_id',
    },
    {
      value: 'Sample Name',
      key: 'name',
    },
    {
      value: 'Type',
      key: 'type',
    }
  ];
  samplesTableData:any[] = [];
  samplesTableStyles = structuredClone(TABLE_STYLES);
  // usersTableConfig!: TableConfig;
  usersTableHeaders:HeaderCell[] = [
    {
      value: '',
      key: 'initials',
      style: {
        width: '32px'
      }
    },
    {
      value: 'User Name',
      key: 'name',
    },
    {
      value: 'Organization',
      key: 'organization',
    }
  ];
  usersTableData:any[] = [];
  usersTableStyles = structuredClone(TABLE_STYLES);
  activeTooltipItem: any = null;
  tooltipPosition = { x: 0, y: 0 };
  tooltipContent: string = '';
  @ViewChild('statusCellTemplate', {static: true}) statusCellTemplate!: TemplateRef<any>;
  @ViewChild('viewResponsesButtonCell', {static: true}) viewResponsesButtonCell!: TemplateRef<any>;
  @ViewChild('panelSampleCellTemplate', {static: true}) panelSampleCellTemplate!: TemplateRef<any>;
  @ViewChild('userIconCellTemplate', {static: true}) userIconCellTemplate!: TemplateRef<any>;

   async ngOnInit(){
    this.route.data.subscribe(async (data) => {
      this.group = data['group'];
      console.log('data', data);
      
      this.groupPanelsData = data['panels'];
      this.groupSamplesData = data['samples'];
      this.groupUsersData = data['users'];
      await this.configurePanelsTable();
      await this.configureSamplesTable();
      await this.configureUsersTable();
      this.setTableHeaderColors();
    });
  }

  setTableHeaderColors() {
    // Panels - Blue
    this.panelsTableStyles = structuredClone(TABLE_STYLES);
    this.panelsTableStyles.header!.style = {
      background: '#CBE5FD',
      color: "#00519C",
      borderRadius: '5px'
    };
    
    // Samples - Purple
    this.samplesTableStyles = structuredClone(TABLE_STYLES);
    this.samplesTableStyles.header!.style = {
      background: '#F5D9FF',
      color: "#5B1E98",
      borderRadius: '5px'
    };
    
    // Users - Pink
    this.usersTableStyles = structuredClone(TABLE_STYLES);
    this.usersTableStyles.header!.style = {
      background: '#FCD5E6',
      color: "#7E1353",
      borderRadius: '5px'
    };
  }

  getEndDate(panel: any) {
    const date = new Date(panel.end_date._seconds * 1000);
    if (date.getFullYear() > 4000) {
      return 'Ongoing';
    }
    return 'Scheduled';
  }

  getStatusColor(status: string) {
    return status.toLowerCase() === 'ongoing' ? '#40BB44' : '#FEC33C';
  }

  async getUsersTableData() {
    const tableData: any[] = this.groupUsersData.map((user: any) => {
      const initials = user.first_name.charAt(0) + user.last_name.charAt(0);
      return {
        initials: this.getInitialsFromString(user.first_name + ' ' + user.last_name, 1),
        initials__template: this.userIconCellTemplate,
        initials__templateContext: {
          $implicit: user
        },
        name: user.first_name + ' ' + user.last_name,
        organization: user.email,
      }
    });

    return tableData;
  }
  async getSamplesTableData() {
    const tableData: any[] = this.groupSamplesData.map((sample: any) => {
      return {
        sample_id: sample.id,
        name: sample.name,
        type: this.constantsService.matrixObject[sample.product_type]?.name || 'Unknown',
      }
    });

    return tableData;
  }

  async getPanelsTableData() {
    const tableData: any[] = this.groupPanelsData.map((panel: any) => {
      const status = this.getEndDate(panel);
      console.log('status', status);
      
      return {
        status: status,
        status__template: this.statusCellTemplate,
        status__templateContext: {
          $implicit: status
        },
        name: panel.name,
        sample: panel.product.name,
        sampleImage: panel.product.image,
        sample__template: this.panelSampleCellTemplate,
        sample__templateContext: {
          $implicit: panel
        },
        responses: '12',
        results: "View Results",
        results__template: this.viewResponsesButtonCell,
        results__templateContext: {
          $implicit: panel
        },
        panelId: panel.id
      }
    });

    return tableData;
  }

  getColorFromStatus(status: any){
    switch(status.status.toLowerCase()){
      case 'ongoing':
        return '#40BB44';
      case 'scheduled':
        return '#FEC33C';
      case 'ended':
      case 'completed':
        return '#0084FF';
      default:
        return '#E34E4E';
    }
  }

  async configurePanelsTable() {
    const bodyData = await this.getPanelsTableData();
    this.panelsTableData = bodyData;
    console.log('this.panelsTableData', this.panelsTableData);
    
  }
  async configureSamplesTable() {
    const bodyData = await this.getSamplesTableData();
    this.samplesTableData = bodyData;
  }
  async configureUsersTable() {
    const bodyData = await this.getUsersTableData();
    this.usersTableData = bodyData;
  }

  getRandomColor(initials: string) {
    // Generate a deterministic color based on initials
    const colors = [
      '#7E1353', '#5B1E98', '#00519C', 
      '#1976D2', '#009688', '#4CAF50', 
      '#8BC34A', '#CDDC39', '#FFC107', 
      '#FF9800', '#FF5722', '#795548'
    ];
    
    // Create a simple hash from the initials
    const hash = initials.charCodeAt(0) + (initials.length > 1 ? initials.charCodeAt(1) : 0);
    return colors[hash % colors.length];
  }

  onPanelRowClick(event: any) {
    // if (event.cell.key === 'results') {
    //   // Navigate to panel results
    //   const panelId = this.groupPanelsData[event.rowIndex].id;
    //   this.router.navigate([`/panels/${panelId}`]);
    // } else {
    //   console.log(event);
    // }
  }

  onSampleRowClick(event:any){
    console.log('onSampleRowClick', event);
    const sampleId = event.cell.data.sample_id;
    // console.log('sampleId', sampleId);  
    this.router.navigate([`/products/${sampleId}`],
      {queryParams: { 
        from: 'group',
        groupId: this.group.id
      }}
    );
  }

  openResponseDetails(panel: any){
    console.log('openResponseDetails', panel);
    this.router.navigate([`/panels/${panel.panelId}`], {
      queryParams: { 
        from: 'group',
        groupId: this.group.id
      }
    });
  }

  toggleActionsPopover() {
    this.showActionsPopover = !this.showActionsPopover;
  }

  deleteGroup(group: any) {
    this.groupsService.deleteGroup(group.id).subscribe((response: any) => {
      if (response.message === 'Group deleted successfully') {
        this.popupService.closeAllPopups();
        this.router.navigate(['/groups']);
      }
    });
  }

  getInitialsFromString(string: string, slice: number = 2){
    if(!string) return '';
    return string.split(' ').map(name => name[0]).join('').slice(0, slice).toUpperCase();
  }

  getOwnerInitials(group: any){
    return this.getInitialsFromString(group.owner.first_name + ' ' + group.owner.last_name, 2);
  }

  showPanelSampleTooltip(event: MouseEvent, panel: any) {
    const element = event.target as HTMLElement;
    const rect = element.getBoundingClientRect();
    
    this.tooltipPosition = {
      x: rect.left + 15,
      y: rect.top - 30 // Position above the element with more space
    };
    this.activeTooltipItem = panel;
    this.tooltipContent = panel.sample;
  }

  hidePanelSampleTooltip() {
    this.activeTooltipItem = null;
    this.tooltipContent = '';
  }

  showOwnerTooltip(event: MouseEvent, group: any) {
    const element = event.target as HTMLElement;
    const rect = element.getBoundingClientRect();
    
    this.tooltipPosition = {
      x: rect.left + 15,
      y: rect.top - 30 // Position above the element with more space
    };
    this.activeTooltipItem = group;
    this.tooltipContent = group.owner.first_name + ' ' + group.owner.last_name;
  }

  hideOwnerTooltip() {
    this.activeTooltipItem = null;
    this.tooltipContent = '';
  }

  
}

