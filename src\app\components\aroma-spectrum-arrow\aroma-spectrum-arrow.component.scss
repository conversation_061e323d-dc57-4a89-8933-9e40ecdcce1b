:host{
    display: block;
    position: relative;
    height: inherit;
    width: inherit;
    // margin-bottom: -23px;

    .arrow-container{
        position: relative;
        height: inherit;
        width: inherit;
        overflow: hidden;

        .arrow-container{
            height: 170px;
        }

        svg{
            width: 100%;    
            height: 100%;

            path{

                &:not(.line){
                    fill: #000;
                }
            }
        }
        
        // .spectrum-categories-and-line{
        //     display: block;
        //     position: relative;
        //     top: 15px;

        //     .spectrum-categories{
        //         width: 100%;
        //         display: flex;
        //         flex-flow: row nowrap;
        //         align-items: center;
        //         justify-content: space-around;

        //         svg{
        //             position: relative;

        //             &.sweet{
        //                 left: 42px;
        //             }

        //             &.prototypical{
        //                 left: 10px;
        //             }
                    
        //             &.savory{
        //                 right: 28px;
        //             }
        //         }
        //     }

        //     .line{
        //         width: 100%;
        //         display: flex;
        //         flex-flow: row nowrap;
        //         align-items: center;
        //         justify-content: center;
        //         position: relative;
        //         top: -9px;
        //     }
        // }

        // .aroma-notes{
        //     width: 100%;
        //     display: flex;
        //     flex-flow: row nowrap;
        //     align-items: center;
        //     justify-content: center;
        //     position: absolute;
        //     top: 0;
        //     height: 100%;
        // }

        // .main-arrow{
        //     width: 100%;
        //     display: flex;
        //     flex-flow: row nowrap;
        //     align-items: center;
        //     justify-content: center;
        //     position: absolute;
        //     top: 0px;
        // }
    }

    .blur{
        // border:1px solid red;
        width: 100%;
        height: 96px;
        background: linear-gradient(180deg, rgba(255, 255, 255, 0) 0%, #FFF 34.85%);
        position: absolute;
        bottom: -36px;
    }

    // @media only screen and (max-device-width: 1185px) {

    //     .arrow-container{
    //         height: 170px;
    //     }

    //     .blur{

    //     }
    // }
}