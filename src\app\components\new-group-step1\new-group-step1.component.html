<div class="form-step-container">
  <form [formGroup]="form">
    <div class="form-step-title">Configure Group</div>

    <div
      class="input-container"
      [class.error-shake]="groupsService.formErrors['step1']['name']"
    >
      <label for="group-name">Group Name</label>
      <input-text
        [control]="nameControl"
        [id]="'group-name'"
        [placeholder]="'group name'"
      ></input-text>
      <div 
      class="error-message" 
      *ngIf="nameControl.invalid && (nameControl.dirty || nameControl.touched)"
    >
      {{ groupsService.getErrorMessage('name') }}
    </div>
    </div>

    <div
      class="input-container textarea-container"
      [class.error-shake]="groupsService.formErrors['step1']['description']"
    >
      <label for="group-description">Group Description</label>
      <input-text-area
        [control]="descriptionControl"
        [id]="'group-description'"
        [placeholder]="'group description'"
      ></input-text-area>
      <div 
        class="error-message" 
        *ngIf="descriptionControl.invalid && (descriptionControl.dirty || descriptionControl.touched)"
      >
        {{ groupsService.getErrorMessage('description') }}
      </div>
    </div>

    <div class="input-container">
      <label for="group-users">Included Entities</label>
      <div [id]="'group-users'" class="table-container" >
        <div class="tabs-container" [ngClass]="{
            'samples-tab': activeTab === 'samples',
            'panelists-tab': activeTab === 'panelists'
        }"  >
          <button
            class="tab-button"
            [class.active]="activeTab === 'panels'"
            (click)="setActiveTab('panels')"
          >
            Panels
          </button>

          <button
            class="tab-button"
            [class.active]="activeTab === 'samples'"
            [class.samples-tab]="activeTab === 'samples'"
            (click)="setActiveTab('samples')"
          >
            Samples
          </button>
          <button
            class="tab-button"
            [class.active]="activeTab === 'panelists'"
            
            [class.panelists-tab]="activeTab === 'panelists'"
            (click)="setActiveTab('panelists')"
          >
            Users
          </button>
        </div>
        <div class="header-elem">
          <span class="title">All Panels</span>
          <div class="search-container">

            <!-- <svg width="10" height="13" viewBox="0 0 10 13" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path fill-rule="evenodd" clip-rule="evenodd" d="M7.23259 9.71276C5.05989 10.8734 2.33687 10.2213 0.896706 8.09053C-0.673857 5.76576 -0.127797 2.55724 2.11586 0.929125C4.35868 -0.698219 7.45525 -0.132417 9.02592 2.19236C10.4703 4.32995 10.1246 7.21541 8.3157 8.9317L9.87965 11.23C10.0892 11.5386 10.0182 11.965 9.72045 12.183C9.42266 12.4001 9.01023 12.3266 8.80071 12.018L7.23259 9.71276ZM7.0503 8.23243C8.69758 7.03757 9.09764 4.68192 7.94529 2.97613C6.7913 1.27015 4.51867 0.854776 2.8724 2.04963C1.22512 3.24449 0.825061 5.59927 1.97741 7.30593C3.1281 9.00762 5.39242 9.42472 7.0389 8.24096C7.04219 8.2384 7.04617 8.23499 7.0503 8.23243Z" fill="#292727"/>
                </svg> -->
            
                
              <input-text
                [control]="searchControl"
                class="search-input"
                [placeholder]="'search'"
              >
            
            </input-text>
          </div>
          <div class="filter-container">
              <input-text
                [control]="filterControl"
                class="filter-input"
                [placeholder]="'Filters'"
              ></input-text>
          </div>
        </div>
        <div class="table-elem">
          <div *ngIf="activeTab === 'panels'" class="table-form-container">
            <!-- <lamb-table [config]="tableConfig"></lamb-table> -->

            <!-- Panel Content -->
            <form [formGroup]="groupArraysForm" (ngSubmit)="submitForm()">
              <!-- Table Headers -->
              <div class="table-header" >
                <div class="select-column">Select</div>
                <div class="status-column">Status</div>
                <div class="name-column">Name</div>
              </div>

              <!-- Table Body -->
              <div formArrayName="panels" class="table-body">
                <div
                  *ngFor="
                    let panelControl of panelsFormArray.controls;
                    let i = index
                  "
                  [formGroupName]="i"
                  class="table-row"
                >
                  <div class="select-column">
                    <input
                      type="radio"
                      [id]="'select-' + i"
                      formControlName="selected"
                      [value]="true"
                      (click)="toggleSelection(i)"
                      [checked]="panelControl.get('selected')?.value"
                      [class.checked]="panelControl.get('selected')?.value"
                    />
                  </div>

                  <div class="status-column">
                    <span class="status-dot"></span>
                    <span class="status-text">{{
                      panelControl.get("status")?.value
                    }}</span>
                  </div>

                  <div class="name-column">
                    <span>{{ panelControl.get("name")?.value }}</span>
                  </div>
                </div>
              </div>
              <!-- <button type="submit" class="submit-button">Submit</button> -->
            </form>
          </div>
            
            <div *ngIf="activeTab === 'samples'" class="table-form-container">
    
              <!-- Panel Content -->
              <form [formGroup]="groupArraysForm" (ngSubmit)="submitForm()">
                <!-- Table Headers -->
                <div class="table-header samples-tab">
                  <div class="select-column">Select</div>
                  <div class="name-column">Name</div>
                  <div class="type-column">Type</div>
                </div>
  
                <!-- Table Body -->
                <div formArrayName="samples" class="table-body">
                  <div
                    *ngFor="
                      let sampleControl of samplesFormArray.controls;
                      let i = index
                    "
                    [formGroupName]="i"
                    class="table-row"
                  >
                    <div class="select-column">
                      <input
                        type="radio"
                        [id]="'select-' + i"
                        formControlName="selected"
                        [value]="true"
                        (click)="toggleSelection(i)"
                        [checked]="sampleControl.get('selected')?.value"
                        [class.checked]="sampleControl.get('selected')?.value"
                      />
                    </div>

                    <div class="name-column">
                      <span>{{ sampleControl.get("name")?.value }}</span>
                    </div>
                    <div class="type-column">
                      <span>{{ sampleControl.get("type")?.value }}</span>
                    </div>
                  </div>
                </div>
                <!-- <button type="submit" class="submit-button">Submit</button> -->
              </form>
            </div>


            <div *ngIf="activeTab === 'panelists'" class="table-form-container">
    
                <!-- Panel Content -->
                <form [formGroup]="groupArraysForm" (ngSubmit)="submitForm()">
                  <!-- Table Headers -->
                  <div class="table-header panelists-tab">
                    <div class="select-column">Select</div>
                    <div class="name-column">Name</div>
                    <div class="Organization-column">Organization</div>
                  </div>
    
                  <!-- Table Body -->
                  <div formArrayName="users" class="table-body">
                    <div
                      *ngFor="
                        let userControl of usersFormArray.controls;
                        let i = index
                      "
                      [formGroupName]="i"
                      class="table-row"
                    >
                      <div class="select-column">
                        <input
                          type="radio"
                          [id]="'select-' + i"
                          formControlName="selected"
                          [value]="true"
                          (click)="toggleSelection(i)"
                          [checked]="userControl.get('selected')?.value"
                          [class.checked]="userControl.get('selected')?.value"
                        />
                      </div>

                      <div class="name-column">
                        <span>{{ userControl.get("name")?.value }}</span>
                      </div>
                      <div class="affiliation-column">
                        <span>{{ userControl.get("email")?.value }}</span>
                      </div>
                    </div>
                  </div>
                  <!-- <button type="submit" class="submit-button">Submit</button> -->
                </form>
              </div>
            
            <button
              *ngIf="currentState.hasMore && !currentState.isLoading"
              (click)="currentState.loadMore()"
              class="load-more-btn">
                Load More {{ activeTab }}
            </button>

            <div *ngIf="currentState.isLoading" class="loading-spinner">
              <svg class="spinner" viewBox="0 0 50 50">
                <circle class="path" cx="25" cy="25" r="20" fill="none" stroke-width="5"></circle>
              </svg>
            </div>

        </div>
      </div>
    </div>
  </form>
</div>
