<div class="sub-section-header">
  <step-type-icon [stepType]="'aroma-distribution'"></step-type-icon>
  <span>Aroma Category Distribution</span>
  <div class="line"></div>
  <div class="export-csv-button" (click)="exportDistributionCSV()">
    <svg xmlns="http://www.w3.org/2000/svg" width="13" height="13" viewBox="0 0 13 13" fill="none">
      <path d="M12.9996 4.06233C12.9996 2.71589 11.9086 1.62493 10.5622 1.62493H8.0557L6.90601 0.475252C6.60134 0.170577 6.18716 0 5.75633 0H2.4374C1.09096 0 0 1.09096 0 2.4374V10.5621C0 11.9085 1.09096 12.9994 2.4374 12.9994H5.48416C5.93323 12.9994 6.29662 12.6361 6.29662 12.187C6.29662 11.7379 5.93323 11.3745 5.48416 11.3745H2.4374C1.98833 11.3745 1.62493 11.0111 1.62493 10.5621V2.4374C1.62493 1.98833 1.98833 1.62493 2.4374 1.62493H5.75633L6.90601 2.77461C7.21069 3.07929 7.62486 3.24986 8.0557 3.24986H10.5622C10.778 3.24986 10.9843 3.33555 11.1366 3.4879C11.2889 3.64024 11.3746 3.84652 11.3746 4.06233V5.07791C11.3746 5.52698 11.738 5.89038 12.1871 5.89038C12.6362 5.89038 12.9996 5.52698 12.9996 5.07791V4.06233Z" fill="black"/>
      <path d="M12.4735 8.48255L10.7288 6.73782C10.4114 6.41966 9.89644 6.41966 9.57907 6.73782C9.2617 7.05519 9.2617 7.56934 9.57907 7.88668L10.6312 8.93718H8.1247C7.22732 8.93718 6.49976 8.20962 6.49976 7.31225C6.49976 6.86318 6.13636 6.49979 5.68729 6.49979C5.23822 6.49979 4.87483 6.86318 4.87483 7.31225C4.87483 8.17391 5.21759 9.00066 5.82694 9.6099C6.43629 10.2192 7.26303 10.562 8.12459 10.562H10.6311L9.58055 11.6125H9.58135C9.26319 11.9299 9.26319 12.444 9.58135 12.7614C9.89872 13.0795 10.4129 13.0795 10.7302 12.7614L12.475 11.0166H12.4757C13.1748 10.3169 13.1748 9.18226 12.4757 8.48236L12.4735 8.48255Z" fill="black"/>
    </svg>
  
    <div class="hover-text">Export <span>.CSV</span></div>
  </div>
</div>

<div class="chart-container">
  <canvas #chartCanvas></canvas>
</div>

<div class="categories-legend">
  <div class="category" *ngFor="let category of topThreeCategories; let i = index">
    <div class="color-dot" [style.background-color]="getColorForIndex(i)"></div>
    <div class="category-info">
      <div class="category-name">{{category.category}}</div>
      <div class="percentage">{{(category.percentage) | number:'1.0-0'}}%</div>
    </div>
    <div class="top-note">Top note in <span class="italic category-name">{{category.category}}</span>: <span class="bold">{{category.topNote}}</span></div>
  </div>
</div>