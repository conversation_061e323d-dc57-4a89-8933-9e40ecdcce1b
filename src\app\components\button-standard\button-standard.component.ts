import { Component, EventEmitter, Input, Output } from '@angular/core';
import { NgIf } from '@angular/common';

@Component({
  selector: 'button-standard',
  standalone: true,
  imports: [NgIf],
  templateUrl: './button-standard.component.html',
  styleUrl: './button-standard.component.scss'
})
export class ButtonStandardComponent {
  @Input() loading: boolean = false;
  @Output() onAction = new EventEmitter();

  handleAction() {
    if (!this.loading) {
      this.onAction.emit();
    }
  }
}
