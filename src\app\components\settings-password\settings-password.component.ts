import { Component, inject } from '@angular/core';
// import { BackButtonComponent } from '../back-button/back-button.component';
import { RouterLink } from '@angular/router';
import { ButtonStandardComponent } from '../button-standard/button-standard.component';
import { AuthService } from '../../services/auth.service';
import { ToastService } from '../../services/toast.service';

@Component({
  selector: 'settings-password',
  standalone: true,
  imports: [RouterLink, ButtonStandardComponent],
  templateUrl: './settings-password.component.html',
  styleUrl: './settings-password.component.scss'
})
export class SettingsPasswordComponent {

  authService = inject(AuthService);
  toastService = inject(ToastService);

  async resetPassword(){
    try {
      await this.authService.sendPasswordResetEmail();
      this.toastService.goodToast('Password reset email sent successfully!');
    } catch (error: any) {
      this.toastService.burntToast('An error occurred while sending password reset email');
    }
  }

}
