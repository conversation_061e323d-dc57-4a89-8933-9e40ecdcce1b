<button-two-click [type]="'close'" (onConfirm)="close()">
    <svg width="14" height="14" viewBox="0 0 14 14" fill="none" xmlns="http://www.w3.org/2000/svg">
        <path d="M7 5.58549L12.2929 0.292606C12.683 -0.0975353 13.3164 -0.0975353 13.7074 0.292606C14.0975 0.683605 14.0975 1.31695 13.7074 1.70711L8.41451 7L13.7074 12.2929C14.0975 12.683 14.0975 13.3164 13.7074 13.7074C13.3164 14.0975 12.683 14.0975 12.2929 13.7074L7 8.41451L1.70711 13.7074C1.31697 14.0975 0.683627 14.0975 0.292606 13.7074C-0.0975353 13.3164 -0.0975353 12.683 0.292606 12.2929L5.58549 7L0.292606 1.70711C-0.0975353 1.31697 -0.0975353 0.683627 0.292606 0.292606C0.683605 -0.0975353 1.31695 -0.0975353 1.70711 0.292606L7 5.58549Z" fill="black"/>
    </svg>
</button-two-click>


<form-header [service]="productsService"></form-header>

<!-- <div class="form-body"> -->
    <new-product-step-1 [style.--full-screen-form-max-width]="'var(--form-header-max-width)'" *ngIf="productsService.formStep === 1" class="form-body"></new-product-step-1>
    <new-product-step-2 *ngIf="productsService.formStep === 2" class="form-body"></new-product-step-2>
    <new-product-step-3 [style.--full-screen-form-max-width]="'var(--form-header-max-width)'" *ngIf="productsService.formStep === 3" class="form-body"></new-product-step-3>
<!-- </div> -->

<form-footer [service]="productsService"></form-footer>