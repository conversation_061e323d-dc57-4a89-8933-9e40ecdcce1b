@import '../../../_colors';

:host{
    --settings-max-width: 932px;
    --settings-nav-width: 286px;
    --settings-border-top-height: 10px;
    --settings-border-top-color: #{$mediumGray};

    --settings-nav-border-color: #{$mediumGray};

    display: grid;
    grid-template-columns: var(--settings-nav-width) 1fr;
    //grid-template-rows: var(--settings-border-top-height) auto;
    width: 100%;
    max-width: var(--settings-max-width);
    min-height: var(--standard-height);
    height: auto;
    padding: 0 var(--standard-padding);
    box-sizing: border-box;
    margin: 0 auto;

    .border-top{
        border-top: var(--settings-border-top-height) solid var(--settings-border-top-color);
        grid-column: span 2;
        height:0px;
    }

    .settings-nav-container{
        border-right: 1px solid var(--settings-nav-border-color);
        padding: var(--standard-padding) 0;

        .profile-header{
            width: 100%;
            min-height: 100px;

        }

        .settings-section{

            .settings-link{
                text-decoration: none;
                color: black;
                font-size: 14px;
                padding: 13px;
                width: 97%;
                border-radius: 8px;

                &.active{
                    color: white;
                    background-color: $blue;
                }
            }
        }
    }

    .settings-outlet-container{
        padding: var(--standard-padding) 0;
        padding-left: var(--standard-padding);
    }
}