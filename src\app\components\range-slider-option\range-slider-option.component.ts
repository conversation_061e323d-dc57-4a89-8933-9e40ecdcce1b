import { Component, EventEmitter, Input, Output } from '@angular/core';
import { NgIf } from '@angular/common';
import { InputTextComponent } from '../input-text/input-text.component';
import { InputTextAreaComponent } from '../input-text-area/input-text-area.component';
import { DigitToWordPipe } from '../../pipes/digit-to-word.pipe';


@Component({
  selector: 'range-slider-option',
  standalone: true,
  imports: [
    InputTextComponent,
    InputTextAreaComponent,
    DigitToWordPipe,
    NgIf,
  ],
  templateUrl: './range-slider-option.component.html',
  styleUrl: './range-slider-option.component.scss'
})
export class RangeSliderOptionComponent {

  @Input() option: any;
  @Input() index: number = 0;
  @Input() editing: boolean = false;
  @Output() removeOptionEvent = new EventEmitter<number>();

  removeOption(){
    this.removeOptionEvent.emit(this.index);
  }

  showRemoveButton(){
    return !this.editing && this.index > 0;
  }
}