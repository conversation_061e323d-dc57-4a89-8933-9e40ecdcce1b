@import url('https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap');
@import './_colors.scss';

/* You can add global styles to this file, and also import other style files */

%fullscreen-fixed {
    width: 100%;
    height: 100%;
    position: fixed;
    top: 0;
    left: 0;
    background-color: #fff;
    display: block;
    z-index: 1000;
    padding: 15px;
    box-sizing: border-box;
    overflow-y: auto;
}

%settings-host {

    .settings-section {
        max-width: 500px;
        margin: 0 auto;
    }
}

*{
    margin: 0;
    padding: 0;
    box-sizing: border-box;
    // color: #292727;
}

html, body {
    height: 100%;
    margin: 0;
    padding: 0;
    font-size: 16px;
    font-family: 'Inter', sans-serif;

    &.no-scroll{
        overflow: hidden !important;
    }

    --nav-height: 70px;
    --footer-height: 70px;
    --standard-padding: 30px;
    --standard-height: calc(100vh - var(--nav-height) - var(--footer-height));
    
    // --table-header-background: #{$lightBlue};
    // --table-header-text-color: #{$darkBlue};
    // --table-header-text-size: 13px;
    // --table-header-text-weight: 700;
    // --table-header-height: 5px;
    // --table-header-border-radius: 20px;
    // --table-header-cell-padding: 8px 15px;

    // --table-row-odd-background: #fff;
    // --table-row-even-background: #{$lightestGray};
    // --table-row-text-color: #000;
    // --table-row-text-size: 13px;
    // --table-row-text-weight: 400;
    // --table-column-gap: 10px;
    // --table-row-gap: 10px;
    // --table-cell-padding: 15px;
    // --table-row-height: 60px;
    // --table-row-border-radius: 18px;

    --full-screen-form-background: #fff;
    --full-screen-form-max-width: 550px;

    --form-header-margin-top: 80px;
    --form-header-margin-bottom: 100px;
    --form-header-margin-left: auto;
    --form-header-margin-right: auto;   
    --form-header-max-width: 850px;
    --form-super-header-font-size: .75rem;
    --form-super-header-font-weight: 500;
    --form-super-header-color: #000;
    --form-header-font-size: 1.625rem;
    --form-header-font-weight: 700;
    --form-header-color: #000;

    --form-step-title-font-size: .875rem;
    --form-step-title-font-weight: 700;
    --form-step-title-color: #000;
    --form-step-title-border-color: #{$mediumGray};
    --form-step-title-padding-bottom: 8px;
    --form-step-title-margin-bottom: 20px;

    --input-container-margin-bottom: 30px;
    --input-container-label-font-size: .75rem;
    --input-container-label-font-weight: 500;
    --input-container-label-color: $darkGray;
    --input-container-label-width: 180px;
}

// input resets
input[type="text"],
input[type="number"],
input[type="email"],
input[type="password"],
input[type="checkbox"],
input[type="radio"],
select,
button{
    appearance: none;
    -webkit-appearance: none;
    -moz-appearance: none;
    border: none;
    outline: none;
    background: none;
    padding: 0;
    margin: 0;
    border-radius: 0;
    font-family: inherit;
    font-size: inherit;
    color: inherit;
    box-sizing: border-box;
    width: fit-content;
    height: fit-content;
}

button{
    cursor: pointer;
}

// // input styles

input[type="text"],
input[type="number"],
input[type="email"],
input[type="password"],
select{
    background-color: $inputBackground;
    outline: $inputBorder;
    width: 265px;
    height: 40px;
    padding: 7px 10px;
    border-radius:10px;
    font-size: 1rem;
    color: $appText;

    &:focus{
        outline: $inputFocusBorder;
    }

    &::placeholder{
        color: $placeholderText;
        font-size: .75rem;
        font-weight: 400;
    }
}

// select{
//     background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='46' height='46' viewBox='0 0 46 46' fill='none'%3E%3Cpath d='M28.4216 15.7294C28.6482 15.5027 28.7846 15.1858 28.7846 14.8688C28.7846 14.5519 28.6482 14.235 28.4216 14.0083C27.9222 13.5089 27.152 13.5089 26.6987 14.0083L22.665 18.0419L18.6314 14.0083C18.132 13.5089 17.3618 13.5089 16.9085 14.0083C16.4552 14.5077 16.4091 15.2779 16.9085 15.7312L21.8046 20.6255C22.3039 21.1248 23.0742 21.1248 23.5275 20.6255L28.4216 15.7294Z' fill='%2340116F'/%3E%3C/svg%3E");
//     background-repeat: no-repeat;
//     background-position: right -10px top -2px;
//     background-size: 60px;
// }

.white{
    color: #fff;
}

.green{
    color: $green;
}

.bold{
    font-weight: 700;
}

.underline{
    text-decoration: underline;
}

.italic{
    font-style: italic;
}

.hidden-input,
.hide-input{
	position: absolute;
	top: 0;
	left: 0;
	opacity: 0;
	width: 0;
	height: 0;
	overflow: hidden;
	pointer-events: none;
	z-index: -1;
}

.table-container {
    overflow-x: auto;
}

.settings-sub-page-header{
    display: flex;
    width: 100%;
    justify-content: flex-start;
    align-items: center;
    gap: 10px;
    margin-bottom: 20px;

    .header-title{
        font-size: 1.1rem;
        font-weight: 700;
    }

    button-standard{
        margin-left: auto;
        width: fit-content;
        margin-right: 0;
    }
}

.settings-section{
    display: grid;
    grid-template-columns: 1fr;
    //gap: 10px;
    //max-width: 500px;
    margin: auto;
    margin-bottom: 20px;
    
    .section-header{
        display: flex;
        align-items: center;
        gap: 10px;
        font-size: 1rem;
        font-weight: 700;
        border-bottom: 1px solid $mediumGray;
        padding: 10px 0px;
        margin-bottom: 10px;
    }

    label.primary{
        font-size: .9rem;
        font-weight: 500;
        margin-bottom: 5px;
        display: block;
        color: $darkGray;
    }

    .input-container{
        display: grid;
        grid-template-columns: var(--input-container-label-width) 1fr;
        align-items: center;
        margin-bottom: var(--input-container-margin-bottom);
        gap: 20px;

        label{
            // width: var(--input-container-label-width);
            font-size: var(--input-container-label-font-size);
            font-weight: var(--input-container-label-font-weight);
            color: var(--input-container-label-color);
            flex-shrink: 0;

            span.optional{
                font-size: 0.5625rem;
                font-weight: 700;
                display: block;
            }
        }

        .radio-container{
            display: flex;
            flex-direction: row;
            gap: 10px;

            input-radio-block{
                flex-basis: 139px;
                flex-grow: 1;
                text-align: center;
            }
        }
    }

    &.variation-1{

        .section-header{
            text-align: center;
            border-bottom: none;
            padding-bottom: 0px;
            margin: 0 auto;
        }
    }
}

.full-screen-form{
    display: flex;
    flex-direction: column;
    justify-content: flex-start;
    align-items: center;
    width: 100%;
    height: 100%;
    position: fixed;
    top: 0;
    left: 0;
    background-color: var(--full-screen-form-background);
    z-index: 1000;
    overflow-y: auto;

    .form-body{
        width: 100%;
        max-width: var(--full-screen-form-max-width);
        margin: 0 auto;
        display: block;
        height: 100%;

        // .form-step{
            // width: 100%;

        .form-step-container{
            width: 100%;
            height: 100%;
            display: flex;
            margin-top: -30px;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            form {
                    width: 100%;
            
                    .form-step-title {
                        font-size: var(--form-step-title-font-size);
                        font-weight: var(--form-step-title-font-weight);
                        color: var(--form-step-title-color);
                        width: 100%;
                        border-bottom: 1px solid var(--form-step-title-border-color);
                        padding-bottom: var(--form-step-title-padding-bottom);
                        margin-bottom: var(--form-step-title-margin-bottom);
                    }
                }
            }
        }
    }

    .input-container {
        display: grid;
        grid-template-columns: var(--input-container-label-width) 1fr;
        align-items: center;
        margin-bottom: var(--input-container-margin-bottom);
        gap: 20px;

        label {
            // width: var(--input-container-label-width);
            font-size: var(--input-container-label-font-size);
            font-weight: var(--input-container-label-font-weight);
            color: var(--input-container-label-color);
            flex-shrink: 0;
          
        span.optional{
            font-size: 0.5625rem;
            font-weight: 700;
            display: block;
        }
    }
}

// bs-datepicker-container{
//     position: absolute !important;
//     top:0 !important;
//     left:0 !important;
//     z-index: 1000 !important;
//     transform: none !important;
// }

// @media (min-width: 769px) {
//     .settings-section{
//         max-width: 1100px;
//     }
// }

page-header {

    svg {
        margin-right: 15px;
    }
}

.error-shake{
    label,
    span{
        color: $red !important;
        animation: errorShake .7s ease 0s 1 normal forwards;	
    }

        input,
        select,
        textarea{
            outline: 2px solid $red !important;
            color: $red !important;
            animation: errorShake .7s ease 0s 1 normal forwards;	

            &::placeholder{
                color: $red !important;
            }
        }
    
}

@keyframes errorShake {
	0%,
	100% {
		transform: translateX(0);
	}

	10%,
	30%,
	50%,
	70% {
		transform: translateX(-5px);
	}

	20%,
	40%,
	60% {
		transform: translateX(5px);
	}

	80% {
		transform: translateX(3px);
	}

	90% {
		transform: translateX(-3px);
	}
}