import { Component, Input, OnInit, ElementRef, ViewChild, AfterViewInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { Chart, registerables } from 'chart.js';
import { PanelStep } from '../../interfaces/panel-step.interface';

Chart.register(...registerables);

@Component({
  selector: 'response-data-multiple-choice',
  standalone: true,
  imports: [CommonModule],
  templateUrl: './response-data-multiple-choice.component.html',
  styleUrl: './response-data-multiple-choice.component.scss'
})
export class ResponseDataMultipleChoiceComponent implements OnInit, AfterViewInit {
  @Input() responseDetails: any = null;
  @Input() step: PanelStep | null = null;

  @ViewChild('barChart') barChart!: ElementRef;
  
  sortedData: {label: string, count: number}[] = [];
  chart!: Chart;

  options: any[] = [];
  optionsObject: { [key: string]: any } = {};

  constructor() {}

  ngOnInit(): void {
    // Process the data structure to extract counts and option details
    // console.log('this.step', this.step);
    this.buildOptions();
    // console.log('this.options', this.options);
    this.processData();
  }

  buildOptions(): void {
    this.options = this.step?.options || [];
    this.optionsObject = this.options.reduce((acc, option) => {
      acc[option.id] = option;
      return acc;
    }, {});
  }

  ngAfterViewInit(): void {
    this.createChart();
  }

  private processData(): void {
    const responseData = this.responseDetails;
    const processedData: {label: string, count: number}[] = [];
    this.options.forEach(option => {
      let count = 0;
      if(responseData && responseData.hasOwnProperty(option.id + '_count')) {
        count = responseData[option.id + '_count'];
      }

      if(responseData && responseData.hasOwnProperty(this.step?.id + "_" + option.id + '_count')) {
        count = responseData[this.step?.id + "_" + option.id + '_count'];
      }

      processedData.push({ label: option.name ? option.name : option.label, count: count });
    });
    
    // Sort by count in descending order
    this.sortedData = processedData.sort((a, b) => b.count - a.count);
  }

  createChart(): void {
    const ctx = this.barChart.nativeElement.getContext('2d');
    
    const maxCount = this.sortedData[0]?.count + 1;
    console.log('maxCount', maxCount);
    
    
    // Custom plugin to display counts at the end of each bar
    const countLabelsPlugin = {
      id: 'countLabels',
      afterDatasetsDraw(chart: Chart) {
        const { ctx } = chart;
        ctx.save();
        
        const meta = chart.getDatasetMeta(0);
        if (!meta.data) return;
        
        ctx.font = '700 12px Inter';
        ctx.textAlign = 'left';
        ctx.textBaseline = 'middle';
        ctx.fillStyle = '#292727';
        
        meta.data.forEach((bar: any, index: number) => {
          const data = chart.data.datasets[0].data[index] as number;
          const { x, y } = bar.tooltipPosition();
          
          // Position the text to the right of each bar
          ctx.fillText(
            data.toString(),
            x + 10,
            y
          );
        });
        
        ctx.restore();
      }
    };
    
    this.chart = new Chart(ctx, {
      type: 'bar',
      data: {
        labels: this.sortedData.map(item => item.label),
        datasets: [{
          data: this.sortedData.map(item => item.count),
          backgroundColor: '#92B3DB', // Color from Figma design
          barThickness: 18,
          borderRadius: {
            topRight: 5,
            bottomRight: 5
          },
          borderSkipped: false
        }]
      },
      options: {
        indexAxis: 'y',
        maintainAspectRatio: false,
        responsive: true,
        layout: {
          padding: {
            left: 0,
            right: 0,
            top: 15,
            bottom: 15
          }
        },
        datasets: {
          bar: {
            barPercentage: 0.5,
            categoryPercentage: 0.7
          }
        },
        plugins: {
          legend: {
            display: false
          },
          tooltip: {
            enabled: false
          }
        },
        scales: {
          x: {
            beginAtZero: true,
            max: maxCount,
            grid: {
              display: false
            },
            border: {
              display: false
            },
            ticks: {
              display: false
            }
          },
          y: {
            grid: {
              display: false
            },
            border: {
              display: false
            },
            ticks: {
              color: '#292727',
              font: {
                family: 'Inter',
                size: 12,
                weight: 500
              },
              padding: 12,
              textStrokeWidth: 0,
              backdropPadding: 0
            },
            afterFit: (scaleInstance: any) => {
              scaleInstance.paddingTop = 15;
              scaleInstance.paddingBottom = 15;
            }
          }
        }
      },
      plugins: [countLabelsPlugin]
    });
  }
}
