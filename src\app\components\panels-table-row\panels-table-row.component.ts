import { Component, Input, inject } from '@angular/core';
import { PanelsService } from '../../services/panels.service';
import { DisplayPublicStatusPipe } from '../../pipes/display-public-status.pipe';

@Component({
  selector: 'panels-table-row',
  standalone: true,
  imports: [DisplayPublicStatusPipe],
  templateUrl: './panels-table-row.component.html',
  styleUrl: './panels-table-row.component.scss'
})
export class PanelsTableRowComponent {

  @Input() panel: any;

  private panelsService = inject(PanelsService);
  numResponses: any = -1;

  get endDate(){
    const date = new Date(this.panel.end_date._seconds * 1000);
    if (date.getFullYear() > 4000) {
      return "Ongoing";
    }
    return date.toLocaleDateString();
  }

  ngOnInit(){
    this.panelsService.getResponseCount(this.panel.id).then((res: any) => {
      // console.log("Panel Response Count: ", res);
      this.numResponses = res.count;
    });
  }
}
