:host{
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 10px;

    label{
        border-radius: 12px;
        border: 1px solid #D8D8D8;
        background: #F7F7F7;
        display: grid;
        grid-template-columns: auto 1fr;
        align-items: flex-start;
        padding: 15px;
        cursor: pointer;

        .step-type-option{

            .step-type-option-name{
                color: #292727;
                font-size: .75rem;
                font-weight: 700;
                margin-bottom: 3px;
            }

            .step-type-option-description{
                font-size: .625rem;
                color: #979797;
                font-weight: 500;
            }
        }
    }
}