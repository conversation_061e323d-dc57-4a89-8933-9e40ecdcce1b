import { Injectable, inject } from '@angular/core';
import { environment } from '../../environments/environment';
import { DOCUMENT } from '@angular/common';
import { BehaviorSubject } from 'rxjs';

@Injectable({
  providedIn: 'root'
})
export class EnvironmentService {
  private document = inject(DOCUMENT);
  
  private baseUrlSubject = new BehaviorSubject<string>('');
  public baseUrl$ = this.baseUrlSubject.asObservable();
  
  private panelUrlSubject = new BehaviorSubject<string>('');
  public panelUrl$ = this.panelUrlSubject.asObservable();
  
  private pdsUrlSubject = new BehaviorSubject<string>('https://pds.abstraxinfra.com');
  public pdsUrl$ = this.pdsUrlSubject.asObservable();
  
  private reviewUrlSubject = new BehaviorSubject<string>('');
  public reviewUrl$ = this.reviewUrlSubject.asObservable();
  
  constructor() {
    this.initUrls();
  }
  
  private initUrls(): void {
    const hostname = this.document.location.hostname;
    let baseUrl = environment.baseUrl; // Default URL
    let panelUrl = environment.panelUrl; // Default review URL
    let reviewUrl = environment.reviewUrl; // Default review URL
    if(hostname.includes('localhost')) {
      baseUrl = 'http://localhost:7890';
      // baseUrl = 'https://api-staging.sensorypanels.com';
      panelUrl = 'http://localhost:4200';
      reviewUrl = 'http://localhost:4200';
    } else if(hostname.includes('staging')) {
      baseUrl = 'https://api-staging.sensorypanels.com';
      panelUrl = 'https://panels-staging.sensorypanels.com';
      reviewUrl = 'https://survey-staging.sensorypanels.com';
    } else {
      hostname.includes('.sensorypanels.com') ? baseUrl = 'https://api.sensorypanels.com' : baseUrl = 'https://sensei-api.abstraxtech.com';
      hostname.includes('.sensorypanels.com') ? panelUrl = 'https://panels.sensorypanels.com' : panelUrl = 'https://panels.abstraxtech.com';
      hostname.includes('.sensorypanels.com') ? reviewUrl = 'https://survey.sensorypanels.com' : reviewUrl = 'https://survey.abstraxtech.com';
    }
    this.baseUrlSubject.next(baseUrl);
    this.panelUrlSubject.next(panelUrl);
    this.reviewUrlSubject.next(reviewUrl);
  }
  
  /**
   * Get the current baseUrl as a string
   */
  getBaseUrl(): string {
    return this.baseUrlSubject.value;
  }
  
  getReviewUrl(): string {
    return this.reviewUrlSubject.value;
  }

  /**
   * Get the current panelUrl as a string
   */
  getPanelUrl(): string {
    return this.panelUrlSubject.value;
  }
  
  /**
   * Get the PDS service URL
   */
  getPdsUrl(): string {
    return this.pdsUrlSubject.value;
  }
  
  /**
   * Get a formatted PDS URL for a specific sample and type
   */
  getPdsFormattedUrl(sample_key: string, pds_type: string = "lite"): string {
    return `${this.getPdsUrl()}/${pds_type}/${sample_key}`;
  }
  
  /**
   * Get the PDS generate PDF endpoint
   */
  getPdsGeneratePdfUrl(): string {
    return `${this.getPdsUrl()}/generate-pdf/`;
  }
} 