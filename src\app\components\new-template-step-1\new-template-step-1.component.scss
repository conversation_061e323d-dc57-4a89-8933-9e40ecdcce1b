:host{
    position: relative;

    .form-step-container{
        
        form{

            .input-container{

                &.textarea-container{
                    align-items: flex-start;

                    label{
                        line-height: 40px;
                    }
                }

                &.radio-container{
                    grid-template-columns: var(--input-container-label-width) 1fr 1fr;

                    input-radio-block{
                        width: 100%;
                    }
                }

                &.date-picker-container{
                    grid-template-columns: var(--input-container-label-width) 1fr 1fr;
                    margin-top: -15px;


                    svg{
                        grid-column: 2;
                        justify-self: end;
                        top: -12px;
                        right: -50px;
                        position: relative;
                    }

                    input-bs-datepicker{
                        justify-self: left;
                        grid-column: 3;
                        right: -35px;
                        position: relative;
                        // margin-right: 10px;
                    }
                }
                
                label{}

                input-text,
                input-text-area{
                    width: 100%;
                }
            }
        }
    }
}