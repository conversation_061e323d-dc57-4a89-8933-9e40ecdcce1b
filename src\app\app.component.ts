import { Component, inject } from '@angular/core';
import { NgIf, AsyncPipe } from '@angular/common';
import { RouterOutlet } from '@angular/router';
import { ToastService } from './services/toast.service';
import { LoadingService } from './services/loading.service';
import { ToastComponent } from './components/toast/toast.component';
import { LoadingComponent } from './components/loading/loading.component';
import { toastSlide } from './modules/animations/animations.module';

@Component({
  selector: 'app-root',
  standalone: true,
  imports: [RouterOutlet, ToastComponent, LoadingComponent, NgIf, AsyncPipe],
  templateUrl: './app.component.html',
  styleUrl: './app.component.scss',
  animations: [toastSlide]
})
export class AppComponent {
  title = 'Sensei by Abstrax - Admin App';
  toastService = inject(ToastService);
  loadingService = inject(LoadingService);
}
