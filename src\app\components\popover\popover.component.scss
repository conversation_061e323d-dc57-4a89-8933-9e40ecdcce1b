:host{
    --popover-item-inset: calc(100% + 5px) 0 auto auto;
    --popover-width: fit-content;
    --popover-background: #fff;
    --popover-border-radius: 12px;
    --popover-padding: 15px 20px;
    --popover-gap: 10px;
    --popover-box-shadow: 0px 4px 14px 0px rgba(0, 0, 0, 0.25);

    // .popover-container{
            display: flex;
            position: absolute;
            inset: var(--popover-item-inset);
            width: var(--popover-width);
            flex-direction: column;            
            background-color: var(--popover-background);
            border-radius: var(--popover-border-radius);
            padding: var(--popover-padding);
            row-gap: var(--popover-gap);
            box-shadow: var(--popover-box-shadow);
            z-index: inherit;
        // }
}