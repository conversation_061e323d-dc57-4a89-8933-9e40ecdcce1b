$navHeaderBg: #fff;
$navHeaderBgOpaque: rgba(255,255,255,.8);
$lightestGray: #F7F7F7;
$lightGray: #EFEFEF;
$mediumLightGray: #d8d8d8;
$mediumGray: #c5c5c5;
$mediumDarkGray: #8c8c8c;
$loginBorderStroke: #535353;
$darkGray: #454545;
$darkestGray: #070707;

$lightYellow: #FFF6E1;
$yellow: #FEC33C;
$gold: #F0A00B;
$bannerBg: $gold;
$darkGold: #4F3A09;

$green: #40BB44;
$brightGreen: #41D946;
$greenishOffWhiteText: #EDFFEE;

$blue: #0084FF;
$lightBlue: #CBE5FD;
$darkBlue: #00519C;
$focusBlue: #6cb7fe;

$purple: #6D116F;

$red: #D85B5B;

$darkPurple: #28003C;
$darkBlue: #030F4C;
$darkGreen: #012D12;
$blackToDarkPurple: linear-gradient(180deg, #000 0%, $darkPurple 100%);
$darkPurpleToBlack: linear-gradient(180deg, $darkPurple 0%, #000 100%);
$inProgressGradient: linear-gradient(277deg, #000 0%, $darkBlue 97.83%);
$availableGradient: linear-gradient(277deg, #000 0%, $darkPurple 97.83%);
$completedGradient: linear-gradient(277deg, #000 0%, $darkGreen 97.83%);
$panelPreviewBg: linear-gradient(151deg, #FFF 0%, $lightYellow 100%);

$panelInProgressBg: $lightestGray;
$panelAvailableBg: $lightestGray;
$panelCompletedBg: $lightestGray;
$inputBorder: 1px solid $mediumLightGray;
$inputHoverBorder: 1px solid $mediumDarkGray;
$inputFocusBorder: 1px solid $blue;
$inProgressHoverBg: linear-gradient(96.83deg, rgba(247, 247, 247, 1) 3.26%, rgba(0, 132, 255, 0.05) 96.92%);
$panelAvailableHoverBg: linear-gradient(96.83deg, rgba(247, 247, 247, 1) 3.26%, rgba(64, 187, 68, 0.04) 96.92%);
$inputBackground: $lightestGray;
$panelInProgressBorder: 1px solid $blue;
$panelAvailableBorder: 1px solid $mediumLightGray;
$panelCompletedBorder: 1px solid $green;
$imgBg: $mediumGray;
$panelText1: #000;
$panelText2: $darkGray;
$navBg: #fff;
$navBorderStroke: $mediumGray;
$placeholderText: $mediumDarkGray;
$grayButtonBg: $mediumGray;
$buttonTextColor: #000;
$appText: #000;
$lightBorderStroke: $mediumLightGray;
$affiliateBackground: #000;
$affiliateText: #fff;