import { Injectable, inject } from '@angular/core';
import { ApiService } from './api.service';

@Injectable({
  providedIn: 'root'
})
export class ConstantsService {

  private apiService = inject(ApiService);
  effects: any;
  effectsObject: any = {};
  flavors: any;
  flavorsObject: any = {};
  metrics: any;
  metricsObject: any = {};
  questions: any;
  questionsObject: any = {};
  // orgQuestions: any;
  // orgQuestionsObject: any = {};
  combinedQuestions: any;
  combinedQuestionsObject: any = {};
  optionsObject: any = {};
  matrix: any;
  matrixObject: any = {};
  packaging: any;
  packagingObject: any = {};
  consumption: any;
  consumptionObject: any = {};
  products: any;
  productsObject: any = {};
  demographics: any;
  demographicsObject: any = {};

  defaultQuestionOrder = ["a9k1bpXX4H57Bdl7XhaX", "h1Cb1yTczSvyyaSqiWfK", "wjA1uhqbIMFuZwAaCoJ8", "awja0ameTALoE0D4GlWD"];

  consumptionMethodOptions = [
    { value: 'smell', label: 'Smell' },
    { value: 'taste', label: 'Taste' },
    { value: 'both', label: 'Both' },
  ];

  panelQuestions = [
    { value: 'Aroma Notes', label: 'Aroma Notes' },
    { value: 'Effects', label: 'Effects' },
    { value: 'Overall Metrics', label: 'Overall Metrics' },
    { value: 'Additional Comments', label: 'Additional Comments' },
  ];

  panelPayOptions = [
    { value: 5, label: '$5' },
    { value: 10, label: '$10' },
    { value: 15, label: '$15' },
    { value: 20, label: '$20' },
    { value: 25, label: '$25' },
  ];

  flavorCategories: string[] = [];

  constructor() { }

  buildConstantsArrays(constants: any){
    this.effects = constants.effects;
    this.flavors = constants.flavors;
    this.metrics = constants.metrics;
    this.questions = constants.questions;
    this.matrix = constants.matrix;
    this.packaging = constants.packaging;
    this.consumption = constants.consumption;
    this.products = constants.products;
    this.demographics = constants.demographics;
  }

  buildConstantsObjects(constants: any){
    
    Object.keys(constants).forEach((key: string) => {
      let arr = constants[key];
      arr.forEach((item: any) => {
        let prop = (key + 'Object') as keyof ConstantsService;
        this[prop][item['id']] = item;
      });
    });

    this.optionsObject = {...this.effectsObject, ...this.flavorsObject, ...this.metricsObject};
  }

  getOptionName(optionId: string){
    return this.optionsObject[optionId].name;
  }

  getTopLevelCategories(constants: any){
    //TODO: this is a hack to get the top level categories in the order of the flavorCategories array
    //  since it's apparently a pain to just re-order them after the CSV is created. 
    const order = ['candy', 'tropical', 'stonefruit', 'berry', 'creamy', 'citrus', 'gassy', 'earth', 'spicy', 'cheesy', 'chemical', 'garlic'];
    return order;
    // let topLevelCategories: Record<string, number> = {};
    // order.forEach((flavor: any) => {
    //   if(topLevelCategories[flavor.type]){
    //     topLevelCategories[flavor.type] = Math.min(topLevelCategories[flavor.type], flavor.priority);
    //   } else {
    //     topLevelCategories[flavor.type] = flavor.priority;
    //   }
    // });
    // return Object.keys(topLevelCategories).sort((a, b) => topLevelCategories[a] - topLevelCategories[b]);
  }

  async getConstants(){
    // const effects = await firstValueFrom(this.apiService.get);
    const constants = await this.apiService.getConstants();
    this.buildConstantsArrays(constants);
    this.buildConstantsObjects(constants);
    this.flavorCategories = this.getTopLevelCategories(constants);
    console.log('this', this);
    return constants;
  }
}
