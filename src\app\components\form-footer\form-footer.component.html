<button-standard (onAction)="service.prevStep()" class="back-button" *ngIf="service.formStep != 1">
    <svg xmlns="http://www.w3.org/2000/svg" width="16" height="13" viewBox="0 0 16 13" fill="none">
        <path d="M15.2134 5.54389H2.66023L6.93207 1.33322C7.24094 1.03018 7.24248 0.535256 6.93668 0.230712C6.63088 -0.0753749 6.13144 -0.0768952 5.82413 0.226144L0.198231 5.75837C0.161351 5.7934 0.129081 5.83299 0.0998831 5.87563V5.89847C0.0752961 5.9335 0.056854 5.97157 0.0399538 6.01116V6.03857C0.0399538 6.07816 0.0122934 6.11623 0.00461006 6.15582C-0.00153669 6.2076 -0.00153669 6.25938 0.00461006 6.31115V6.35074C0.00307347 6.38882 0.00307347 6.42537 0.00461006 6.46343C0.00461006 6.49846 0.0245871 6.53044 0.0322704 6.56089C0.035344 6.57764 0.035344 6.5944 0.0322704 6.61115L0.0829826 6.70404L0.106032 6.74363H0.10757C0.13523 6.78322 0.165964 6.82129 0.198234 6.85784L5.79935 12.4283C6.10976 12.7085 6.58766 12.6978 6.88424 12.4039C7.18082 12.11 7.19465 11.638 6.91344 11.3288L2.64907 7.10294H15.2132C15.6481 7.10294 16 6.75421 16 6.32325C16 5.8923 15.6482 5.54389 15.2134 5.54389Z" fill="black"/>
    </svg>
    <span>Back to Step {{this.service.formStep - 1 | digitToWord: true}}</span>
</button-standard>

<div *ngIf="!service.hasError" class="next-step-flavor-text">{{service.nextStepFlavorTextArr[service.formStep - 1]}}</div>
<span *ngIf="service.hasError" class="next-step-flavor-text error-message">{{service.errorMessage}}</span>

<button-standard (onAction)="service.nextStep()" class="yellow next-button" *ngIf="service.formStep < service.totalSteps && !service.editing">
    <span>Next</span>
    <svg xmlns="http://www.w3.org/2000/svg" width="16" height="13" viewBox="0 0 16 13" fill="none">
        <path d="M0.786646 7.45611L13.3398 7.45611L9.06793 11.6668C8.75906 11.9698 8.75752 12.4647 9.06332 12.7693C9.36912 13.0754 9.86856 13.0769 10.1759 12.7739L15.8018 7.24163C15.8386 7.2066 15.8709 7.16701 15.9001 7.12437V7.10153C15.9247 7.0665 15.9431 7.02843 15.96 6.98884V6.96143C15.96 6.92184 15.9877 6.88377 15.9954 6.84418C16.0015 6.7924 16.0015 6.74062 15.9954 6.68885V6.64926C15.9969 6.61118 15.9969 6.57463 15.9954 6.53657C15.9954 6.50154 15.9754 6.46956 15.9677 6.43911C15.9647 6.42236 15.9647 6.4056 15.9677 6.38885L15.917 6.29596L15.894 6.25637H15.8924C15.8648 6.21678 15.834 6.17871 15.8018 6.14216L10.2007 0.571726C9.89024 0.291528 9.41234 0.302186 9.11576 0.596091C8.81918 0.889996 8.80535 1.36205 9.08656 1.67119L13.3509 5.89706L0.786784 5.89706C0.35189 5.89706 9.53674e-07 6.24579 9.53674e-07 6.67675C9.53674e-07 7.1077 0.351764 7.45611 0.786646 7.45611Z" fill="black"/>
    </svg>
</button-standard>

<button-standard (onAction)="service.reviewStep()" class="yellow next-button" *ngIf="service.formStep < service.totalSteps && service.editing">
    <span>Review and Save</span>
    <svg xmlns="http://www.w3.org/2000/svg" width="16" height="13" viewBox="0 0 16 13" fill="none">
        <path d="M0.786646 7.45611L13.3398 7.45611L9.06793 11.6668C8.75906 11.9698 8.75752 12.4647 9.06332 12.7693C9.36912 13.0754 9.86856 13.0769 10.1759 12.7739L15.8018 7.24163C15.8386 7.2066 15.8709 7.16701 15.9001 7.12437V7.10153C15.9247 7.0665 15.9431 7.02843 15.96 6.98884V6.96143C15.96 6.92184 15.9877 6.88377 15.9954 6.84418C16.0015 6.7924 16.0015 6.74062 15.9954 6.68885V6.64926C15.9969 6.61118 15.9969 6.57463 15.9954 6.53657C15.9954 6.50154 15.9754 6.46956 15.9677 6.43911C15.9647 6.42236 15.9647 6.4056 15.9677 6.38885L15.917 6.29596L15.894 6.25637H15.8924C15.8648 6.21678 15.834 6.17871 15.8018 6.14216L10.2007 0.571726C9.89024 0.291528 9.41234 0.302186 9.11576 0.596091C8.81918 0.889996 8.80535 1.36205 9.08656 1.67119L13.3509 5.89706L0.786784 5.89706C0.35189 5.89706 9.53674e-07 6.24579 9.53674e-07 6.67675C9.53674e-07 7.1077 0.351764 7.45611 0.786646 7.45611Z" fill="black"/>
    </svg>
</button-standard>

<button-standard (onAction)="save()" class="yellow next-button" *ngIf="service.formStep === service.totalSteps">
    <span>Finish and Save</span>
    <svg xmlns="http://www.w3.org/2000/svg" width="16" height="13" viewBox="0 0 16 13" fill="none">
        <path d="M0.786646 7.45611L13.3398 7.45611L9.06793 11.6668C8.75906 11.9698 8.75752 12.4647 9.06332 12.7693C9.36912 13.0754 9.86856 13.0769 10.1759 12.7739L15.8018 7.24163C15.8386 7.2066 15.8709 7.16701 15.9001 7.12437V7.10153C15.9247 7.0665 15.9431 7.02843 15.96 6.98884V6.96143C15.96 6.92184 15.9877 6.88377 15.9954 6.84418C16.0015 6.7924 16.0015 6.74062 15.9954 6.68885V6.64926C15.9969 6.61118 15.9969 6.57463 15.9954 6.53657C15.9954 6.50154 15.9754 6.46956 15.9677 6.43911C15.9647 6.42236 15.9647 6.4056 15.9677 6.38885L15.917 6.29596L15.894 6.25637H15.8924C15.8648 6.21678 15.834 6.17871 15.8018 6.14216L10.2007 0.571726C9.89024 0.291528 9.41234 0.302186 9.11576 0.596091C8.81918 0.889996 8.80535 1.36205 9.08656 1.67119L13.3509 5.89706L0.786784 5.89706C0.35189 5.89706 9.53674e-07 6.24579 9.53674e-07 6.67675C9.53674e-07 7.1077 0.351764 7.45611 0.786646 7.45611Z" fill="black"/>
    </svg>
</button-standard>