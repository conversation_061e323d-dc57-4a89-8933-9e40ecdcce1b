import { Component, inject } from '@angular/core';
import { NgIf } from '@angular/common';
import { InputTextComponent } from '../input-text/input-text.component';
import { InputTextAreaComponent } from '../input-text-area/input-text-area.component';
import { InputRadioBlockComponent } from '../input-radio-block/input-radio-block.component';
import { PanelsService } from '../../services/panels.service';
import { FormControl, ReactiveFormsModule } from '@angular/forms';
import { InputBsDatepickerComponent } from '../input-bs-datepicker/input-bs-datepicker.component';
import { InputSelectComponent } from '../input-select/input-select.component';


@Component({
  selector: 'new-panel-step-1',
  standalone: true,
  imports: [InputTextComponent, InputTextAreaComponent, InputRadioBlockComponent, ReactiveFormsModule, NgIf, InputBsDatepickerComponent, InputSelectComponent],
  templateUrl: './new-panel-step-1.component.html',
  styleUrl: './new-panel-step-1.component.scss'
})
export class NewPanelStep1Component {

  panelsService = inject(PanelsService);

  form = this.panelsService.step1Form;
  experimentTypeControl = this.form.get('experiment_type')! as FormControl;
  nameControl = this.form.get('name')! as FormControl;
  descriptionControl = this.form.get('description')! as FormControl;
  startOptionControl = this.form.get('startOption')! as FormControl;
  startDateControl = this.form.get('start_date')! as FormControl;
  endOptionControl = this.form.get('endOption')! as FormControl;
  endDateControl = this.form.get('end_date')! as FormControl;

  experimentTypeOptions = [
    { id: 'Normal Sensory', name: 'Normal Sensory' },
    { id: 'Triangle Test', name: 'Triangle Test' }
  ];

}
