import { Component, inject } from '@angular/core';
import { GroupsService } from '../../services/groups.service';
import {
  FormArray,
  FormBuilder,
  FormControl,
  FormGroup,
  ReactiveFormsModule,
  Validators,
} from '@angular/forms';
import { CommonModule } from '@angular/common';
import { InputRadioBlockComponent } from '../input-radio-block/input-radio-block.component';
import { InputTextAreaComponent } from '../input-text-area/input-text-area.component';
import { InputTextComponent } from '../input-text/input-text.component';

import { TABLE_STYLES } from '../../config/table-styles.config';
import {
  LambTableComponent,
  HeaderCell,
} from '@lamb-sensory/lamb-component-library';
import { ConstantsService } from '../../services/constants.service';
import { PanelsService } from '../../services/panels.service';
import { ProductsService } from '../../services/products.service';
import { combineLatest, take, firstValueFrom } from 'rxjs';
import { ProfileService } from '../../services/profile.service';

interface Panel {
  id: string;
  name: string;
  status: string;
  selected: boolean;
  sampleId: string;
}

@Component({
  selector: 'new-group-step1',
  standalone: true,
  imports: [
    ReactiveFormsModule,
    CommonModule,
    InputTextComponent,
    InputTextAreaComponent,
    InputRadioBlockComponent,
    LambTableComponent
  ],
  templateUrl: './new-group-step1.component.html',
  styleUrl: './new-group-step1.component.scss',
})
export class NewGroupStep1Component {
  groupsService = inject(GroupsService);
  panelsService = inject(PanelsService);
  productsService= inject(ProductsService);
  profileService = inject(ProfileService);
  constantsService = inject(ConstantsService);
  form = this.groupsService.step1Form;

  nameControl: FormControl = this.form.get('name') as FormControl;
  descriptionControl: FormControl = this.form.get('description') as FormControl;
  usersControl = this.form.get('users') as FormArray;
  samplesControl = this.form.get('samples') as FormArray;
  panelsControl = this.form.get('panels') as FormArray;

  searchControl = new FormControl<string>('');
  filterControl = new FormControl<string>('');

panels: any[] = [];
products: any[] = [];
users: any[] = [];

  activeTab: string = 'panels';

  tableHeaders:HeaderCell[] = [];
  tableData:any[] = [];
  tableStyles = structuredClone(TABLE_STYLES);

  // State for panels pagination
  panelsState = {
    panels: [] as any[],
    totalCount: 0,
    hasMore: true,
    lastLoadedPage: 0,
    pageSize: 15
  };

  // State for samples/products pagination
  samplesState = {
    samples: [] as any[],
    totalCount: 0,
    hasMore: true,
    lastLoadedPage: 0,
    pageSize: 15
  };

  // State for users pagination
  usersState = {
    users: [] as any[],
    totalCount: 0,
    hasMore: true,
    lastLoadedPage: 0,
    pageSize: 15
  };

  // Loading states
  isLoadingMorePanels = false;
  isLoadingMoreSamples = false;
  isLoadingMoreUsers = false;

  async ngOnInit() {  
    // Initialize the first page of data for each type
    await this.loadInitialData();
  }

  async loadInitialData() {
    // Set initial loading states
    this.isLoadingMorePanels = true;
    this.isLoadingMoreSamples = true;
    this.isLoadingMoreUsers = true;
    
    try {
      // Load first page of data for each type
      await Promise.all([
        this.loadMorePanels(),
        this.loadMoreSamples(),
        this.loadAllUsers()
      ]);
      
      // Initialize the form with the loaded data
      this.checkDataFormArray().then(() => {
        this.initForm();
      });
    } finally {
      this.isLoadingMorePanels = false;
      this.isLoadingMoreSamples = false;
      this.isLoadingMoreUsers = false;
    }
  }

  async checkDataFormArray() {
    let users = this.usersControl.value;
    let samples = this.samplesControl.value;
    let panels = this.panelsControl.value;

    this.buildDataFromArray(panels, samples, users);

    // console.log(users, samples, panels);
    return;
  }

  buildDataFromArray(panels: any, samples: any, users: any): void {
    console.log('hello', users, samples, panels);

    this.usersControl.clear();
    users.forEach((user: any) => {
      this.usersControl.push(new FormControl(user));
    });
    console.log('this.usersControl', this.usersControl);
    this.samplesControl.clear();
    samples.forEach((sample: any) => {
      this.samplesControl.push(new FormControl(sample));
    });
    this.panelsControl.clear();
    panels.forEach((panel: any) => {
      this.panelsControl.push(new FormControl(panel));
    });
  }

  setActiveTab(tab: string) {
    this.activeTab = tab;
    
    // When switching tabs, ensure the form array for that tab is populated
    switch(tab) {
      case 'panels':
        if (this.panelsFormArray.length === 0 && this.panelsState.panels.length > 0) {
          this.panelsState.panels.forEach(panel => {
            this.panelsFormArray.push(this.createPanelFormGroup('panels', panel));
          });
        }
        break;
      case 'samples':
        if (this.samplesFormArray.length === 0 && this.samplesState.samples.length > 0) {
          this.samplesState.samples.forEach(product => {
            this.samplesFormArray.push(this.createPanelFormGroup('samples', product));
          });
        }
        break;
      case 'users':
        if (this.usersFormArray.length === 0 && this.users.length > 0) {
          this.users.forEach(user => {
            this.usersFormArray.push(this.createPanelFormGroup('users', user));
          });
        }
        break;
    }
  }

  getTableHeaders() {
    return [
      {
        value: 'Select',
        key: 'select',
      },
      {
        value: 'Status',
        key: 'status',
      },
      {
        value: 'Name',
        key: 'name',
      },
      {
        value: 'Sample',
        key: 'sample',
      },
    ];
  }

  async getTableData() {
    // const tableData: any[] = samplePanels.map((panel: any) => {
    //   return {
    //     select: 'input',
    //     status: this.getEndDate(panel),
    //     name: panel.name,
    //     sample: panel.product.name,
    //   }
      // return [
      //   {
      //     value: 'input',
      //     key: 'select',
      //   },
      //   {
      //     value: this.getEndDate(panel),
      //     key: 'status',
      //   },
      //   {
      //     value: panel.name,
      //     key: 'name',
      //   },
      //   {
      //     value: panel.product.name,
      //     key: 'sample',
      //   },
      // ];
    // });

    // return tableData;
  }

  getEndDate(panel: any) {
    const date = new Date(panel.end_date._seconds * 1000);
    if (date.getFullYear() > 4000) {
      return 'Ongoing';
    }
    return date.toLocaleDateString();
  }

  async configureTable() {
    // const headerData = this.getTableHeaders();
    // const bodyData = await this.getTableData();
    // this.tableHeaders = headerData;
    // this.tableData = bodyData;
  }

  mockPanels: Panel[] = Array(3)
    .fill(null)
    .map((_, index) => ({
      id: `panel-${index}`,
      name: 'Panel Name Placeholder',
      status: 'Ongoing',
      selected: true, // Pre-select two panels as shown in the image
      sampleId: 'sample-1',
    }));

  fb = inject(FormBuilder);

  groupArraysForm = this.fb.group({
    panels: this.fb.array([]),
    samples: this.fb.array([]),
    users: this.fb.array([]),
  });

  initForm(): void {
    // Populate the form array with panel data
    // this.mockPanels.forEach(panel => {
    (this.groupArraysForm.get('panels') as FormArray).clear();
    (this.groupArraysForm.get('samples') as FormArray).clear();
    (this.groupArraysForm.get('users') as FormArray).clear();

    this.panels.forEach((panel) => {
      this.panelsFormArray.push(this.createPanelFormGroup('panels', panel));
    });

    this.products.forEach((product) => {
      this.samplesFormArray.push(this.createPanelFormGroup('samples', product));
    });

    this.users.forEach((user) => {
      this.usersFormArray.push(this.createPanelFormGroup('users', user));
    });
  }

  get panelsFormArray(): FormArray {
    return this.groupArraysForm.get('panels') as FormArray;
  }

  get samplesFormArray(): FormArray {
    return this.groupArraysForm.get('samples') as FormArray;
  }
  get usersFormArray(): FormArray {
    return this.groupArraysForm.get('users') as FormArray;
  }

  createPanelFormGroup(type: string, data: any): FormGroup {
    // Check if we're in edit mode and have editing data
    const isEditing = this.groupsService.editing;
    let isSelected = false;
    
    if (isEditing && this.groupsService.editingGroup) {
      // Check if this item exists in the editingGroup based on type
      if (type === 'panels' && this.groupsService.editingGroup.panels) {
        isSelected = this.groupsService.editingGroup.panels.some(
          (panel: any) => panel === data.id
        );
      } else if (type === 'samples' && this.groupsService.editingGroup.samples) {
        isSelected = this.groupsService.editingGroup.samples.some(
          (sample: any) => sample === data.product_id);
      } else if (type === 'users' && this.groupsService.editingGroup.users) {
        isSelected = this.groupsService.editingGroup.users.some(
          (user: any) => user === data.uid);
      }
    }

    if (type === 'panels') {
      return this.fb.group({
        id: [data.id],
        name: [data.name, Validators.required],
        status: [this.getEndDate(data)],
        selected: [isSelected],
      });
    } else if (type === 'samples') {
      return this.fb.group({
        id: [data.id],
        name: [data.name, Validators.required],
        type: [
          this.constantsService.matrixObject[data.product_type]?.name ||
            'Unknown',
        ],
        selected: [isSelected],
      });
    } else {
      return this.fb.group({
        id: [data.uid],
        name: [data.first_name + ' ' + data.last_name, Validators.required],
        email: [data.email],
        selected: [isSelected],
      });
    }
  }

  toggleSelection(index: number): void {
    console.log(index);
    let control;

    if (this.activeTab === 'panels') {
      control = this.panelsFormArray.at(index).get('selected');
    } else if (this.activeTab === 'samples') {
      control = this.samplesFormArray.at(index).get('selected');
    } else {
      control = this.usersFormArray.at(index).get('selected');
    }
    control?.setValue(!control.value);
    this.submitForm();
  }

  submitForm(): void {
    if (this.groupArraysForm.valid) {
      // Get only the selected panels
      const selectedPanels = this.panelsFormArray.controls
        .filter((control) => control.get('selected')?.value)
        .map((control) => control.value);

      const selectedSamples = this.samplesFormArray.controls
        .filter((control) => control.get('selected')?.value)
        .map((control) => control.value);

      const selectedUsers = this.usersFormArray.controls
        .filter((control) => control.get('selected')?.value)
        .map((control) => control.value);

      this.buildDataFromArray(selectedPanels, selectedSamples, selectedUsers);

      console.log('Selected coll:', {
        selectedPanels,
        selectedSamples,
        selectedUsers,
      });
      // Here you would typically send the data to your backend
    }
  }

  async loadMorePanels() {
    try {
      this.isLoadingMorePanels = true;
      const nextPage = this.panelsState.lastLoadedPage + 1;
      
      const response: any = await firstValueFrom(
        this.panelsService.getPanels({
          page: nextPage,
          limit: this.panelsState.pageSize
        })
      );

      // Update panels state
      this.panelsState.panels = [...this.panelsState.panels, ...response.panels];
      this.panelsState.lastLoadedPage = nextPage;
      this.panelsState.hasMore = response.hasMore;
      this.panelsState.totalCount = response.totalCount;
      
      // Update the component's panels array
      this.panels = this.panelsState.panels;
      
      // Add new panels to form arrays if they're not there already
      if (this.activeTab === 'panels') {
        response.panels.forEach((panel: any) => {
          this.panelsFormArray.push(this.createPanelFormGroup('panels', panel));
        });
      }
      
      return response;
    } catch (error) {
      console.error('Error loading more panels:', error);
    } finally {
      this.isLoadingMorePanels = false;
    }
  }

  async loadMoreSamples() {
    try {
      this.isLoadingMoreSamples = true;
      const nextPage = this.samplesState.lastLoadedPage + 1;
      
      const response: any = await firstValueFrom(
        this.productsService.getProducts({
          page: nextPage,
          limit: this.samplesState.pageSize
        })
      );

      // Update samples state
      this.samplesState.samples = [...this.samplesState.samples, ...response.products];
      this.samplesState.lastLoadedPage = nextPage;
      this.samplesState.hasMore = response.hasMore;
      this.samplesState.totalCount = response.totalCount;
      
      // Update the component's products array
      this.products = this.samplesState.samples;
      
      // Add new samples to form arrays if they're not there already
      if (this.activeTab === 'samples') {
        response.products.forEach((product: any) => {
          this.samplesFormArray.push(this.createPanelFormGroup('samples', product));
        });
      }
      
      return response;
    } catch (error) {
      console.error('Error loading more samples:', error);
    } finally {
      this.isLoadingMoreSamples = false;
    }
  }

  async loadAllUsers() {
    try {
      this.isLoadingMoreUsers = true;
      // const nextPage = this.usersState.lastLoadedPage + 1;
      
      // const response: any = await firstValueFrom(
      //   this.profileService.getUsersListInMyOrg({
      //     page: nextPage,
      //     limit: this.usersState.pageSize
      //   })
      // );

      const response: any = await firstValueFrom((await this.profileService.getUsersListInMyOrg()).pipe(take(1)));
      console.log('response', response);
      const users = response as any[];

      // // Update samples state
      this.usersState.users = [...this.usersState.users, ...users];
      this.usersState.lastLoadedPage = 0;
      this.usersState.hasMore = false;
      this.usersState.totalCount = users.length;
      
      // Update the component's products array
      this.users = this.usersState.users;
      
      // Add new samples to form arrays if they're not there already
      if (this.activeTab === 'users') {
        users.forEach((user: any) => {
          this.usersFormArray.push(this.createPanelFormGroup('users', user));
        });
      }
      
      return response;
    } catch (error) {
      console.error('Error loading more users:', error);
      return;
    } finally {
      this.isLoadingMoreUsers = false;
      return;
    }
  }
  
  get currentState() {
    switch (this.activeTab) {
      case 'panels':
        return {
          data: this.panelsState.panels,
          hasMore: this.panelsState.hasMore,
          isLoading: this.isLoadingMorePanels,
          loadMore: () => this.loadMorePanels()
        };
      case 'samples':
        return {
          data: this.samplesState.samples,
          hasMore: this.samplesState.hasMore,
          isLoading: this.isLoadingMoreSamples,
          loadMore: () => this.loadMoreSamples()
        };
      case 'users':
        return {
          data: this.usersState.users,
          hasMore: this.usersState.hasMore,
          isLoading: this.isLoadingMoreUsers,
          loadMore: () => Promise.resolve()
        };
      default:
        return {
          data: [],
          hasMore: false,
          isLoading: false,
          loadMore: () => Promise.resolve()
        };
    }
  }

  isItemInEditingGroup(type: string, id: string): boolean {
    if (!this.groupsService.editing || !this.groupsService.editingGroup) {
      return false;
    }

    const editGroup = this.groupsService.editingGroup;
    
    if (type === 'panels' && editGroup.panels) {
      return editGroup.panels.some((panel: any) => panel.id === id);
    } else if (type === 'samples' && editGroup.samples) {
      return editGroup.samples.some((sample: any) => sample.id === id);
    } else if (type === 'users' && editGroup.users) {
      return editGroup.users.some((user: any) => user.id === id || user.uid === id);
    }
    
    return false;
  }
}
