import { Directive, ElementRef, HostListener, Input } from '@angular/core';

@Directive({
  selector: '[dollarsAndCents]',
  standalone: true
})
export class DollarsAndCentsDirective {
  @Input() set dollarsAndCents(value: boolean) {
    this.enabled = value;
  }
  private enabled: boolean = false;

  constructor(private el: ElementRef) { }

  @HostListener('input', ['$event']) 
  onInputChange(event: InputEvent) {
    if (!this.enabled) return;
    
    const initialValue = this.el.nativeElement.value;
    const newValue = initialValue?.replace(/[^0-9.]/g, '')
                                .replace(/(\..*)\./g, '$1')
                                .replace(/(\.[\d]{2})./g, '$1');
    
    this.el.nativeElement.value = newValue;
    
    if (initialValue !== this.el.nativeElement.value) {
      event.stopPropagation();
    }
  }

  @HostListener('keypress', ['$event']) 
  onKeyPress(event: KeyboardEvent) {
    if (!this.enabled) return;
    
    const currentValue = this.el.nativeElement.value || '';
    
    if (event.key === '.' && currentValue.includes('.')) {
      event.preventDefault();
      return;
    }
    
    if (!/^[0-9.]$/.test(event.key)) {
      event.preventDefault();
    }
  }
}
