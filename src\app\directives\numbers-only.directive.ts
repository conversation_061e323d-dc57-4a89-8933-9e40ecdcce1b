import { Directive, ElementRef, HostListener, Input } from '@angular/core';

@Directive({
  selector: '[numbersOnly]',
  standalone: true
})
export class NumbersOnlyDirective {
  @Input() set numbersOnly(value: boolean) {
    this.enabled = value;
  }
  private enabled: boolean = false;

  constructor(private el: ElementRef) { }

  @HostListener('input', ['$event']) 
  onInputChange(event: InputEvent) {
    if (!this.enabled) return;
    
    const initialValue = this.el.nativeElement.value;
    const newValue = initialValue?.replace(/[^0-9]/g, '');
    
    this.el.nativeElement.value = newValue;
    
    if (initialValue !== this.el.nativeElement.value) {
      event.stopPropagation();
    }
  }

  @HostListener('keypress', ['$event']) 
  onKeyPress(event: KeyboardEvent) {
    if (!this.enabled) return;
    
    if (!/^[0-9]$/.test(event.key)) {
      event.preventDefault();
    }
  }
}
