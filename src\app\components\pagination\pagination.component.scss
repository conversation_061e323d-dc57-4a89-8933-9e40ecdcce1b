@import '../../../_colors.scss';

:host {
    width: 100%;
    display: flex;
    justify-content: center;
    align-items: center;
    margin: 70px 0 40px 0;
    font-size: 13px;

  .pagination-wrap {
    .pagination {
      display: flex;
      align-items: center;
      gap: 32px;

      .main {
        display: flex;
        align-items: center;
        gap: 8px;

        .navigate {
          display: flex;
          align-items: center;
          justify-content: center;
          cursor: pointer;
          padding: 8px;
          border-radius: 100%;
         border: 1px solid $mediumLightGray;
         width: 40px;
         height: 40px;

          &:hover {
            background: $lightestGray;
          }

          &.inactive {
            cursor: default;
            opacity: 0.35;

            &:hover {
              background: none;
            }
          }
        }

        .specific-page {
          display: flex;
          align-items: center;
          gap: 4px;
          border-radius: 12px;
          border: 1px solid $mediumLightGray;
          padding: 3px;

          div {
            min-width: 34px;
            height: 34px;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            border-radius: 10px;
            
            &:hover {
              background: $lightestGray;
            }

            &.active {
              background: $lightestGray;
              border: 1px solid $mediumLightGray;
              color: black;
            }

            &.skip {
              cursor: default;
              color: $mediumLightGray;
              
              &:hover {
                background: none;
              }
            }
          }
        }
      }

      .jump {
        input {
          border: 1px solid $mediumLightGray;
          border-radius: 12px;
          width: 70px;
          padding: 12px;
          margin-left: 8px;
        }
      }
    }
  }
}