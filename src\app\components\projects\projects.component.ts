import { Component, inject, TemplateRef, ViewChild, ViewContainerRef, ElementRef } from '@angular/core';
import { NgIf, AsyncPipe } from '@angular/common';
import { PageHeaderComponent } from '../page-header/page-header.component';
import { ProjectsService } from '../../services/projects.service';
import { PopupService } from '../../services/popup.service';
import { LambTableComponent, HeaderCell, LambGPIconComponent, GraphTooltipComponent, GraphTooltipConfig } from '@lamb-sensory/lamb-component-library';
import { PaginationComponent } from '../pagination/pagination.component';
import { ProjectAdapter } from '../../adapters/project.adapter';
import { LoadingService } from '../../services/loading.service';
import { TABLE_STYLES } from '../../config/table-styles.config';
import { Router } from '@angular/router';
import { ProjectInterface } from '../../interfaces/project.interface';
import { PercentageBarComponent } from '../percentage-bar/percentage-bar.component';
import { ApiService } from '../../services/api.service';
import { map, Observable, share, switchMap, tap } from 'rxjs';
import { ConstantsService } from '../../services/constants.service';
import { fadeInOut } from '../../modules/animations/animations.module';

@Component({
  selector: 'projects',
  standalone: true,
  imports: [AsyncPipe, NgIf,PageHeaderComponent, LambTableComponent, PaginationComponent, LambGPIconComponent, PercentageBarComponent, GraphTooltipComponent],
  templateUrl: './projects.component.html',
  styleUrl: './projects.component.scss',
  animations: [fadeInOut]
})

export class ProjectsComponent {
  projectsService = inject(ProjectsService);
  popupService = inject(PopupService);
  loadingService = inject(LoadingService);
  router = inject(Router);
  apiService = inject(ApiService);
  constantsService = inject(ConstantsService);
  tableStyles = TABLE_STYLES;
  tableHeaders: HeaderCell[] = [
    {
      value: 'Status',
      key: 'status'
    },
    {
      value: 'Project Name',
      key: 'projectName'
    },
    {
      value: 'Samples',
      key: 'associatedSampleCount'
    },
    {
      value: 'Privacy',
      key: 'privacy'
    },
    {
      value: 'Owner',
      key: 'owner'
    },
    {
      value: 'Scheduled End',
      key: 'endDate'
    },
    {
      value: '% Completed',
      key: 'completionPercentage'
    }
  ]


  // Shared source Observable
  private projectsSource$!: Observable<any>;
  projects$!: Observable<any>;
  paginationState$!: Observable<any>;


  @ViewChild('statusCellTemplate') statusCellTemplate!: TemplateRef<any>;
  @ViewChild('ownerCellTemplate') ownerCellTemplate!: TemplateRef<any>;

  activeTooltipItem: any = null;
  tooltipPosition = { x: 0, y: 0 };
  private viewContainerRef = inject(ViewContainerRef);
  @ViewChild('percentageBar', { read: ElementRef }) percentageBarRef!: ElementRef;

  constructor(){
    this.loadingService.setLoading('projects', true);
  }

  ngOnInit(){
    this.getSummaryMetrics();
    // Create a shared source observable
    this.projectsSource$ = this.projectsService.projects$.pipe(
      tap(() => this.loadingService.setLoading('projects', false)),
      share()
    );
    
    // Derive projects$ from the shared source
    this.projects$ = this.projectsSource$.pipe(
      switchMap(async (data) => {
        this.projectsService.projectsArray = await this.getTableData(data['projects']);
        return data['projects'];
      })
    );
    
    // Derive paginationState$ from the same shared source
    this.paginationState$ = this.projectsSource$.pipe(
      map(response => ({
        currentPage: response.currentPage,
        totalPages: response.totalPages,
        hasMore: response.hasMore
      }))
    );
    this.setTableHeaderColors();
  }

  getSummaryMetrics(){
    // console.log('hello');
    
    this.apiService.getProjectDashboardStats().subscribe((data:any) => {
      console.log('data', data);
      this.projectsService.summaryMetrics = {
        openProjects: data.openCount,
        endingSoon: data.endingSoonCount,
        completed: data.completedCount,
        productTypeDistribution: this.getProductTypeDistribution(data.productDistribution)
      }
      console.log('projectsService.summaryMetrics', this.projectsService.summaryMetrics);
    })
  }

  getProductTypeDistribution(data:any){

    return data.map((item:any) => {
      return {
        name: this.constantsService.matrixObject[item.type].name,
        value: item.percentage,
        color: this.getColorFromProductType(item.type)
      }
    })
  }

  getColorFromProductType(type: string) {
    // Get a consistent color from any string
    const stringToColor = (str: string, options = {
      lightness: 65,
      chroma: 0.15,
      lchChroma: 50,
      hslSat: 65
    }): string => {
      // Handle empty strings
      if (!str || str.length === 0) return `hsl(0, ${options.hslSat}%, 45%)`;
      
      // Create a hash from the entire string
      let hash = 0;
      for (let i = 0; i < str.length; i++) {
        hash = str.charCodeAt(i) + ((hash << 5) - hash);
      }
      
      // Convert hash to a hue value (0-359)
      const hue = ((hash % 360) + 360) % 360;
      
      const lightness = options.lightness;  // Consistent across all color spaces
      const chroma = options.chroma;   // OKLCH chroma (0-0.4 typical range)
      const lchChroma = options.lchChroma;  // LCH chroma (0-132 range)
      const hslSat = options.hslSat;     // HSL saturation (0-100)

      // Try OKLCH first
      if (CSS.supports('color', 'oklch(65% 0.15 0)')) {
        return `oklch(${lightness}% ${chroma} ${hue})`;
      }
      // Try LCH as first fallback
      else if (CSS.supports('color', 'lch(65% 50 0)')) {
        return `lch(${lightness}% ${lchChroma} ${hue})`;
      }
      // HSL as final fallback
      else {
        return `hsl(${hue}, ${hslSat}%, 45%)`;
      }
    };
    
    return stringToColor(type);
  }

  setTableHeaderColors(){
    this.tableStyles = structuredClone(TABLE_STYLES);
    this.tableStyles.header!.style = {
      background: '#E6FFE9',
      color: "#386E3F"
    }
  }

  async getTableData(data: Record<string, Object>[]){
    const adapter = new ProjectAdapter();
    let projects = await adapter.adapt(data);
    this.insertCustomCells(projects);
    return projects;
  }

  insertCustomCells(projects: ProjectInterface[]){
    return projects.forEach((project) => {
      this.insertCustomStatusCell(project);
      this.insertCustomOwnerCell(project);
    })
  }

  insertCustomStatusCell(project: ProjectInterface){
    project['status__template'] = this.statusCellTemplate;
    project['status__templateContext'] = {
      $implicit: project
    }
  }

  insertCustomOwnerCell(project: ProjectInterface){
    project['owner__template'] = this.ownerCellTemplate;
    project['owner__templateContext'] = {
      $implicit: project
    }
  }

  onPageChange(page: number) {
    this.projectsService.setPage(page);
  }

  onRowClick(event: any){
    let id = event.cell.data.id;
    this.router.navigate([`/projects/${id}`]);
  }

  getColorFromStatus(status: string){
    status = status.toLowerCase();
    switch(status){
      case 'ongoing':
        return '#40BB44';
      case 'scheduled':
        return '#FEC33C';
      case 'ended':
      case 'completed':
        return '#0084FF';
      default:
        return '#E34E4E';
    }
  }

  showTooltip(event: MouseEvent) {
    console.log('event', event);
    
    const target = event.target as HTMLElement;
    
    // Only proceed if we're hovering over a percentage bar piece
    if (!target.classList.contains('percentage-bar-piece')) {
      this.hideTooltip();
      return;
    }
    
    // Find the index of the piece in the parent element's children
    const pieces = target.parentElement?.children || [];
    const pieceIndex = Array.from(pieces).indexOf(target);
    
    // Get the piece data
    const piece = this.projectsService.summaryMetrics.productTypeDistribution[pieceIndex];
    if (!piece) return;

    const rect = target.getBoundingClientRect();
    
    console.log('target', target, target.offsetLeft);
    console.log('target.parentElement', target.parentElement);
    
    this.tooltipPosition = {
      x: target.offsetLeft + (rect.width / 2),
      y: 33
    };
    
    this.activeTooltipItem = piece;
  }
  
  hideTooltip() {
    this.activeTooltipItem = null;
  }
}