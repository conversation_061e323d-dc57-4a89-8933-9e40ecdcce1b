<div class="table-header">
    <div class="table-header-cell">Status</div>
    <div class="table-header-cell">Name</div>
    <div class="table-header-cell">Product Name</div>
    <div class="table-header-cell">Privacy</div>
    <!-- <div class="table-header-cell">Sentiment</div> -->
    <div class="table-header-cell">Scheduled End</div>
    <div class="table-header-cell"># Responses</div>
</div>

<div class="table-body">
    <a *ngFor="let panel of panels" [routerLink]="['/panels', panel.id]">
        <panels-table-row [panel]="panel"></panels-table-row>
    </a>
</div>

<!-- <router-outlet></router-outlet> -->
