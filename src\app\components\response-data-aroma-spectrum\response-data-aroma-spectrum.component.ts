import { Component, inject, Input } from '@angular/core';
import { CommonModule } from '@angular/common';
import { AromaSpectrumArrowComponent } from '../aroma-spectrum-arrow/aroma-spectrum-arrow.component';
import { AromaSpectrumBarComponent } from '../aroma-spectrum-bar/aroma-spectrum-bar.component';
import { PanelsService } from '../../services/panels.service';

@Component({
  selector: 'response-data-aroma-spectrum',
  standalone: true,
  imports: [CommonModule, AromaSpectrumArrowComponent, AromaSpectrumBarComponent],
  templateUrl: './response-data-aroma-spectrum.component.html',
  styleUrl: './response-data-aroma-spectrum.component.scss'
})
export class ResponseDataAromaSpectrumComponent {

  @Input() responseDetails!: any;
  sortedDescriptors: any[] = [];
  responseDescriptorsObj: any = {};

  panelsService = inject(PanelsService);

  async ngOnInit(){
    this.sortedDescriptors = await this.panelsService.getAllDescriptors();
    let responseDescriptorsArr = await this.panelsService.getResponseDescriptors(this.responseDetails);
    responseDescriptorsArr = this.panelsService.getResponseDescriptorAverages(responseDescriptorsArr, this.responseDetails);
    this.responseDescriptorsObj = {};
    responseDescriptorsArr.forEach((descriptor: any) => {
      this.responseDescriptorsObj[descriptor.descriptor.option_id] = descriptor;
    });    
  }

}
