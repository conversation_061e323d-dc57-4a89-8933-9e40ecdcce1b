import { ComponentFixture, TestBed } from '@angular/core/testing';

import { ResponseDataMultipleChoiceComponent } from './response-data-multiple-choice.component';

describe('ResponseDataMultipleChoiceComponent', () => {
  let component: ResponseDataMultipleChoiceComponent;
  let fixture: ComponentFixture<ResponseDataMultipleChoiceComponent>;

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      imports: [ResponseDataMultipleChoiceComponent]
    })
    .compileComponents();

    fixture = TestBed.createComponent(ResponseDataMultipleChoiceComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
