@import '../../../_colors'; 

:host{
    display: grid;
    grid-template-columns: 1fr 1fr;
    column-gap: 20px;
    row-gap: 30px;

    .demographic-repeater{
        display: flex;
        gap: 10px;
        flex-direction: column;
        position: relative;

        .demographic-title{
            font-size: .75rem;
            font-weight: 500;
            text-transform: capitalize;
        }

        .percentage-bar-container{
            position: relative;
            width: 100%;
        }

        .demographic-legend{
            display: flex;
            flex-flow: row wrap;
            column-gap: 20px;
            row-gap: 10px;
            font-size: .75rem;

            .demographic-category{
                display: flex;
                align-items: center;
                gap: 6px;
                flex-flow: row nowrap;

                .color-dot{
                    --dot-size: 15px;
                    width: var(--dot-size);
                    height: var(--dot-size);
                    border-radius: 5px;
                    background-color: $mediumGray;
                }
            }
        }
        
    }
}