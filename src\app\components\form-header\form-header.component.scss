@import '../../../_colors';

:host{
    display: grid;
    grid-template-columns: 1fr 1fr;
    justify-content: space-between;
    align-items: center;
    margin: var(--form-header-margin-top) var(--form-header-margin-right) var(--form-header-margin-bottom) var(--form-header-margin-left);
    width: 100%;
    max-width: var(--form-header-max-width);

    .super-header{
        font-size: var(--form-super-header-font-size);
        font-weight: var(--form-super-header-font-weight);
        color: var(--form-super-header-color);
        grid-column: span 2;
        line-height: var(--form-super-header-font-size);
        margin-top: calc(var(--form-super-header-font-size) * -1);
        font-style: italic;
    }

    .header{
        font-size: var(--form-header-font-size);
        font-weight: var(--form-header-font-weight);
        color: var(--form-header-color);

        &.editing{
            grid-column: span 2;
        }
    }
}