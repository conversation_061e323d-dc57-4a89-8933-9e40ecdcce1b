<!-- <div class="remove-button" *ngIf="control!.value && control!.value > 0" (click)="removeBar()">
    <svg xmlns="http://www.w3.org/2000/svg" width="8" height="8" viewBox="0 0 8 8" fill="none">
        <path d="M4 3.19171L7.02451 0.167203C7.24744 -0.0557344 7.60936 -0.0557344 7.8328 0.167203C8.05573 0.390631 8.05573 0.752544 7.8328 0.975494L4.80829 4L7.8328 7.02451C8.05573 7.24744 8.05573 7.60936 7.8328 7.8328C7.60937 8.05573 7.24746 8.05573 7.02451 7.8328L4 4.80829L0.975494 7.8328C0.752556 8.05573 0.390644 8.05573 0.167203 7.8328C-0.0557344 7.60937 -0.0557344 7.24746 0.167203 7.02451L3.19171 4L0.167203 0.975494C-0.0557344 0.752556 -0.0557344 0.390644 0.167203 0.167203C0.390631 -0.0557344 0.752544 -0.0557344 0.975494 0.167203L4 3.19171Z" fill="black"/>
    </svg>
</div> -->

<!-- <input type="range" [formControl]="control!" min="0" max="100" /> -->

<div class="initial-bar"></div>

<div class="fill-bar" [style.height]="value + '%'">
    <lamb-graph-tooltip [config]="tooltipConfig"></lamb-graph-tooltip>
</div>

<!-- <div class="bar-value" *ngIf="control!.value && control!.value > 0" [style.--font-size]="getFontSize(control!.value)" [style.--text-color]="getTextColor(control!.value)">{{control!.value}}</div> -->

<!-- <div class="connector"></div> -->

<!-- <div class="name"><span [class.bold]="control!.value && control!.value > 0"><div class="dot" *ngIf="control!.value && control!.value > 0"></div>{{name}}</span></div> -->

