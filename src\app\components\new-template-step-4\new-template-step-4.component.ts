import { Component, inject } from '@angular/core';
import { CommonModule } from '@angular/common';
import { TemplateFormService } from '../../services/template-form.service';
import { InputRadioStandardComponent } from '../input-radio-standard/input-radio-standard.component';
import { InputRadioBlockComponent } from '../input-radio-block/input-radio-block.component';
import { FormControl, ReactiveFormsModule } from '@angular/forms';

@Component({
  selector: 'new-template-step-4',
  standalone: true,
  imports: [CommonModule, InputRadioStandardComponent, InputRadioBlockComponent, ReactiveFormsModule],
  templateUrl: './new-template-step-4.component.html',
  styleUrl: './new-template-step-4.component.scss'
})
export class NewTemplateStep4Component {
  templateFormService = inject(TemplateFormService);
  form = this.templateFormService.step3Form;
  panelistPoolControl = this.form.get('panelistPool')! as FormControl;
  restrictedControl = this.form.get('restricted')! as FormControl;
  restrictionLevelControl = this.form.get('restrictionLevel')! as FormControl;

  ngOnInit() {
    // Make sure the form controls have the correct values
    console.log('Template step 4 form values:', this.form.value);
    this.panelistPoolControl.setValue('private');
  }
}
