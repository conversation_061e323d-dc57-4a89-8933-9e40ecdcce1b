<button-two-click [type]="'close'" (onConfirm)="close()" class="light-bg">
    <svg width="14" height="14" viewBox="0 0 14 14" fill="none" xmlns="http://www.w3.org/2000/svg">
        <path d="M7 5.58549L12.2929 0.292606C12.683 -0.0975353 13.3164 -0.0975353 13.7074 0.292606C14.0975 0.683605 14.0975 1.31695 13.7074 1.70711L8.41451 7L13.7074 12.2929C14.0975 12.683 14.0975 13.3164 13.7074 13.7074C13.3164 14.0975 12.683 14.0975 12.2929 13.7074L7 8.41451L1.70711 13.7074C1.31697 14.0975 0.683627 14.0975 0.292606 13.7074C-0.0975353 13.3164 -0.0975353 12.683 0.292606 12.2929L5.58549 7L0.292606 1.70711C-0.0975353 1.31697 -0.0975353 0.683627 0.292606 0.292606C0.683605 -0.0975353 1.31695 -0.0975353 1.70711 0.292606L7 5.58549Z" fill="black"/>
    </svg>
</button-two-click>

<button-standard (onAction)="saveQuestion()" class="blue save-button" *ngIf="!questionsService.editing">
    <span>Save</span>
    <svg xmlns="http://www.w3.org/2000/svg" width="16" height="13" viewBox="0 0 16 13" fill="none">
        <path d="M0.786646 7.08795L13.3398 7.08795L9.06793 11.2986C8.75906 11.6017 8.75752 12.0966 9.06332 12.4011C9.36912 12.7072 9.86856 12.7087 10.1759 12.4057L15.8018 6.87346C15.8386 6.83844 15.8709 6.79884 15.9001 6.75621V6.73337C15.9247 6.69834 15.9431 6.66027 15.96 6.62068V6.59327C15.96 6.55367 15.9877 6.5156 15.9954 6.47601C16.0015 6.42424 16.0015 6.37246 15.9954 6.32068V6.28109C15.9969 6.24302 15.9969 6.20647 15.9954 6.1684C15.9954 6.13338 15.9754 6.1014 15.9677 6.07094C15.9647 6.05419 15.9647 6.03744 15.9677 6.02069L15.917 5.9278L15.894 5.88821H15.8924C15.8648 5.84861 15.834 5.81054 15.8018 5.77399L10.2007 0.203562C9.89024 -0.0766363 9.41234 -0.0659781 9.11576 0.227927C8.81918 0.521832 8.80535 0.993884 9.08656 1.30303L13.3509 5.5289L0.786784 5.5289C0.35189 5.5289 9.53674e-07 5.87762 9.53674e-07 6.30858C9.53674e-07 6.73954 0.351764 7.08795 0.786646 7.08795Z" fill="white"/>
    </svg>
</button-standard>

<button-standard (onAction)="addQuestion()" class="blue save-button" *ngIf="questionsService.addingExistingStep">
    <span>Add</span>
    <svg xmlns="http://www.w3.org/2000/svg" width="16" height="13" viewBox="0 0 16 13" fill="none">
        <path d="M0.786646 7.08795L13.3398 7.08795L9.06793 11.2986C8.75906 11.6017 8.75752 12.0966 9.06332 12.4011C9.36912 12.7072 9.86856 12.7087 10.1759 12.4057L15.8018 6.87346C15.8386 6.83844 15.8709 6.79884 15.9001 6.75621V6.73337C15.9247 6.69834 15.9431 6.66027 15.96 6.62068V6.59327C15.96 6.55367 15.9877 6.5156 15.9954 6.47601C16.0015 6.42424 16.0015 6.37246 15.9954 6.32068V6.28109C15.9969 6.24302 15.9969 6.20647 15.9954 6.1684C15.9954 6.13338 15.9754 6.1014 15.9677 6.07094C15.9647 6.05419 15.9647 6.03744 15.9677 6.02069L15.917 5.9278L15.894 5.88821H15.8924C15.8648 5.84861 15.834 5.81054 15.8018 5.77399L10.2007 0.203562C9.89024 -0.0766363 9.41234 -0.0659781 9.11576 0.227927C8.81918 0.521832 8.80535 0.993884 9.08656 1.30303L13.3509 5.5289L0.786784 5.5289C0.35189 5.5289 9.53674e-07 5.87762 9.53674e-07 6.30858C9.53674e-07 6.73954 0.351764 7.08795 0.786646 7.08795Z" fill="white"/>
    </svg>
</button-standard>

<div class="form-header">
    <div class="super-header">{{questionsService.editing? 'Previewing' : 'Creating'}} Step</div>
    <div class="header">{{nameControl.value ? nameControl.value : 'Step Name'}}</div>
</div>

<form>
    <div class="step-details-container">
        <div class="section-label">
            <span>Step Details</span>
            <input-radio-standard [id]="'step-details-required'" [control]="requiredControl" [type]="'checkbox'">Required</input-radio-standard>
        </div>
        <div class="input-container">
            <label for="step-name"><span>Step Name</span></label>
            <input-text [id]="'step-name'" [control]="nameControl" [placeholder]="'step name'"></input-text>
        </div>
        <div class="input-container step-instructions">
            <label for="step-instructions">
                <span>Step Instructions</span>
                <div class="sub-label">OPTIONAL</div>
            </label>
            <input-text-area [id]="'step-instructions'" [control]="stepInstructionsControl" [placeholder]="'instructions'"></input-text-area>
        </div>
        <div class="input-container step-type">
            <label for="step-type">
                <span>Step Type</span>
                <div class="sub-label">Choose one. All questions in this step will be of the same type.</div>
            </label>
            <custom-step-type-radio [id]="'step-type'" [control]="stepTypeControl" [options]="questionsService.stepTypeOptions"></custom-step-type-radio>
        </div>
    </div>

    <div class="step-options-container">
        <div class="section-label">Options</div>

        <ng-container *ngIf="stepTypeControl.value === 'range-slider'">
            <range-slider-option *ngFor="let option of optionsControl.controls; let i = index" [option]="option" [index]="i" [editing]="questionsService.editing" (removeOptionEvent)="removeOption($event)"></range-slider-option>
        </ng-container>

        <ng-container *ngIf="stepTypeControl.value === 'true-false'">
            <div class="no-config">No additional configuration requeired for True/False questions</div>
        </ng-container>

        <ng-container *ngIf="stepTypeControl.value === 'free-form-text'">
            <!-- <free-form-text-option *ngFor="let option of optionsControl.controls" [option]="option"></free-form-text-option> -->
            <div class="no-config">No additional configuration require for Free Form Text questions</div>
        </ng-container>

        <ng-container *ngIf="stepTypeControl.value === 'sensory-spectrum'">
            <div class="aroma-spectrum">
                <div class="label">Sensory Spectrum</div>
                <svg width="474" height="86" viewBox="0 0 474 86" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <rect opacity="0.75" y="20" width="7" height="66" fill="#F106FF"/>
                    <rect opacity="0.75" x="11" y="75" width="7" height="11" fill="#F106FF"/>
                    <rect opacity="0.75" x="23" y="52" width="7" height="34" fill="#F105FF"/>
                    <rect opacity="0.75" x="34" y="75" width="8" height="11" fill="#F106FF"/>
                    <rect opacity="0.75" x="45" y="34" width="9" height="52" fill="#F105FF"/>
                    <rect opacity="0.75" x="57" width="7" height="86" fill="#E70EFF"/>
                    <rect opacity="0.75" x="68" y="59" width="7" height="27" fill="#D222FF"/>
                    <rect opacity="0.75" x="147" y="75" width="8" height="11" fill="#2BB5FB"/>
                    <rect opacity="0.75" x="158" y="42" width="10" height="44" fill="#1AC6FA"/>
                    <rect opacity="0.75" x="204" y="20" width="8" height="66" fill="#27D5EE"/>
                    <rect opacity="0.75" x="273" y="52" width="7" height="34" fill="#82D5A5"/>
                    <rect opacity="0.75" x="375" y="59" width="7" height="27" fill="#F4D149"/>
                    <rect opacity="0.75" x="387" y="6" width="7" height="80" fill="#F4D348"/>
                    <rect opacity="0.75" x="465" y="67" width="9" height="19" fill="#FADF41"/>
                </svg>
            </div>
        </ng-container>

        <ng-container *ngIf="stepTypeControl.value === 'multiple-choice' || stepTypeControl.value === 'select-one'">
            <multiple-choice-option *ngFor="let option of optionsControl.controls; let i = index" [option]="option" [index]="i" [editing]="questionsService.editing" (removeOptionEvent)="removeOption($event)"></multiple-choice-option>
        </ng-container>

        <button class="add-option-button" type="button" (click)="addOption()" *ngIf="canAddOption()">
            <div class="plus-container">
                <svg xmlns="http://www.w3.org/2000/svg" width="10" height="10" viewBox="0 0 10 10" fill="none">
                    <path d="M4.11765 0V4.11765H0V5.88235H4.11765V10H5.88235V5.88235H10V4.11765H5.88235V0H4.11765Z" fill="#5D5D5D"/>
                </svg>
            </div>
            <div class="add-step-text" *ngIf="canAddOption()">{{addOptionText[stepTypeControl.value]}}</div>
        </button>
    </div>
</form>