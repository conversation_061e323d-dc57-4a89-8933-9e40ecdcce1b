<!-- <page-header [pageName]="'Sensory Panels'" [infoVarient]="1" [info]="'info'" (onFilter)="panelsService.filter($event)" (onSearch)="panelsService.search($event)" (onAdd)="popupService.openNewPanel($event)" [addText]="'New Panel'"> -->
<page-header [buttonClass]="'cyan'"  [pageName]="'Groups'" [infoVarient]="1" [info]="'info'" [addText]="'New Group'" (onAdd)="popupService.openNewGroup($event)">
  <svg
    width="30"
    height="42"
    viewBox="0 0 30 42"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path d="M12.2951 0H30L17.7049 42H0L12.2951 0Z" fill="#CEFFFC" />

      <path
        fill-rule="evenodd"
        clip-rule="evenodd"
        d="M19.5456 8.18186C17.7878 8.18186 16.3641 6.75684 16.3641 5.00032C16.3641 3.24247 17.788 1.81877 19.5456 1.81877C21.3023 1.81877 22.7272 3.24262 22.7272 5.00032C22.7272 6.75699 21.3022 8.18186 19.5456 8.18186ZM19.5456 7.27249C20.8007 7.27249 21.8179 6.25529 21.8179 5.0002C21.8179 3.7451 20.8007 2.72674 19.5456 2.72674C18.2906 2.72674 17.2722 3.7451 17.2722 5.0002C17.2722 6.25529 18.2906 7.27249 19.5456 7.27249ZM24.9997 13.6359C23.243 13.6359 21.8182 12.212 21.8182 10.4543C21.8182 8.69765 23.2432 7.27279 24.9997 7.27279C26.7575 7.27279 28.1812 8.6978 28.1812 10.4543C28.1812 12.2122 26.7574 13.6359 24.9997 13.6359ZM24.9997 12.7277C26.2548 12.7277 27.2732 11.7093 27.2732 10.4542C27.2732 9.19911 26.2548 8.18192 24.9997 8.18192C23.7446 8.18192 22.7274 9.19911 22.7274 10.4542C22.7274 11.7093 23.7446 12.7277 24.9997 12.7277ZM26.8185 6.36308C25.0606 6.36308 23.6369 4.93923 23.6369 3.18154C23.6369 1.42487 25.0608 0 26.8185 0C28.5751 0 30 1.42502 30 3.18154C30 4.93938 28.575 6.36308 26.8185 6.36308ZM26.8185 5.45488C28.0736 5.45488 29.0908 4.43652 29.0908 3.18142C29.0908 1.92633 28.0736 0.909132 26.8185 0.909132C25.5634 0.909132 24.545 1.92633 24.545 3.18142C24.545 4.43652 25.5634 5.45488 26.8185 5.45488ZM3.18153 30C1.42486 30 0 28.575 0 26.8185C0 25.0606 1.42501 23.6369 3.18153 23.6369C4.93937 23.6369 6.36306 25.0608 6.36306 26.8185C6.36306 28.5751 4.93922 30 3.18153 30ZM3.18153 29.0906C4.43663 29.0906 5.45499 28.0734 5.45499 26.8183C5.45499 25.5632 4.43663 24.5449 3.18153 24.5449C1.92644 24.5449 0.909249 25.5632 0.909249 26.8183C0.909249 28.0734 1.92644 29.0906 3.18153 29.0906ZM5.0003 22.7272C3.24246 22.7272 1.81877 21.3022 1.81877 19.5457C1.81877 17.7879 3.24261 16.3642 5.0003 16.3642C6.75697 16.3642 8.18183 17.788 8.18183 19.5457C8.18183 21.3024 6.75682 22.7272 5.0003 22.7272ZM5.0003 21.8179C6.25539 21.8179 7.27258 20.8007 7.27258 19.5456C7.27258 18.2905 6.25539 17.2721 5.0003 17.2721C3.74521 17.2721 2.72685 18.2905 2.72685 19.5456C2.72685 20.8007 3.74521 21.8179 5.0003 21.8179ZM10.4544 28.1813C8.69769 28.1813 7.27282 26.7574 7.27282 24.9997C7.27282 23.243 8.69784 21.8182 10.4544 21.8182C12.2122 21.8182 13.6359 23.2432 13.6359 24.9997C13.6359 26.7576 12.212 28.1813 10.4544 28.1813ZM10.4544 27.2731C11.7094 27.2731 12.7278 26.2547 12.7278 24.9996C12.7278 23.7445 11.7094 22.7273 10.4544 22.7273C9.19926 22.7273 8.18207 23.7445 8.18207 24.9996C8.18207 26.2547 9.19926 27.2731 10.4544 27.2731Z"
        fill="black"
      />
  </svg>
</page-header>
<div class="table-container">
    <lamb-table *ngIf="groups$ | async" [headers]="tableHeaders" [data]="tableData" [styles]="tableStyles" [clickableRows]="true" (rowClick)="onRowClick($event)"></lamb-table>
    <div *ngIf="!groups$">Loading...</div>
</div>

<pagination [state]="paginationState$ | async" (pageChange)="onPageChange($event)"></pagination>

<ng-template #creatorCellTemplate let-group>
  <div class="creator-cell">
    <lamb-gp-icon [userInitials]="getOwnerInitials(group)"></lamb-gp-icon>
    <div class="creator-name-container">{{getOwnerName(group)}}</div>
  </div>
</ng-template>