@import '../../../_colors';

:host{
    --question-text-width: 186px;
    --question-item-cursor: default;
    display: block;

    .input-container{

        &.question-order-options{
            grid-template-columns: var(--input-container-label-width) 1fr 1fr !important;

            input-radio-block{
                width: 100%;
            }
        }

        &.question-order{
            align-items: flex-start;

            label{
                line-height: 40px;
            }

            .questions-container{
                border: 1px solid $mediumLightGray;
                border-radius: 12px;
                padding: 12px;
                display: flex;
                flex-flow: column nowrap;
                row-gap: 12px;
                background-color: $lightGray;

                .question-item{
                    display: flex;
                    justify-content: space-between;
                    font-size: .75rem;
                    align-items: center;
                    position: relative;

                    .flow-line{
                        width: 1px;
                        height: 28px;
                        background-color: #9d9d9d;
                        position: relative;
                        margin: 0;
                        top: 26px;
                        left: 8px;
                    }

                    button.remove-question-button{
                        width: 15px;
                        height: 15px;
                        background-color: transparent;
                        border: none;
                        cursor: pointer;
                        
                        
                    }

                    .question-number{
                        font-weight: 500;
                        color: #000;
                        margin-left: 8px;
                    }

                    .arrow-container{
                        margin: 0 auto;
                    }

                    .question-text{
                        width: var(--question-text-width);
                        height: 40px;
                        border-radius: 12px;
                        font-weight: 700;
                        color: #fff;
                        background-color: $purple;
                        display: flex;
                        align-items: center;
                        justify-content: center;
                        position: relative;
                        cursor: var(--question-item-cursor);

                        span{
                            display: flex;
                            margin: 0 auto;
                        }

                        .edit-buttons-container{
                            position: absolute;
                            right: 12px;
                            transition: opacity 0.2s ease;
                            opacity: 0;
                            display: flex;
                            height: 100%;
                            align-items: center;
                            justify-content: center;
                            flex-flow: row nowrap;
                            column-gap: 8px;

                            .reorder-arrows{
                                display: flex;
                                height:100%;
                                align-items: center;
                                justify-content: center;
                                flex-flow: column nowrap;
                                
                                button{
                                    cursor: pointer;
                                    height: 40%;
                                }
                            }
                        }

                        &:hover{
                            .edit-buttons-container{
                                opacity: 1;
                            }
                        }
                    }

                    &.dndDragover {
                        background-color: rgba(0, 0, 0, 0.05);  // Light gray background
                        transition: background-color 0.2s ease;  // Smooth transition
                    }
                }

                .add-step-container{
                    display: flex;
                    flex-flow: row nowrap;
                    align-items: center;
                    justify-content: space-between;
                    margin-top: 17px;
                    position: relative;

                    button.add-step-button{
                        display: flex;
                        cursor: pointer;
                        align-items: center;
                        margin-left: -1px;

                        .plus-container{
                            background-color: #D9D9D9;
                            width: 19px;
                            height: 19px;
                            border-radius: 6px;
                            display: flex;
                            align-items: center;
                            justify-content: center;
                            margin-right: 5px;
                        }

                        .add-step-text{
                            font-size: .75rem;
                            font-weight: 500;
                            color: #5D5D5D;
                        }
                    }

                    .reorder-instructions{
                        font-size: .625rem;
                        font-weight: 500;
                        color: $mediumGray;
                        margin-top: -38px;
                        width: var(--question-text-width);
                        text-align: center;
                    }
                    
                    .add-step-popover{
                        position: absolute;
                        bottom: -3px;
                        left: -3px;
                        width: 230px;
                        height: 120px;
                        background-color: #fff;
                        border-radius: 12px;
                        padding: 12px;
                        display: flex;
                        flex-flow: column nowrap;
                        justify-content: space-around;
                        align-items: flex-start;

                        .add-step-popover-header{
                            background-color: #6D116F;
                            border-radius: 6px;
                            color: #fff;
                            padding: 4px;
                            width: 100%;
                            text-align: center;
                            font-size: 0.6875rem;
                            font-weight: 500;
                            // margin-bottom: 4px;
                        }

                        .popover-item{
                            font-size: 0.8125rem;
                            font-weight: 500;
                        }
                    }

                    custom-step-list-popover{
                        position: absolute;
                        bottom: -180px;
                        left: -3px;
                        width: 370px;
                        height: 380px;
                        background-color: #fff;
                        border-radius: 12px;
                    }
                }
            }
        }
    }
}
