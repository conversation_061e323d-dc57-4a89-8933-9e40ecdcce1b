@import '../../../_colors.scss';

:host{
    display: flex;
    border: none;
    background-color: $inputBackground;
    outline: $inputBorder;
    width: fit-content;
    height: 40px;
    border-radius:12px;
    font-size: 1rem;
    color: $appText;
    justify-content: center;
    align-items: center;

    &:focus{
        outline: $inputFocusBorder;
    }

    &.active{
        border: 1px solid $blue;
        background-color: $blue;
        color: #fff;
        box-shadow: 0px 4px 10px rgba(0, 0, 0, 0.18);
    }

    label{
        width: 100%;
        height: 100%;
        padding: 7px 10px;
        display: flex;
        align-items: center;
        justify-content: center;
    }

    input{
        appearance: none;
        -webkit-appearance: none;
        -moz-appearance: none;
        border: none;
        outline: none;
        background: none;
        padding: 0;
        margin: 0;
        border-radius: 0;
        font-family: inherit;
        font-size: inherit;
        color: inherit;
        box-sizing: border-box;
        width: 0;
        height: 0;
        opacity: 0;
        position: absolute;
        z-index: -1;
    }
}