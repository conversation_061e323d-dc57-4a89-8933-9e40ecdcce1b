@import '../../../_colors';

:host{
    display: flex;
    align-items: center;

    input{
        
        &[type="radio"],
        &[type="checkbox"] {
            width: 16px;
            height: 16px;
            background-color: #fff;
            border: 2px solid #fff;
            outline: $inputBorder;
            border-radius: 50%;
            margin-right: 10px;
            cursor: pointer;
            flex-shrink: 0;
    
            &:checked {
                background-color: $blue;
            }
    
            &:focus {
                outline-color: $blue;
            }
        }
    }
}