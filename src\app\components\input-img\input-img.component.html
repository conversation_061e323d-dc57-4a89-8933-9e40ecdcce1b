<div (click)="unsetPhoto()" class="remove-photo" *ngIf="hasImage">
    <svg viewBox="0 0 50 50"><path fill="#000" fill-rule="nonzero" d="M47.652 3.883L44.1168 0.3478 23.9998 20.4648 3.8828 0.3478 0.3476 3.883 20.4646 24 0.3476 44.117 3.8828 47.6522 23.9998 27.5352 44.1168 47.6522 47.652 44.117 27.535 24z"/></svg>
    <span>REMOVE PHOTO</span>
</div>
<input (change)="stagePhoto($event)" [name]="id" [id]="id" type="file">
<label [for]="id" 
        (dragover)="onDragOver($event)"
        (dragleave)="onDragLeave($event)"
        (drop)="onDrop($event)"
        [class.dragging]="isDragging">
  <img *ngIf="hasImage" class="image-preview" src="{{imgUrl}}" />
  <svg *ngIf="!hasImage" width="23" height="23" viewBox="0 0 23 23" fill="none" xmlns="http://www.w3.org/2000/svg">
      <path d="M17.8018 22.9997H5.16176C2.31092 22.9941 0.00573529 20.6852 0 17.8298V5.16989C0.00560079 2.31456 2.31092 0.00574433 5.16176 0H17.7963C20.6471 0.00560962 22.9523 2.31456 22.9581 5.16989V17.8244C22.9581 20.6853 20.6527 22.9943 17.802 23L17.8018 22.9997ZM5.16176 1.83761C3.32693 1.83761 1.83472 3.33091 1.83472 5.16989V17.8244C1.83472 19.6621 3.32566 21.1566 5.16176 21.1566H17.7963C19.6311 21.1566 21.1234 19.6633 21.1234 17.8244V5.16989C21.1234 3.33217 19.6324 1.83761 17.7963 1.83761H5.16176Z" fill="#9B9B9B"/>
      <path d="M7.54764 11.3045C5.84945 11.3045 4.47381 9.92568 4.47381 8.22588C4.47381 6.52501 5.85051 5.1472 7.54764 5.1472C9.24583 5.1472 10.6215 6.52607 10.6215 8.22588C10.6215 9.92562 9.24477 11.3045 7.54764 11.3045ZM7.54764 6.9851C6.86545 6.9851 6.30873 7.5427 6.30873 8.22596C6.30873 8.90922 6.86545 9.46682 7.54764 9.46682C8.22982 9.46682 8.78655 8.90922 8.78655 8.22596C8.78655 7.54158 8.22982 6.9851 7.54764 6.9851Z" fill="#9B9B9B"/>
      <path d="M1.11781 20.0299C0.612615 20.0299 0.200391 19.6215 0.200391 19.111C0.200391 18.8642 0.297845 18.6286 0.475954 18.4558C0.81425 18.1226 1.97252 17.077 3.09156 16.0717C3.88911 15.3537 4.7147 14.6132 4.89279 14.4348C5.70155 13.6539 6.92927 13.498 7.90381 14.0668L9.69944 15.1181C9.96381 15.2729 10.3021 15.2269 10.5194 15.0037L13.1921 12.2235C14.0524 11.3327 15.4403 11.2014 16.4495 11.9194L22.5521 15.8776C22.9879 16.1356 23.1313 16.6988 22.868 17.1353C22.6037 17.5717 22.0481 17.7153 21.6123 17.4517C21.5955 17.4404 21.572 17.4292 21.5552 17.4169L15.4121 13.4363L15.3897 13.4195C15.1198 13.2243 14.7479 13.2591 14.5126 13.4947L11.8399 16.2749C11.0367 17.1073 9.7698 17.2857 8.77725 16.7001L6.98162 15.6488C6.72398 15.4996 6.39688 15.54 6.17845 15.7464C5.97234 15.9472 5.2095 16.6372 4.32007 17.4349C3.32199 18.337 2.07749 19.4567 1.76833 19.7618C1.58798 19.938 1.35834 20.03 1.11752 20.03L1.11781 20.0299Z" fill="#9B9B9B"/>
  </svg>
  <div *ngIf="!hasImage" class="drop-text">Drop a product image here</div>
  <div *ngIf="!hasImage" class="choose-text">or Choose a file...</div>
</label>
