import { PanelInterface } from "../interfaces/panel.interface";
import { ApiService } from "../services/api.service";

export class PanelAdapter {

    apiService?: ApiService;

    constructor(apiService: ApiService){
        this.apiService = apiService;
    }

    async adapt(apiPanels: any): Promise<PanelInterface[]> {
        return await Promise.all(apiPanels.map(async (panel: any) => {
            // Check if this is a Triangle Test panel
            const isTriangleTest = panel.experiment_type === 'triangle_test';

            // Handle product information based on panel type
            let productName = 'Multiple Products';
            let productImage = '';
            let productId = '';

            // For normal sensory panels, use the product information
            if (!isTriangleTest && panel.product) {
                productName = panel.product.name;
                productImage = panel.product.image || '';
                productId = panel.product.id;
            }
            // For triangle test panels with products array
            else if (isTriangleTest && panel.products && panel.products.length > 0) {
                productName = `Triangle Test (${panel.products.length} products)`;
                // Optionally use the first product's image if available
                if (panel.products[0].image) {
                    productImage = panel.products[0].image;
                }
                productId = 'multiple';
            }
            // Fallback for any panel without product information
            else if (panel.product) {
                productName = panel.product.name;
                productImage = panel.product.image || '';
                productId = panel.product.id;
            }

            return {
                status: panel.active ? 'Active' : 'Inactive',
                panelName: panel.name,
                productName: productName,
                productImage: productImage,
                privacy: panel.isPublic ? 'Public' : 'Private',
                scheduledEnd: this.getDateFromSeconds(panel.end_date?._seconds),
                responses: await this.getResponseCountByPanelId(panel.id),
                groupCount: this.getGroupCount(panel),
                panelId: panel.id,
                productId: productId,
                experimentType: panel.experiment_type || 'normal_sensory',
                questions: panel.questions || [],
            };
        }))
    }

    getDateFromSeconds(seconds: number | string | undefined){
        if (!seconds) {
            return "Ongoing";
        }

        const date = new Date(seconds as number * 1000);
        if (date.getFullYear() > 4000 || isNaN(date.getTime())) {
            return "Ongoing";
        }
        return date.toLocaleDateString();
    }

    async getResponseCountByPanelId(panelId: string){
        if(!this.apiService){
            throw new Error('ApiService is not set');
        }
        if(typeof panelId === 'string'){
            const responseCount = await this.apiService.getResponseCount(panelId, "panels") as { count: number };
            return responseCount.count;
        }
        return 0;
    }

    getGroupCount(_panel: any): string {
        // Prefix with underscore to indicate it's intentionally unused for now
        return 'To do: get group count';
    }
}
