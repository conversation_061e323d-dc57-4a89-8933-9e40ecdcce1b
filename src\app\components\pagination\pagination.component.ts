import { Component, EventEmitter, Input, Output, SimpleChanges } from '@angular/core';
import { NgFor } from '@angular/common';
import { PaginationState } from '../../interfaces/pagination.interface';

@Component({
  selector: 'pagination',
  standalone: true,
  imports: [NgFor],
  templateUrl: './pagination.component.html',
  styleUrl: './pagination.component.scss'
})
export class PaginationComponent {
  @Input() state: any = {
    currentPage: 1,
    totalPages: 2,
    hasMore: true
  };
  @Output() pageChange = new EventEmitter<number>();

  get pages(): number[] {
    const total = this.state?.totalPages || 1;
    const current = this.state?.currentPage || 1;
    
    // Show max 5 page numbers
    let start = Math.max(1, current - 2);
    let end = Math.min(total, start + 4);
    
    if (end - start < 4) {
      start = Math.max(1, end - 4);
    }
    
    return Array.from({ length: end - start + 1 }, (_, i) => start + i);
  }

  nextPage() {
    console.log("nextPage", this.state);
    if (this.state?.hasMore) {
      // console.log("nextPage", this.state.currentPage + 1);
      this.pageChange.emit(this.state.currentPage + 1);
    }
  }

  previousPage() {
    // console.log("previousPage", this.state);
    if (this.state.currentPage > 1) {
      // console.log("previousPage", this.state.currentPage - 1);
      this.pageChange.emit(this.state.currentPage - 1);
    }
  }

  goToPage(page: number) {
    // console.log("goToPage", this.state);
    if (page !== this.state.currentPage && page > 0 && page <= this.state.totalPages) {
      // console.log("goToPage", page);
      this.pageChange.emit(page);
    }
  }

  goToFirst() {
    // console.log("goToFirst", this.state);
    if (this.state.currentPage !== 1) {
      // console.log("goToFirst", 1);
      this.pageChange.emit(1);
    }
  }

  goToLast() {
    // console.log("goToLast", this.state);
    if (this.state.currentPage !== this.state.totalPages) {
      // console.log("goToLast", this.state.totalPages);
      this.pageChange.emit(this.state.totalPages);
    }
  }

  constructor() { 
    console.log("state",this.state);
  }

  ngOnInit() {
    console.log("state from init",this.state);
  }
}
