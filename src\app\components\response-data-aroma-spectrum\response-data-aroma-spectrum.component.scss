:host{
    --aroma-spectrum-arrow-height: 120px;
    --aroma-spectrum-arrow-width: inherit;
    
    display: flex;
    width: 100%;
    position: relative;
    height: auto;
    position: relative;
    justify-content: center;

    aroma-spectrum-arrow{
        
        height: auto;
        width: var(--aroma-spectrum-arrow-width);
        margin: 0 auto;
        min-height: 121px;

        // position: absolute;
        // top: 0;
        // left: 0;
    }

    .bars-container{
        --bar-container-width: calc(var(--aroma-spectrum-arrow-width) - 140px);
        --bar-container-height: calc(var(--aroma-spectrum-arrow-height) - 30px);
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(0, 1fr));
        height: var(--bar-container-height);
        // height: 57%;
        top: 23%;
        // width: var(--bar-container-width);
        width: 84.3%;
        position: absolute;
        // justify-content: center;
        // top: 21px;
        // left: 0;
        // margin: 0 calc(50% - (var(--bar-container-width) / 2));
        gap: 2px;

        aroma-spectrum-bar{
            opacity: .75;
            transition: opacity 0.2s ease;

            &:hover{
                opacity: 1;
            }
        }
    }
}