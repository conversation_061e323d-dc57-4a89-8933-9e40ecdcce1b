<div class="form-step-container">
<form [formGroup]="form">
    <div class="form-step-title">Configure panel questions</div>

    <div class="input-container" [class.error-shake]="panelsService.formErrors['step3']['consumptionMethod']">
        <label for="consumption-method-control">Consumption Method</label>
        <input-select [control]="consumptionMethodControl" [id]="'consumption-method-control'" [options]="consumptionOptions" [placeholder]="'consumption method'"></input-select>
    </div>

    <div class="input-container question-order">
        <label>Steps</label>
        <div class="questions-container">
            <div class="question-item" 
             *ngFor="let question of questionsControl().value; let i = index"
             [attr.data-index]="i"
             dndDropzone
             (dndDrop)="onDrop($event)">

                <div class="flow-line"></div>
            
                <button class="remove-question-button" type="button" (click)="removeQuestion(i)">
                    <svg xmlns="http://www.w3.org/2000/svg" width="8" height="8" viewBox="0 0 8 8" fill="none">
                        <path d="M4 3.19171L7.02451 0.167203C7.24744 -0.0557344 7.60936 -0.0557344 7.8328 0.167203C8.05573 0.390631 8.05573 0.752544 7.8328 0.975494L4.80829 4L7.8328 7.02451C8.05573 7.24744 8.05573 7.60936 7.8328 7.8328C7.60937 8.05573 7.24746 8.05573 7.02451 7.8328L4 4.80829L0.975494 7.8328C0.752556 8.05573 0.390644 8.05573 0.167203 7.8328C-0.0557344 7.60937 -0.0557344 7.24746 0.167203 7.02451L3.19171 4L0.167203 0.975494C-0.0557344 0.752556 -0.0557344 0.390644 0.167203 0.167203C0.390631 -0.0557344 0.752544 -0.0557344 0.975494 0.167203L4 3.19171Z" fill="#9D9D9D"/>
                    </svg>
                </button>
                <div class="question-number">Step {{i + 1 | digitToWord: true}}</div>
                <div class="arrow-container">
                    <svg xmlns="http://www.w3.org/2000/svg" width="21" height="8" viewBox="0 0 21 8" fill="none">
                        <path d="M20.3536 4.35355C20.5488 4.15829 20.5488 3.84171 20.3536 3.64645L17.1716 0.464466C16.9763 0.269204 16.6597 0.269204 16.4645 0.464466C16.2692 0.659728 16.2692 0.976311 16.4645 1.17157L19.2929 4L16.4645 6.82843C16.2692 7.02369 16.2692 7.34027 16.4645 7.53553C16.6597 7.7308 16.9763 7.7308 17.1716 7.53553L20.3536 4.35355ZM0 4.5H20V3.5H0V4.5Z" fill="#9D9D9D"/>
                    </svg>
                </div>
                
                <div class="question-text"
                    [dndDraggable]="question"
                    [dndEffectAllowed]="draggable.effectAllowed"
                    [attr.data-index]="i"
                    [style.--question-item-cursor]="'grab'">
                    <span>{{questionsService.adaptedQuestionsObject[question].name}}</span>

                    <div class="edit-buttons-container">
                        <button class="edit-step-button" type="button" (click)="editStep(question)">
                            <svg xmlns="http://www.w3.org/2000/svg" width="18" height="10" viewBox="0 0 18 10" fill="none">
                                <path d="M8.63636 0C5.13738 0 2.03155 1.83283 0.105733 4.65002C-0.0352442 4.85784 -0.0352442 5.13596 0.105733 5.34378C2.03135 8.16099 5.13738 10 8.63636 10C12.1353 10 15.2412 8.16093 17.167 5.34378C17.308 5.13596 17.308 4.85784 17.167 4.65002C15.2414 1.83281 12.1353 0 8.63636 0ZM8.63636 1.2C11.6 1.2 14.2266 2.68283 15.9611 5.00002C14.2266 7.31483 11.5982 8.80003 8.63636 8.80003C5.6745 8.80003 3.04609 7.31487 1.31167 5.00002C3.04613 2.68281 5.67297 1.2 8.63636 1.2ZM8.63636 2.00001C7.05264 2.00001 5.75686 3.35001 5.75686 5.00002C5.75686 6.65003 7.05264 8.00003 8.63636 8.00003C10.2201 8.00003 11.5159 6.65003 11.5159 5.00002C11.5159 3.35001 10.2201 2.00001 8.63636 2.00001ZM8.63636 3.20001C9.5977 3.20001 10.3641 3.99846 10.3641 5.00002C10.3641 6.00158 9.5977 6.80003 8.63636 6.80003C7.67503 6.80003 6.90866 6.00158 6.90866 5.00002C6.90866 3.99846 7.67503 3.20001 8.63636 3.20001Z" fill="#EFB2F1"/>
                            </svg>
                        </button>

                        <div class="reorder-arrows">
                            <button [disabled]="i === 0" type="button" (click)="moveItemUp(i)">
                                <svg xmlns="http://www.w3.org/2000/svg" width="10" height="7" viewBox="0 0 10 7" fill="none">
                                    <path d="M4.99994 2.40922L1.11337 6.78488C0.858596 7.07171 0.44585 7.07171 0.191078 6.78488C-0.063693 6.49804 -0.063693 6.03336 0.191078 5.74653L4.53864 0.851368C4.66624 0.708202 4.8329 0.636364 5 0.636364C5.1671 0.636364 5.33376 0.708192 5.46136 0.851368L9.80892 5.74653C10.0637 6.03336 10.0637 6.49804 9.80892 6.78488C9.55415 7.07171 9.1414 7.07171 8.88663 6.78488L4.99994 2.40922Z" fill="#EFB2F1"/>
                                </svg>
                            </button>
                            <button [disabled]="i === questionItems.length - 1" type="button" (click)="moveItemDown(i)">
                                <svg xmlns="http://www.w3.org/2000/svg" width="10" height="7" viewBox="0 0 10 7" fill="none">
                                    <path d="M5.00006 4.59078L8.88663 0.215124C9.1414 -0.071708 9.55415 -0.071708 9.80892 0.215124C10.0637 0.501956 10.0637 0.966641 9.80892 1.25347L5.46136 6.14863C5.33376 6.2918 5.1671 6.36364 5 6.36364C4.8329 6.36364 4.66624 6.29181 4.53864 6.14863L0.191079 1.25347C-0.063693 0.966641 -0.063693 0.501956 0.191079 0.215124C0.445851 -0.071708 0.858597 -0.071708 1.11337 0.215124L5.00006 4.59078Z" fill="#EFB2F1"/>
                                </svg>
                            </button>
                        </div>
                    </div>
                </div>
            </div>

            <div class="add-step-container">
                <button class="add-step-button" type="button" (click)="openAddStepPopover()">
                    <div class="plus-container">
                        <svg xmlns="http://www.w3.org/2000/svg" width="10" height="10" viewBox="0 0 10 10" fill="none">
                            <path d="M4.11765 0V4.11765H0V5.88235H4.11765V10H5.88235V5.88235H10V4.11765H5.88235V0H4.11765Z" fill="#5D5D5D"/>
                        </svg>
                    </div>
                    <div class="add-step-text">Add step</div>
                </button>

                <div class="reorder-instructions">Click and drag or use the buttons to re-order</div>

                <div class="add-step-popover" *ngIf="addStepPopover" [@fadeScale]>
                    <div class="add-step-popover-header">Add a Step</div>
                    <button type="button" class="popover-item" (click)="addStep()">Create New Step</button>
                    <button type="button" class="popover-item" (click)="openCustomStepListPopover()">Use a Previously Created Step</button>
                </div>

                <custom-step-list-popover *ngIf="customStepListPopover" [@fadeScale] (viewStep)="onViewStep($event)" (stepSelected)="onStepSelected($event)" (back)="backToAddStepPopover()" [addedSteps]="questionsControl().value" [questions]="questionsService.adaptedQuestions"></custom-step-list-popover>
            </div>
        </div>
    </div>
</form>

</div>
