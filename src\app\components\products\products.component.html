<page-header [buttonClass]="'purple'" [pageName]="'Samples'" [infoVarient]="1" [info]="'info'" (onFilter)="productsService.filter($event)" (onSearch)="productsService.search($event)" (onAdd)="popupService.openNewProduct($event)" [addText]="'New Sample'">
    <svg width="31" height="42" viewBox="0 0 31 42" fill="none" xmlns="http://www.w3.org/2000/svg">
        <path d="M13.2951 0H31L18.7049 42H1L13.2951 0Z" fill="#F5D9FF"/>
        <path d="M16.2284 16.0339C15.9566 17.1562 14.5718 17.5633 13.7358 16.7667L8.62788 11.8996C7.79192 11.103 8.13174 9.70019 9.23956 9.37451L16.0086 7.38447C17.1165 7.05878 18.1614 8.05448 17.8895 9.17672L16.2284 16.0339Z" stroke="#292727"/>
        <circle cx="6" cy="27" r="5.5" stroke="#292727"/>
        <rect x="17.0778" y="24.1582" width="9.3" height="9.3" rx="2.5" transform="rotate(-19.1664 17.0778 24.1582)" stroke="#292727"/>
    </svg>
        
</page-header>

<div class="table-container">
    <ng-container *ngIf="products$ | async as products; else loading">
        <lamb-table *ngIf="productsService.productsArray" [styles]="tableStyles" [headers]="tableHeaders" [data]="productsService.productsArray" [clickableRows]="true" (rowClick)="onRowClick($event)"></lamb-table>
    </ng-container>
    <ng-template #loading>
        <div *ngIf="!productsService.productsArray">No samples found</div>
    </ng-template>
</div>

<pagination [state]="paginationState$ | async" (pageChange)="onPageChange($event)"></pagination>
