import { Component, Input } from '@angular/core';
import { NgIf } from '@angular/common';
import { FormControl, ReactiveFormsModule } from '@angular/forms';
// import * as imageResizeCompress from 'image-resize-compress';
import { fromBlob, blobToURL } from 'image-resize-compress';

@Component({
  selector: 'input-img',
  standalone: true,
  imports: [ReactiveFormsModule, NgIf],
  templateUrl: './input-img.component.html',
  styleUrl: './input-img.component.scss'
})
export class InputImgComponent {

  @Input() control: FormControl = new FormControl();
  @Input() id: string = '';
  @Input() placeholder: string = '';

  hasImage: boolean = false;
  imgUrl: string = '';
  reader: FileReader = new FileReader();
  isDragging = false;

  ngOnInit() {
    this.checkForPhoto();
  }

  checkForPhoto() {
    const initialValue = this.control.value;
    if (initialValue) {
      if (initialValue instanceof Blob) {
        this.imgUrl = URL.createObjectURL(initialValue);
      } else {
        this.imgUrl = initialValue;
      }
      this.hasImage = true;
    }
  }

  async stagePhoto(event: any) {
    let file = event.target.files[0];
    const quality = 80; // For webp and jpeg formats
    const width = 'auto'; // Original width
    const height = 'auto'; // Original height
    const format = 'webp'; // Output format
    const compressedImage = await fromBlob(file, quality, width, height, format);
    const compressedImageUrl = await blobToURL(compressedImage);
    this.imgUrl = compressedImageUrl as string;
    this.hasImage = true;
    this.control.setValue(compressedImageUrl);
  }

  ngOnDestroy() {
    if (this.imgUrl) {
      URL.revokeObjectURL(this.imgUrl);
    }
  }

  unsetPhoto() {
    // Clear the form control value
    this.control.setValue(null);
    
    // Clear the image preview
    if (this.imgUrl) {
      URL.revokeObjectURL(this.imgUrl);
      this.imgUrl = '';
    }
    
    // Reset the hasImage flag
    this.hasImage = false;
  }

  onDragOver(event: DragEvent) {
    event.preventDefault();
    event.stopPropagation();
    this.isDragging = true;
  }

  onDragLeave(event: DragEvent) {
    event.preventDefault();
    event.stopPropagation();
    this.isDragging = false;
  }

  onDrop(event: DragEvent) {
    event.preventDefault();
    event.stopPropagation();
    this.isDragging = false;

    const files = event.dataTransfer?.files;
    if (files?.length) {
      // Create a new event-like object
      const fileEvent = {
        target: {
          files: files
        }
      };
      this.stagePhoto(fileEvent);
    }
  }

}
