import { Component, EventEmitter, Input, Output } from '@angular/core';
import { NgIf } from '@angular/common';
import { ButtonStandardComponent } from '../button-standard/button-standard.component';
import { RouterLink } from '@angular/router';

@Component({
  selector: 'page-header',
  standalone: true,
  imports: [ButtonStandardComponent, NgIf, RouterLink],
  templateUrl: './page-header.component.html',
  styleUrl: './page-header.component.scss'
})
export class PageHeaderComponent {

  @Input() pageName: string = '';
  @Input() infoVarient: number = 0;
  @Input() info: string | number = '';
  @Input() addText: string = 'New';
  @Input() buttonClass: string = 'blue';
  @Input() url: string = '';
  @Output() onFilter = new EventEmitter<any>();
  @Output() onSearch = new EventEmitter<any>();
  @Output() onAdd = new EventEmitter<any>();
}
