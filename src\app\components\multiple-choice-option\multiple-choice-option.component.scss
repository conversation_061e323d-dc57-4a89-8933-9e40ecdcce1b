@import '../../../_colors';
:host{
    display: block;
    margin-bottom: 30px;
    box-shadow: 0px 4px 11px 0px rgba(0, 0, 0, 0.10);
    padding: 20px;
    border-radius: 12px;

    input-text,
    input-text-area{
        width: 100%;
    }

    .option-header{
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 30px;

        button.remove-option-button{
            margin-right: 10px;
            opacity: .4;
            cursor: pointer;
            transition: opacity .2s ease-in-out;

            &:hover{
                opacity: 1;
            }
        }

        .option-container-label{
            margin-right: auto;
            font-size: .75rem;
            font-weight: 700;
        }
    }

    .input-container:last-child{
        margin-bottom: 0;
    }
}