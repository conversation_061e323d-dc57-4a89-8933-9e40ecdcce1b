:host{
    --table-columns: 0.5fr 2fr 2fr 2fr 1fr 0.75fr;
    display: flex;
    flex-direction: column;
    justify-content: flex-start;
    align-items: center;
    width: 100%;
    max-width: 1750px;
    margin: 25px auto 0 auto;
    min-width: 850px;

    a{
        text-decoration: none;
        color: inherit;
    }

    .table-header{
        background: var(--table-header-background);
        color: var(--table-header-text-color);
        font-size: var(--table-header-text-size);
        font-weight: var(--table-header-text-weight);
        display: grid;
        grid-template-columns: var(--table-columns);
        column-gap: var(--table-column-gap);
        row-gap: var(--table-row-gap);
        padding: var(--table-header-cell-padding);
        align-items: center;
        height: auto;
        min-height: var(--table-header-height);
        border-radius: var(--table-header-border-radius);
        width:100%;
    }

    .table-body {
        width: 100%;
        max-width: 1750px;

        panels-table-row {
                background: var(--table-row-odd-background);
                display: grid;
                grid-template-columns: var(--table-columns);
                column-gap: var(--table-column-gap);
                row-gap: var(--table-row-gap);
                padding: var(--table-cell-padding);
                align-items: center;
                height: auto;
                font-size: var(--table-row-text-size);
                min-height: var(--table-row-height);
                border-radius: var(--table-row-border-radius);
        
            &:nth-child(even) {
                background: var(--table-row-even-background);
            }
        }
    }

    
}