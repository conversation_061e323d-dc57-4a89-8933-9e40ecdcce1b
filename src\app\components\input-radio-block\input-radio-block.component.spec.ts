import { ComponentFixture, TestBed } from '@angular/core/testing';

import { InputRadioBlockComponent } from './input-radio-block.component';

describe('InputRadioBlockComponent', () => {
  let component: InputRadioBlockComponent;
  let fixture: ComponentFixture<InputRadioBlockComponent>;

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      imports: [InputRadioBlockComponent]
    })
    .compileComponents();

    fixture = TestBed.createComponent(InputRadioBlockComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
