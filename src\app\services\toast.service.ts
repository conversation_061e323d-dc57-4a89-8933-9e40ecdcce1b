import { Injectable } from '@angular/core';
import { Subject } from 'rxjs';

@Injectable({
  providedIn: 'root'
})
export class ToastService {
  // private toastSubject = new Subject<string>();
  // toastState = this.toastSubject.asObservable();
  burnt: boolean = false;
  showToast: boolean = false;
  message: any = "init";

  constructor() { }

  goodToast(message: string){
    this.message = message;
    this.showToast = true;
    setTimeout(() => {
      this.showToast = false;
    }, 3000);
  }

  burntToast(message: string){
    this.message = message;
    this.burnt = true;
    this.showToast = true;
    setTimeout(() => {
      this.burnt = false;
      this.showToast = false;
    }, 3000);
  }
}
