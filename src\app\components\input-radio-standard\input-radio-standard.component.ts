import { Component, Input } from '@angular/core';
import { NgIf } from '@angular/common';
import { FormControl, ReactiveFormsModule } from '@angular/forms';
@Component({
  selector: 'input-radio-standard',
  standalone: true,
  imports: [ReactiveFormsModule, NgIf],
  templateUrl: './input-radio-standard.component.html',
  styleUrl: './input-radio-standard.component.scss'
})
export class InputRadioStandardComponent {

  @Input() control!: FormControl;
  @Input() id!: string;
  @Input() value!: string | boolean | number;
  @Input() name!: string;
  @Input() type: 'radio' | 'checkbox' = 'radio';
}
