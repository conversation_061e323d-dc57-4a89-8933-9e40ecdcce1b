import { Component, Input } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormControl, ReactiveFormsModule } from '@angular/forms';

@Component({
  selector: 'input-radio-block',
  standalone: true,
  imports: [CommonModule, ReactiveFormsModule],
  templateUrl: './input-radio-block.component.html',
  styleUrl: './input-radio-block.component.scss'
})
export class InputRadioBlockComponent {

  @Input() control!: FormControl;
  @Input() id!: string;
  @Input() value!: string | number | boolean;
  @Input() name!: string;
  @Input() type: 'radio' | 'checkbox' = 'radio';
}
