import { Component, inject } from '@angular/core';
import { CommonModule } from '@angular/common';
import { PanelsService } from '../../services/panels.service';
import { InputRadioStandardComponent } from '../input-radio-standard/input-radio-standard.component';
import { InputRadioBlockComponent } from '../input-radio-block/input-radio-block.component';
import { FormControl, ReactiveFormsModule } from '@angular/forms';

@Component({
  selector: 'new-panel-step-4',
  standalone: true,
  imports: [CommonModule, InputRadioStandardComponent, InputRadioBlockComponent, ReactiveFormsModule],
  templateUrl: './new-panel-step-4.component.html',
  styleUrl: './new-panel-step-4.component.scss'
})
export class NewPanelStep4Component {

  panelsService = inject(PanelsService);
  form = this.panelsService.step4Form;
  panelistPoolControl = this.form.get('panelistPool')! as FormControl;
  restrictedControl = this.form.get('restricted')! as FormControl;
  restrictionLevelControl = this.form.get('restrictionLevel')! as FormControl;

  ngOnInit(){
    this.panelistPoolControl.setValue('private');
  }
}