:host {
  background: #fff;
    border-radius: 16px;
    box-shadow: 0 4px 24px rgba(0,0,0,0.10);
    padding: 24px 20px 20px 20px;
    min-width: 380px;
    max-width: 420px;
    display: flex;
    flex-direction: column;
    gap: 16px;

  // .custom-step-list-popover-container {
    

    .popover-header {
      font-size: 0.8125rem;
      font-weight: 700;
      color: #292727;
      margin-bottom: 8px;
      letter-spacing: 0.01em;
      display: flex;
      flex-flow: row nowrap;
      align-items: center;
      gap: 10px;

      button{
        display: flex;
        align-items: center;
        height: fit-content;
      }

      span{
        line-height: 1rem;
      }
    }

    .search-row {
      margin-bottom: 8px;
    }

    input-text.search-input {
      width: 100%;
    }

    .view-step-btn {
      background: none;
      border: none;
      color: #a259e6;
      font-weight: 600;
      cursor: pointer;
      padding: 4px 10px;
      border-radius: 6px;
      transition: background 0.15s;

      &:hover,
      &:focus {
        background: #f3eaff;
        outline: none;
      }
    }
  // }

  // Table tweaks if needed
  ::ng-deep lamb-table {
    margin-top: 0;
    overflow: scroll;
    width: 100%;

    thead{
      position: sticky;
      top: 0;
      // background: #fff;
    }

    tbody{
      tr:hover {
        cursor: pointer;
      }
    }
  }
} 