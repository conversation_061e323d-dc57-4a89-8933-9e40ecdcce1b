import { Component, inject } from '@angular/core';
import { FormGroup, ReactiveFormsModule } from '@angular/forms';
import { ProductsService } from '../../services/products.service';
import { ConstantsService } from '../../services/constants.service';
import { ProductBlockComponent } from '../product-block/product-block.component';

@Component({
  selector: 'new-product-step-3',
  standalone: true,
  imports: [ReactiveFormsModule, ProductBlockComponent],
  templateUrl: './new-product-step-3.component.html',
  styleUrl: './new-product-step-3.component.scss'
})
export class NewProductStep3Component {

  productsService = inject(ProductsService);
  constantsService = inject(ConstantsService);
  formData: any;

  ngOnInit(): void {
    this.formData = this.getFromData();
  }

  getFromData() {
    const formData = {
      ...this.productsService.step1Form.value,
      ...this.productsService.step2Form.value,
    };
    return formData;
  }

  editGeneral(event: any){
    event.preventDefault();
    this.productsService.setFormStep(1);
  }

  editNitty(event: any){
    event.preventDefault();
    this.productsService.setFormStep(2);
  }
}
