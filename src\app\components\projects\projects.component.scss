:host{
    display: block;
    width: 100%;
    min-height: var(--standard-height);
    height: auto;
    padding: var(--standard-padding);
    box-sizing: border-box;

    .projects-summary-metrics-container{
        display: grid;
        grid-template-columns: repeat(3, 150px) 1fr;
        gap: 20px;
        align-items: center;
        margin-top: 20px;

        .projects-summary-metric{
            background: #fff;
            border: 1px solid #DCDCDC;
            box-shadow: 0px 4px 13px 0px rgba(0, 0, 0, 0.10);
            border-radius: 12px;
            padding: 20px;

            .projects-summary-metric-label{
                font-size: 0.8125rem;
                font-weight: 500;
                color: #505050;
                margin-bottom: 10px;
            }

            .projects-summary-metric-value{
                font-size: 1.25rem;
                font-weight: 700;
                color: #292727;
            }
        }

        .percentage-bar-container{
            margin-left: 10px;
            position: relative;

            .percentage-bar-label{
                font-size: 0.8125rem;
                font-weight: 500;
                color: #505050;
                margin-bottom: 10px;
            }
        }
    }

    .table-container{
        margin-top: 20px;
    }
}

.status-cell{
    display: flex;
    align-items: center;
    gap: 8px;

    .color-dot{
        width: 10px;
        height: 10px;
        border-radius: 50%;
    }

    .status-text{
        text-transform: capitalize;
    }
}

.owner-cell{
    display: flex;
    align-items: center;
    gap: 8px;

    .icon-placeholder{
        width: 20px;
        height: 20px;
        border-radius: 50%;
        background-color: lightGray;
    }
}