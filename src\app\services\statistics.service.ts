import { Injectable } from '@angular/core';

@Injectable({
  providedIn: 'root'
})
export class StatisticsService {

  constructor() { }

  calculatedStats: any;

  /**
   * calculateStats - Computes various statistics for each attribute
   *
   * The input object should include for each attribute:
   *   - [attribute]_sum:         Sum of intensities for that attribute
   *   - [attribute]_count:       Count of times the attribute was identified
   *   - [attribute]_sum_squares:  Sum of squared intensities
   *
   * Additionally, the input should include:
   *   - count: Total number of panel responses
   *
   * The function returns an object mapping each attribute to its computed statistics:
   *   - rawMean:           (attribute_sum / attribute_count)
   *   - globalMean:        (attribute_sum / total panel count)
   *   - variance:          Sample variance (if attribute_count > 1)
   *   - std:               Standard deviation (square root of variance)
   *   - stdError:          Standard error of the mean (std / sqrt(attribute_count))
   *   - shrinkageEstimate: Weighted average of rawMean and globalMean:
   *                        (n / (n + kappa)) * rawMean + (kappa / (n + kappa)) * globalMean,
   *                        where kappa is the average identification count across attributes.
   *
   * @param {Object} data - Input data object with the structure described above.
   * @returns {Object} An object with computed statistics for each attribute.
   */
  calculateStats(data: any) {
    // Total number of panel responses (used to compute the global mean)
    const totalPanelCount = data.count;

    // Extract attribute names by looking for keys ending with '_sum'.
    // (This assumes no other key ends with '_sum'.)
    const attributeNames = Object.keys(data)
    .filter(key => key.endsWith('_sum'))
    .map(key => key.slice(0, -4)); // remove the '_sum' suffix
    
    // Compute kappa as the average identification count across attributes.
    // This hyperparameter determines how much to shrink low-count estimates toward the global mean.
    let sumAttributeCounts = 0;
    let numAttributes = 0;
    attributeNames.forEach(attr => {
      const attrCount = data[`${attr}_count`];
      if (typeof attrCount === 'number') {
        sumAttributeCounts += attrCount;
        numAttributes++;
      }
    });
    const kappa = numAttributes > 0 ? sumAttributeCounts / numAttributes : 0;
    
    // Initialize the results object.
    let results: { [key: string]: any } = {};
    
    // For each attribute, calculate statistics.
    attributeNames.forEach(attr => {
      // Get the stored values.
      const sum = data[`${attr}_sum`];
      const count = data[`${attr}_count`];
      const sumsquares = data[`${attr}_sum_squares`];
      
      // Compute the raw mean intensity when the attribute is identified.
      // (If count is 0, we set rawMean to 0.)
      const rawMean = count > 0 ? sum / count : 0;
      
      // Compute the global mean over all panels.
      // This treats non-responses as zero intensity.
      const globalMean = totalPanelCount > 0 ? sum / totalPanelCount : 0;
      
      // Compute the sample variance using the formula:
      // variance = (sumsquares - (sum^2)/count) / (count - 1)
      // Only defined if there are at least 2 responses.
      let variance = null;
      if (count > 1) {
        variance = (sumsquares - (sum * sum) / count) / (count - 1);
      }
      
      // Standard deviation is the square root of the variance.
      const std = variance !== null ? Math.sqrt(variance) : null;
      
      // Standard error of the mean is std divided by the square root of count.
      const stdError = (std !== null && count > 0) ? std / Math.sqrt(count) : null;
      
      // Compute the shrinkage estimate.
      // If count is 0, the formula naturally yields the global mean.
      const shrinkageEstimate = (count + kappa) > 0 ?
        (count / (count + kappa)) * rawMean + (kappa / (count + kappa)) * globalMean
        : null;
      
      // Save the computed statistics for the current attribute.
      results[attr] = {
        rawMean,
        globalMean,
        variance,
        std,
        stdError,
        shrinkageEstimate,
        count  // For reference, include the attribute's identification count.
      };
    });
    this.calculatedStats = results;
    return results;
  }
}
