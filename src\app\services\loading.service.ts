import { Injectable } from '@angular/core';
import { BehaviorSubject, combineLatest } from 'rxjs';
import { map } from 'rxjs/operators';

interface LoadingState {
  products: boolean;
  panels: boolean;
  profile: boolean;
  constants: boolean;
  groups: boolean;
  groupPanels: boolean;
  groupSamples: boolean;
  groupUsers: boolean;
  projects: boolean;
  templates: boolean
}

@Injectable({
  providedIn: 'root'
})
export class LoadingService {
  private loadingStates: LoadingState = {
    products: false,
    panels: false,
    profile: false,
    constants: false,
    groups: false,
    groupPanels: false,
    groupSamples: false,
    groupUsers: false,
    projects: false,
    templates: false
  };

  private loadingStateSubjects = {
    products: new BehaviorSubject<boolean>(false),
    panels: new BehaviorSubject<boolean>(false),
    profile: new BehaviorSubject<boolean>(false),
    constants: new BehaviorSubject<boolean>(false),
    groups: new BehaviorSubject<boolean>(false),
    groupPanels: new BehaviorSubject<boolean>(false),
    groupSamples: new BehaviorSubject<boolean>(false),
    groupUsers: new BehaviorSubject<boolean>(false),
    projects: new BehaviorSubject<boolean>(false),
    templates: new BehaviorSubject<boolean>(false)
  };

  loading$ = combineLatest(Object.values(this.loadingStateSubjects)).pipe(
    map(states => states.some(state => state))
  );

  setLoading(resolver: keyof LoadingState, loading: boolean) {
    this.loadingStates[resolver] = loading;
    this.loadingStateSubjects[resolver].next(loading);
  }

  getLoadingState(resolver: keyof LoadingState): boolean {
    return this.loadingStates[resolver];
  }
}
