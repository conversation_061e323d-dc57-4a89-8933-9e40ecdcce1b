@import '../../../_colors.scss';

:host{
    // --product-block-bg: transparent;
    // --product-block-border: #{$mediumLightGray};
    // --product-block-border-radius: 18px;
    --product-block-padding: 15px;
    --product-block-image-size: 65px;
    --product-block-image-border-radius: 10px;
    --product-block-image-bg: #{$mediumGray};
    
    display: grid;
    // background-color: var(--product-block-bg);
    // border: 1px solid var(--product-block-border);
    // border-radius: var(--product-block-border-radius);
    padding: var(--product-block-padding) calc(var(--product-block-padding) - 9px);
    grid-template-columns: var(--product-block-image-size) 1fr;
    gap: var(--product-block-padding);
    width: 100%;
    height: 95px;

    .product-image{
        width: var(--product-block-image-size);
        height: var(--product-block-image-size);
        border-radius: var(--product-block-image-border-radius);
        background-color: var(--product-block-image-bg);
        overflow: hidden;
        display: flex;
        align-items: center;
        justify-content: center;
        box-shadow: 0px 4px 10px 0px rgba(0, 0, 0, 0.15);

        img{
            height: inherit;
            width: inherit;
            object-fit: contain;
        }
    }

    &.for-preview-step{
        --product-block-image-size: 157px;
        --product-block-image-border-radius: 18px;
        height: fit-content;

        .product-name{
            font-size: 2rem;
            font-weight: 700;
        }
        
        .product-type{
            font-weight: 700;
            margin-top: 12px;
            font-size: .75rem;
        }

        .producer{
            display: flex;
            align-items: center;
            gap: 4px;
            margin-top: 12px;
            font-size: 0.75rem;
            color: $mediumGray;
            font-weight: 500;
        }
    }
}

// .product-block {
// border: 1px solid #e0e0e0;
// border-radius: 8px;
// padding: 16px;
// max-width: 300px;
// box-shadow: 0 2px 4px rgba(0,0,0,0.1);
// }

// .product-image {
// width: 100%;
// height: 200px;
// overflow: hidden;
// border-radius: 4px;
// }

// .product-image img {
// width: 100%;
// height: 100%;
// object-fit: cover;
// }

// .product-info {
// margin-top: 12px;
// }

// .product-name {
// margin: 0;
// font-size: 1.2rem;
// font-weight: 500;
// }

// .product-type {
// margin: 4px 0 0;
// color: #666;
// font-size: 0.9rem;
// }