import { Injectable, inject } from '@angular/core';
import { FormBuilder, FormControl, Validators, FormGroup } from '@angular/forms';
import { Subject, BehaviorSubject, Observable, switchMap } from 'rxjs';
import { map, shareReplay, tap, throttleTime } from 'rxjs/operators';
import { ApiService } from './api.service';
import { PaginationParams } from '../interfaces/paginationParams';
import { SampleInterface } from '../interfaces/sample.interface';
import { HttpParams } from '@angular/common/http';
import { EnvironmentService } from './environment.service';

@Injectable({
  providedIn: 'root'
})
export class ProductsService {

  products: any[] = [];
  productsObject: { [key: string]: any } = {};

  private pdsURL: string = "https://pds.abstraxinfra.com/generate-pdf/";

  page: number = 1;
  limit: number = 10;

  form = inject(FormBuilder);
  apiService = inject(ApiService);
  private environmentService = inject(EnvironmentService);

  formStep: number = 1;
  totalSteps: number = 3;
  get formSuperHeader() {
    return this.editing ? 'Editing product' : 'Creating product';
  }
  get formHeader() {
    if(this.formStep > 1) {
      return this.step1Form.get('name')?.value;
    }
    return this.editing ? 'Edit product' : 'Create a Product';
  }
  nextStepFlavorTextArr: string[] = ['All done?', 'All done?', 'Look good?'];

  editing: boolean = false;
  editingProduct: any = {};
  responseDetails: any = {};

  step1Form: FormGroup = this.form.group({
    name: new FormControl('', Validators.required),
    description: new FormControl('', Validators.required),
    productCode: new FormControl(''),
    type: new FormControl('', Validators.required),
    image: new FormControl(''),
  });

  step2Form: FormGroup = this.form.group({
    packaging: new FormControl('', Validators.required),
    lotNumber: new FormControl('', Validators.required),
    manuDate: new FormControl('', Validators.required),
    packageDate: new FormControl('', Validators.required),
    producer: new FormControl('', Validators.required),
    notes: new FormControl(''),
  });

  loading: boolean = false;
  buttonDisabled: boolean = false;

  stepSubject = new Subject<'next' | 'prev'>();
  saveSubject = new Subject<void>();
  scrollResetSubject = new Subject<void>();

  formErrors: { [key: string]: { [key: string]: boolean } } = {
    step1: {
      name: false,
      description: false,
      productCode: false,
      type: false,
      subtype: false,
      image: false,
    },
    step2: {
      packaging: false,
      lotNumber: false,
      manuDate: false,
      packageDate: false,
      producer: false,
      notes: false,
    },
  };

  hasError: boolean = false;
  errorMessage: string = '';

  private pageSubject = new BehaviorSubject<number>(1);
  private limitSubject = new BehaviorSubject<number>(10);

  public products$ = this.createProductsStream();
  public productsArray: SampleInterface[] = [];

  constructor() {
    this.initializeSubscriptions();
  }

  initializeSubscriptions(): void {
    this.initializeStepSubscription();
    this.initializeSaveSubscription();
    this.initializeFormValueChangeSubscriptions();
  }

  private initializeStepSubscription(): void {
    this.stepSubject.pipe(
      throttleTime(1000)
    ).subscribe(direction => {
      if (direction === 'next' && this.formStep < this.totalSteps) {
        this.formStep++;
        this.scrollResetSubject.next();
      } else if (direction === 'prev' && this.formStep > 1) {
        this.formStep--;
        this.scrollResetSubject.next();
      }
    });
  }

  private initializeSaveSubscription(): void {
    this.saveSubject.pipe(
      throttleTime(3000)
    ).subscribe(async () => {
      if (this.formStep === this.totalSteps) {

        // create a copy of step 2 values to make sure dates are in the correct format
        const step2Values = { ...this.step2Form.value };
        step2Values.manuDate = new Date(step2Values.manuDate).toISOString();
        step2Values.packageDate = new Date(step2Values.packageDate).toISOString();

        const payload = {
          ...this.step1Form.value,
          ...step2Values,
        };
        console.log("New Product Payload", payload);
        let formattedPayload = this.formatProduct(payload);
        let new_product: any = await this.saveProduct(formattedPayload);
        if(new_product){
          this.productsObject[new_product['product_id']] = new_product;
          this.products.push(new_product);
          this.setPage(this.page);
        } else {
          //TODO: Show some error message here
        }

      }
    });
  }

  private validateCurrentStep(): boolean {
    const currentStep = `step${this.formStep}` as keyof typeof this.formErrors;
    const form = this.getCurrentForm();
    let isValid = form.valid;

    // Update individual field errors
    Object.keys(this.formErrors[currentStep]).forEach(fieldName => {
      const control = form.get(fieldName);
      if (control) {
        this.formErrors[currentStep][fieldName as keyof typeof this.formErrors[typeof currentStep]] =
          control.invalid;
      }
    });

    return isValid;
  }

  showError(): void {
    this.hasError = true;
    switch(this.formStep) {
      default:
        this.errorMessage = 'Please complete all required fields before proceeding.';
        break;
    }
  }

  nextStep() {
    if (this.validateCurrentStep()) {
      this.stepSubject.next('next');
    } else {
      this.showError();
    }
  }

  getPdsUrl(sample_key: string, pds_type: string = "lite"): string {
    return this.environmentService.getPdsFormattedUrl(sample_key, pds_type);
  }

  prevStep() {
    const currentStep = `step${this.formStep}` as keyof typeof this.formErrors;
    // Reset current step's error state
    if(currentStep !== 'step3'){
      Object.keys(this.formErrors[currentStep]).forEach(fieldName => {
        this.formErrors[currentStep][fieldName as keyof typeof this.formErrors[typeof currentStep]] = false;
      });
    }

    this.stepSubject.next('prev');
  }

  save() {
    this.saveSubject.next();
  }

  setFormStep(step: number): void {
    if (step >= 1 && step <= this.totalSteps) {
      this.formStep = step;
      this.scrollResetSubject.next();
    }
  }

  reviewStep(){
    this.formStep = 3;
    this.scrollResetSubject.next();
  }

  filter(event: any) {
    console.log(event);
  }

  search(event: any) {
    console.log(event);
  }

  add(event: any) {
    console.log(event);
  }

  formatProduct(payload: any) {
    console.log(payload);
    return {
      name: payload.name,
      description: payload.description,
      product_type: payload.type,
      product_subtype: payload.subtype,
      image: payload.image,
      packaging_option: payload.packaging,
      lot_number: payload.lotNumber,
      manufacturing_date: payload.manuDate,
      packaging_date: payload.packageDate,
      producer: payload.producer,
      notes: payload.notes || '',
      external_id: payload.productCode,
      organization_id: payload.organizationId,
    };
  }

  async fillFormFromProductObject(product: any){
    console.log(product);
    this.step1Form.patchValue({
      name: product.name,
      description: product.description,
      productCode: product.external_id,
      type: product.product_type,
      image: product.image,
    });
    this.step2Form.patchValue({
      packaging: product.packaging_option,
      lotNumber: product.lot_number,
      manuDate: product.manufacturing_date,
      packageDate: product.packaging_date,
      producer: product.producer,
      notes: product.notes,
    });
    return;
  }

  clearForm(){
    this.step1Form.reset();
    this.step2Form.reset();
  }

  getProducts(params: PaginationParams){
    return this.apiService.getProducts(params).pipe(
      map((response: any) => {
        this.products = response.products;
        this.productsObject = response.products.reduce((acc: { [key: string]: any }, product: any) => {
          acc[product.id] = product;
          return acc;
        }, {});
        return response;
      })
    );
  }

  async getProductComments(id: string){
    const comments = await this.apiService.getProductComments(id);
    console.log(comments);
    return comments;
  }

  async getProductDetails(id: string){
    const product = await this.apiService.getProductDetails(id);
    console.log(product);
    return product;
  }

  async getProductInsights(id: string){
    const insights = await this.apiService.getProductInsights(id);
    console.log(insights);
    return insights;
  }
  
  async getProductQuestionDetails(id: string){
    const questionDetails = await this.apiService.getProductQuestionDetails(id);
    console.log(questionDetails);
    return questionDetails;
  }

  async saveProduct(product: any){
    let savedProduct;
    if(this.editing){
      savedProduct = await this.apiService.updateProduct(product, this.editingProduct.id);
    }else{
      savedProduct = await this.apiService.saveProduct(product);
    }
    console.log(savedProduct);
    return savedProduct;
  }

  getResponseCount(id: string){
    return this.apiService.getResponseCount(id, "products");
  }

  getPanelCount(id: string){
    return this.apiService.getPanelCount(id);
  }

  isFieldInvalid(formStep: number, fieldName: string): boolean {
    const stepKey = `step${formStep}` as keyof typeof this.formErrors;
    return this.formErrors[stepKey][fieldName as keyof typeof this.formErrors[typeof stepKey]];
  }

  private getCurrentForm(): FormGroup {
    switch (this.formStep) {
      case 1:
        return this.step1Form;
      case 2:
        return this.step2Form;
      default:
        return this.step1Form;
    }
  }

  private initializeFormValueChangeSubscriptions(): void {
    // Subscribe to each form's valueChanges
    [this.step1Form, this.step2Form].forEach((form, index) => {
      form.valueChanges.subscribe(() => {
        const stepKey = `step${index + 1}` as keyof typeof this.formErrors;

        // Reset all error flags for the current step
        Object.keys(this.formErrors[stepKey]).forEach(fieldName => {
          this.formErrors[stepKey][fieldName as keyof typeof this.formErrors[typeof stepKey]] = false;
        });

        this.hasError = false;
        this.errorMessage = '';
      });
    });
  }

  private createProductsStream() {
    return this.pageSubject.pipe(
      switchMap(page => this.getProducts({
        page,
        limit: this.limitSubject.getValue()
      }))
    );
  }

  public setPage(page: number) {
    this.page = page;
    this.pageSubject.next(page);
  }

  public setLimit(limit: number) {
    this.limitSubject.next(limit);
    // Optionally reset to page 1 when changing limit
    this.setPage(1);
  }

  generatePDS(sample_key: string, lite_or_advanced: string = "lite") {
    const params = new HttpParams()
      .set('sample_id', sample_key);

    return this.apiService.generatePDS(`${this.environmentService.getPdsGeneratePdfUrl()}`, params);
  }
}

