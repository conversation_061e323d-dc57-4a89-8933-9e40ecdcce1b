import { Component, inject } from '@angular/core';
import { PanelsService } from '../../services/panels.service';
import { FormGroup, isFormArray, ReactiveFormsModule } from '@angular/forms';
import { ProductBlockComponent } from '../product-block/product-block.component';
import { CommonModule } from '@angular/common';
import { ConstantsService } from '../../services/constants.service';
import { ProductsService } from '../../services/products.service';
import { ProfileService } from '../../services/profile.service';
import { QuestionsService } from '../../services/questions.service';

@Component({
  selector: 'new-panel-step-6',
  standalone: true,
  imports: [ReactiveFormsModule, ProductBlockComponent, CommonModule],
  templateUrl: './new-panel-step-6.component.html',
  styleUrl: './new-panel-step-6.component.scss'
})
export class NewPanelStep6Component {

  panelsService = inject(PanelsService);
  constantsService = inject(ConstantsService);
  productsService = inject(ProductsService);
  profileService = inject(ProfileService);
  questionsService = inject(QuestionsService);
  formData: any;
  questions: any = {};

  ngOnInit(): void {
    // Initialize with empty data to prevent template errors
    this.formData = {};

      // Fetch products if not already loaded
      if (Object.keys(this.productsService.productsObject).length === 0) {
        this.productsService.setPage(1);
      }
      if(this.panelsService.editing){
        this.panelsService.editingPanel.questions.forEach((question: any) => {
          this.questions[question.id] = question;
        });
      }
      // Initialize form data
      this.formData = this.getFromData();
  }

  getQuestionName(question:any){
    // console.log('question', question);
    if(this.panelsService.editing){
      return this.questions[question].name;
    }
    
    return this.questionsService.adaptedQuestionsObject[question].name || question;
  }

  getFromData() {
    const experimentType = this.panelsService.step1Form.get('experiment_type')?.value;
    const isTriangleTest = experimentType === 'Triangle Test';

    // Create the base form data with step1 values
    let formData = {
      ...this.panelsService.step1Form.value
    };

    // Add the appropriate step2 form values based on experiment type
    if (isTriangleTest) {
      formData = {
        ...formData,
        ...this.panelsService.step2FormTt.value
      };
    } else {
      formData = {
        ...formData,
        ...this.panelsService.step2Form.value
      };
    }

    // Add the remaining form values
    formData = {
      ...formData,
      ...this.panelsService.step3Form.value,
      ...this.panelsService.step4Form.value,
      ...this.panelsService.step5Form.value
    };

    console.log('Review form data:', formData);
    return formData;
  }

  get mockProduct() {
    console.log('mockProduct', this.formData);
    console.log('productsObject', this.productsService.productsObject);

    // Handle different experiment types
    if (this.isTriangleTest && this.formData.products && this.formData.products.length > 0) {
      // For Triangle Test, use the first product for display
      const productId = this.formData.products[0];
      if (productId && this.productsService.productsObject[productId]) {
        const product = this.productsService.productsObject[productId];
        return {
          name: this.formData.name,
          description: this.formData.description,
          producer: this.profileService.profileData?.org_name || 'Organization',
          image: product.image || 'assets/images/placeholder.png',
        };
      }
    } else if (this.formData.product) {
      // For Normal Sensory
      const productId = this.formData.product;
      if (productId && this.productsService.productsObject[productId]) {
        const product = this.productsService.productsObject[productId];
        return {
          name: this.formData.name,
          description: this.formData.description,
          producer: this.profileService.profileData?.org_name || 'Organization',
          image: product.image || 'assets/images/placeholder.png',
        };
      }
    }

    // Fallback if no product is found
    return {
      name: this.formData?.name || 'Panel Name',
      description: this.formData?.description || 'Panel Description',
      producer: this.profileService.profileData?.org_name || 'Organization',
      image: 'assets/images/placeholder.png',
    };
  }

  get stepProduct() {
    const productId = this.formData.product;
    const product = this.panelsService.editing ? this.panelsService.editingPanel.product : this.productsService.productsObject[productId];
    console.log('product', product);
    let stepProduct = {
      name: this.formData.name,
      description: this.formData.description,
      producer: this.profileService.profileData.org_name,
      image: product?.image || 'assets/images/placeholder.png',
    };
    return stepProduct;
  }

  get startDate(): string {
    if (!this.formData) {
      return 'Immediately';
    }

    try {
      if (this.formData.startOption === 'now') {
        return 'Immediately';
      }

      if (this.formData.start_date) {
        return new Date(this.formData.start_date).toLocaleDateString();
      }

      return 'Immediately';
    } catch (error) {
      console.error('Error getting start date:', error);
      return 'Immediately';
    }
  }

  get endDate(): string {
    if (!this.formData) {
      return 'Ongoing, no set end date';
    }

    try {
      if (this.formData.endOption === 'never') {
        return 'Ongoing, no set end date';
      }

      if (this.formData.end_date) {
        return new Date(this.formData.end_date).toLocaleDateString();
      }

      return 'Ongoing, no set end date';
    } catch (error) {
      console.error('Error getting end date:', error);
      return 'Ongoing, no set end date';
    }
  }

  get productName() {
    return this.panelsService.editing ? this.panelsService.editingPanel.product.name : this.productsService.productsObject[this.formData.product].name;
  }

  /**
   * Get the name of a product by its ID
   */
  getProductName(productId: string): string {
    if (productId && this.productsService.productsObject[productId]) {
      return this.productsService.productsObject[productId].name;
    }
    return 'Unknown Product';
  }

  /**
   * Check if the current experiment is a Triangle Test
   */
  get isTriangleTest(): boolean {
    const experimentType = this.formData?.experiment_type;
    return experimentType === 'Triangle Test';
  }

  get restrictionDescription(): string {
    if (!this.formData) {
      return 'Public panel, no restrictions';
    }

    try {
      if (this.formData.panelistPool === 'public') {
        return 'Public panel, no restrictions';
      } else if (this.formData.panelistPool === 'private') {
        if (this.formData.restricted) {
          return 'Offer to invited only who have achieved level ' + (this.formData.restrictionLevel || '1');
        } else {
          return 'Offer to all eligible panelists on platform';
        }
      }
      return 'Public panel, no restrictions';
    } catch (error) {
      console.error('Error getting restriction description:', error);
      return 'Public panel, no restrictions';
    }
  }

  getExperimentTypeName(typeId: string) {
    if(!typeId) return 'Normal Sensory';

    if(typeId === 'normal_sensory') return 'Normal Sensory';
    if(typeId === 'triangle_test') return 'Triangle Test';

    return typeId;
  }

  editGeneral(event: any){
    event.preventDefault();
    this.panelsService.setFormStep(1);
  }

  editProduct(event: any){
    event.preventDefault();
    this.panelsService.setFormStep(2);
  }

  editQuestions(event: any){
    event.preventDefault();
    this.panelsService.setFormStep(3);
  }

  editTargetPanelists(event: any){
    event.preventDefault();
    this.panelsService.setFormStep(4);
  }

  editBudget(event: any){
    event.preventDefault();
    this.panelsService.setFormStep(5);
  }
}
