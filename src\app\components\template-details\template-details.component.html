<div class="template-details-container" *ngIf="template">
    <div class="close-button-container">
        <button-two-click [type]="'close'" (onConfirm)="goBack()">
            <svg width="14" height="14" viewBox="0 0 14 14" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path d="M7 5.58549L12.2929 0.292606C12.683 -0.0975353 13.3164 -0.0975353 13.7074 0.292606C14.0975 0.683605 14.0975 1.31695 13.7074 1.70711L8.41451 7L13.7074 12.2929C14.0975 12.683 14.0975 13.3164 13.7074 13.7074C13.3164 14.0975 12.683 14.0975 12.2929 13.7074L7 8.41451L1.70711 13.7074C1.31697 14.0975 0.683627 14.0975 0.292606 13.7074C-0.0975353 13.3164 -0.0975353 12.683 0.292606 12.2929L5.58549 7L0.292606 1.70711C-0.0975353 1.31697 -0.0975353 0.683627 0.292606 0.292606C0.683605 -0.0975353 1.31695 -0.0975353 1.70711 0.292606L7 5.58549Z" fill="black"/>
            </svg>
        </button-two-click>
    </div>



    <div class="template-title-container">
        <h2 class="template-name">{{ template.name }}</h2>
        <button class="more-actions-button" (click)="toggleActionsPopover()">
            <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path d="M12 13C12.5523 13 13 12.5523 13 12C13 11.4477 12.5523 11 12 11C11.4477 11 11 11.4477 11 12C11 12.5523 11.4477 13 12 13Z" stroke="black" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                <path d="M19 13C19.5523 13 20 12.5523 20 12C20 11.4477 19.5523 11 19 11C18.4477 11 18 11.4477 18 12C18 12.5523 18.4477 13 19 13Z" stroke="black" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                <path d="M5 13C5.55228 13 6 12.5523 6 12C6 11.4477 5.55228 11 5 11C4.44772 11 4 11.4477 4 12C4 12.5523 4.44772 13 5 13Z" stroke="black" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
            </svg>
        </button>
        <popover *ngIf="showActionsPopover" (clickOutside)="showActionsPopover = false" [@fadeInOut]>
            <div class="popover-item" (click)="editTemplate()">Edit Template</div>
            <div class="popover-item" (click)="duplicateTemplate()">Duplicate Template</div>
            <div class="popover-item" (click)="useTemplateToCreatePanel()">Create Panel from Template</div>
            <div class="popover-item delete" (click)="deleteTemplate()">Delete Template</div>
        </popover>
    </div>
    <div class="template-content">

        <div class="template-section">
            <div class="section-icon">

                <svg width="14" height="14" viewBox="0 0 14 14" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path d="M0.312908 8.40722L1.78345 8.66749C1.87589 8.95618 1.9925 9.23778 2.1326 9.50871L1.27645 10.7332C1.1712 10.884 1.18898 11.0888 1.31911 11.2189L2.78111 12.6809C2.91124 12.811 3.11532 12.8288 3.26679 12.7236L4.49131 11.8674C4.76153 12.0068 5.0431 12.1241 5.33253 12.2166L5.59279 13.6871C5.62479 13.8684 5.78195 14 5.96612 14H8.03319C8.21736 14 8.37451 13.8677 8.40651 13.6871L8.66678 12.2166C8.95547 12.1241 9.23707 12.0075 9.50729 11.8674L10.7325 12.7236C10.8826 12.8288 11.0881 12.811 11.2182 12.6809L12.6802 11.2189C12.8103 11.0888 12.8281 10.884 12.7229 10.7332L11.8667 9.50871C12.0061 9.23849 12.1234 8.95691 12.2159 8.66749L13.6864 8.40722C13.8677 8.37523 13.9993 8.21807 13.9993 8.0339L14 5.9661C14 5.78193 13.8677 5.62478 13.6871 5.59278L12.2166 5.33251C12.1241 5.04382 12.0075 4.76222 11.8674 4.49129L12.7236 3.26678C12.8288 3.11602 12.811 2.91124 12.6809 2.78109L11.2189 1.3191C11.0888 1.18897 10.884 1.17119 10.7332 1.27643L9.508 2.13258C9.23778 1.99321 8.9562 1.87587 8.66749 1.78343L8.40722 0.31289C8.37523 0.131562 8.21736 0 8.0339 0H5.9661C5.78193 0 5.62478 0.132263 5.59278 0.31289L5.33251 1.78343C5.04382 1.87587 4.76222 1.99248 4.49129 2.13258L3.26678 1.27643C3.11602 1.17119 2.91124 1.18896 2.78109 1.3191L1.31909 2.78109C1.18897 2.91122 1.17119 3.11601 1.27643 3.26678L2.13258 4.49129C1.99321 4.76151 1.87587 5.04309 1.78343 5.33251L0.312891 5.59278C0.131562 5.62478 0 5.78193 0 5.9661V8.03317C0 8.21734 0.13228 8.37522 0.312908 8.40722ZM0.75876 6.28391L2.14753 6.03786C2.29259 6.01227 2.40921 5.9056 2.4476 5.76338C2.55213 5.37726 2.70715 5.00393 2.90767 4.6548C2.98091 4.52751 2.9738 4.36894 2.88989 4.24876L2.08138 3.0918L3.09327 2.07991L4.25022 2.88843C4.37111 2.97234 4.52896 2.97945 4.65626 2.9062C5.00542 2.70567 5.37802 2.55137 5.76485 2.44613C5.90636 2.40773 6.01374 2.29112 6.03933 2.14606L6.28538 0.757293H7.71681L7.96286 2.14606C7.98846 2.29112 8.09512 2.40774 8.23734 2.44613C8.62346 2.55066 8.99679 2.70569 9.34592 2.9062C9.47321 2.97944 9.63178 2.97233 9.75197 2.88843L10.9089 2.07991L11.9208 3.0918L11.1123 4.24876C11.0284 4.36893 11.0213 4.5275 11.0945 4.6548C11.2951 5.00395 11.4494 5.37655 11.5546 5.76338C11.593 5.90489 11.7096 6.01227 11.8547 6.03786L13.2434 6.28391V7.71534L11.8547 7.96139C11.7103 7.98699 11.593 8.09365 11.5546 8.23587C11.4501 8.622 11.295 8.99532 11.0945 9.34446C11.0213 9.47174 11.0284 9.63032 11.1123 9.7505L11.9208 10.9075L10.9089 11.9193L9.75197 11.1108C9.63179 11.0269 9.47322 11.0198 9.34592 11.0931C8.99677 11.2936 8.62417 11.4486 8.23734 11.5531C8.09583 11.5915 7.98845 11.7081 7.96286 11.8532L7.71681 13.242H6.28538L6.03933 11.8532C6.01373 11.7081 5.90707 11.5915 5.76485 11.5531C5.37873 11.4486 5.0054 11.2936 4.65626 11.0931C4.52827 11.0198 4.37041 11.0269 4.25022 11.1108L3.09327 11.9193L2.08138 10.9075L2.88989 9.7505C2.9738 9.63032 2.98091 9.47176 2.90767 9.34446C2.70713 8.99531 2.55284 8.62271 2.4476 8.23587C2.4092 8.09436 2.29258 7.98698 2.14753 7.96139L0.75876 7.71534V6.28391Z" fill="#292727"/>
                    <path d="M6.99997 10.0463C8.67958 10.0463 10.0462 8.67959 10.0462 7.00005C10.0462 5.32052 8.6795 3.9538 6.99997 3.9538C5.32043 3.9538 3.95371 5.32052 3.95371 7.00005C3.95371 8.67959 5.32043 10.0463 6.99997 10.0463ZM6.99997 4.71236C8.26145 4.71236 9.28748 5.73846 9.28748 6.99987C9.28748 8.26128 8.26138 9.28739 6.99997 9.28739C5.73856 9.28739 4.71245 8.26128 4.71245 6.99987C4.71245 5.73846 5.73856 4.71236 6.99997 4.71236Z" fill="#292727"/>
                </svg>

            </div>
            <h3>General</h3>
        </div>

        <div class="template-info-grid">
            <div class="info-row">
                <div class="info-label">Template Name</div>
                <div class="info-value">{{ template.name }}</div>
            </div>
            <div class="info-row">
                <div class="info-label">Template Description</div>
                <div class="info-value">{{ template.description || 'No description provided' }}</div>
            </div>
            <div class="info-row">
                <div class="info-label">Starts</div>
                <div class="info-value">{{template['start_date'] || 'Unknown'}}</div>
            </div>
            <div class="info-row">
                <div class="info-label">Ends</div>
                <div class="info-value">{{template['end_date'] || 'Unknown'}}</div>
            </div>
        </div>

        <div class="template-section">
            <div class="section-icon">
                <svg width="14" height="14" viewBox="0 0 14 14" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path d="M7 14C8.85674 14 10.6373 13.2625 11.9499 11.9499C13.2625 10.6373 14 8.85674 14 7C14 5.14325 13.2625 3.36268 11.9499 2.0501C10.6373 0.737527 8.85674 0 7 0C5.14325 0 3.36268 0.737542 2.0501 2.0501C0.737527 3.36266 0 5.14325 0 7C0.00228856 8.85557 0.740398 10.635 2.05302 11.947C3.36502 13.2595 5.14443 13.9977 7 14ZM7 1.36634C8.49333 1.36634 9.92618 1.95969 10.9824 3.01597C12.0387 4.0722 12.632 5.50432 12.632 6.99839C12.632 8.49172 12.0393 9.92456 10.983 10.9808C9.92678 12.037 8.49465 12.6304 7.00117 12.631C5.50725 12.631 4.075 12.0382 3.01831 10.982C1.96208 9.92632 1.3681 8.49363 1.3681 7.00015C1.36981 5.50681 1.96316 4.07514 3.01933 3.01948C4.075 1.96325 5.50652 1.36985 7 1.36824V1.36634ZM7.63741 10.4302V10.4308C7.63741 10.7232 7.46061 10.9869 7.19054 11.0991C6.91989 11.2112 6.60863 11.1489 6.40208 10.9423C6.19495 10.7352 6.13316 10.4239 6.24531 10.1538C6.35688 9.8832 6.62065 9.70697 6.91361 9.70697C7.10586 9.70697 7.2901 9.78307 7.42571 9.91925C7.56131 10.0554 7.63741 10.2397 7.63741 10.4319V10.4302ZM4.65631 4.79142C4.59279 4.61118 4.60424 4.41378 4.6872 4.24213C4.88518 3.82616 5.19645 3.47484 5.58495 3.22765C5.97345 2.98105 6.42376 2.84831 6.88436 2.84601H6.89924H6.89867C7.54122 2.8483 8.15803 3.09949 8.61977 3.54577C9.08151 3.99265 9.3533 4.60029 9.37734 5.24227C9.39393 5.76925 9.24002 6.28707 8.93905 6.71964C8.63808 7.15278 8.20552 7.47664 7.706 7.64428C7.706 7.64428 7.63448 7.66945 7.63448 7.7118V8.28627H7.63391C7.63391 8.68392 7.31178 9.00607 6.91411 9.00607C6.51645 9.00607 6.1943 8.68394 6.1943 8.28627V7.7118C6.20117 7.05952 6.62287 6.48391 7.24311 6.28079C7.67053 6.13603 7.95319 5.72864 7.93888 5.27718C7.90969 4.72561 7.45767 4.29075 6.90495 4.28274C6.51244 4.28731 6.15711 4.51446 5.98947 4.86865C5.86416 5.13471 5.59008 5.29778 5.29656 5.28061C5.00303 5.26402 4.74957 5.07006 4.65631 4.79142Z" fill="#292727"/>
                    </svg>

            </div>
            <h3>Panel Questions</h3>
        </div>

        <div class="template-questions">
            <!-- Loading state -->
            <div class="loading-container" *ngIf="isLoadingQuestions">
                <div class="loading-spinner"></div>
                <div class="loading-text">Loading questions...</div>
            </div>

            <!-- Error state -->
            <div class="error-container" *ngIf="questionsError">
                <div class="error-icon">
                    <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <path d="M12 22C17.5228 22 22 17.5228 22 12C22 6.47715 17.5228 2 12 2C6.47715 2 2 6.47715 2 12C2 17.5228 6.47715 22 12 22Z" stroke="#FF4D4F" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                        <path d="M12 8V12" stroke="#FF4D4F" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                        <path d="M12 16H12.01" stroke="#FF4D4F" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                    </svg>
                </div>
                <div class="error-text">{{ questionsError }}</div>
            </div>

            <!-- No questions state -->
            <div class="empty-questions" *ngIf="!isLoadingQuestions && !questionsError && (!questions || questions.length === 0)">
                <div class="empty-icon">
                    <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <path d="M12 22C17.5228 22 22 17.5228 22 12C22 6.47715 17.5228 2 12 2C6.47715 2 2 6.47715 2 12C2 17.5228 6.47715 22 12 22Z" stroke="#CCCCCC" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                        <path d="M8 12H16" stroke="#CCCCCC" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                    </svg>
                </div>
                <div class="empty-text">No question steps found for this template</div>
            </div>

            <!-- Questions display -->
            <div class="questions-grid" *ngIf="!isLoadingQuestions && !questionsError && questions && questions.length > 0">
                <div class="question-category">Consumption</div>
                <div class="question-category-value">Aroma</div>

                <div class="question-label">Questions</div>
                <div class="question-steps">
                    <div class="question-step" *ngFor="let question of questions; let i = index">
                        <div class="step-label">Step {{ i + 1 }}</div>
                        <div class="step-arrow">
                            <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                <path d="M8 4l8 8-8 8" stroke="#CCCCCC" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                            </svg>
                        </div>
                        <div class="question-box">
                            <div class="question-text">{{ question.name || 'Question ' + (i + 1) }}</div>
                            <div class="question-type">{{ question.type || 'Unknown type' }}</div>
                            <div class="eye-icon">
                                <svg width="18" height="10" viewBox="0 0 18 10" fill="none" xmlns="http://www.w3.org/2000/svg">
                                    <path d="M8.63636 0C5.13738 0 2.03155 1.83283 0.105733 4.65002C-0.0352442 4.85784 -0.0352442 5.13596 0.105733 5.34378C2.03135 8.16099 5.13738 10 8.63636 10C12.1353 10 15.2412 8.16093 17.167 5.34378C17.308 5.13596 17.308 4.85784 17.167 4.65002C15.2414 1.83281 12.1353 0 8.63636 0ZM8.63636 1.2C11.6 1.2 14.2266 2.68283 15.9611 5.00002C14.2266 7.31483 11.5982 8.80003 8.63636 8.80003C5.6745 8.80003 3.04609 7.31487 1.31167 5.00002C3.04613 2.68281 5.67297 1.2 8.63636 1.2ZM8.63636 2.00001C7.05264 2.00001 5.75686 3.35001 5.75686 5.00002C5.75686 6.65003 7.05264 8.00003 8.63636 8.00003C10.2201 8.00003 11.5159 6.65003 11.5159 5.00002C11.5159 3.35001 10.2201 2.00001 8.63636 2.00001ZM8.63636 3.20001C9.5977 3.20001 10.3641 3.99846 10.3641 5.00002C10.3641 6.00158 9.5977 6.80003 8.63636 6.80003C7.67503 6.80003 6.90866 6.00158 6.90866 5.00002C6.90866 3.99846 7.67503 3.20001 8.63636 3.20001Z" fill="#292727"/>
                                </svg>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="template-section">
            <div class="section-icon">
                <svg width="14" height="14" viewBox="0 0 14 14" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path d="M9.79489 7C9.79489 8.54392 8.54384 9.79489 7 9.79489C5.45616 9.79489 4.20511 8.54384 4.20511 7C4.20511 5.45616 5.45616 4.20511 7 4.20511C8.54384 4.20511 9.79489 5.45616 9.79489 7Z" fill="#292727"/>
                    <path d="M12.3286 6.47754C12.0939 3.94383 10.0561 1.93268 7.52246 1.67145V0H6.47754V1.67145C3.94383 1.90615 1.93268 3.94393 1.67145 6.47754H0V7.52246H1.67145C1.90615 10.0562 3.94393 12.0673 6.47754 12.3286V14H7.52246V12.3286C10.0562 12.0939 12.0673 10.0561 12.3286 7.52246H14V6.47754H12.3286ZM7 11.3103C4.6224 11.3103 2.68972 9.3776 2.68972 7C2.68972 4.6224 4.6224 2.68972 7 2.68972C9.3776 2.68972 11.3103 4.6224 11.3103 7C11.3103 9.3776 9.3776 11.3103 7 11.3103Z" fill="#292727"/>
                    </svg>

            </div>
            <h3>Target Panelists</h3>
        </div>

        <div class="template-info-grid">
            <div class="info-row">
                <div class="info-label">Restrictions</div>
                <div class="info-value">{{ template['minimum_certification_level'] || 'No restrictions' }}</div>
            </div>
        </div>

        <div class="template-section">
            <div class="section-icon">
                <svg width="12" height="14" viewBox="0 0 12 14" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path d="M6.03685 6.70729H5.40194C5.15151 6.70729 4.94804 6.48928 4.94804 6.22096C4.94804 6.05982 4.82622 5.92931 4.67583 5.92931C4.52544 5.92931 4.40363 6.05982 4.40363 6.22096C4.40363 6.81083 4.8514 7.2906 5.40194 7.2906H5.49245V7.8739C5.49245 8.03504 5.61426 8.16555 5.76465 8.16555C5.91504 8.16555 6.03686 8.03504 6.03686 7.8739V7.2906C6.63706 7.2906 7.12567 6.76708 7.12567 6.12399C7.12567 5.4809 6.63706 4.95738 6.03686 4.95738H5.49245C5.19234 4.95738 4.94804 4.69563 4.94804 4.37408C4.94804 4.05253 5.19234 3.79078 5.49245 3.79078H6.12736C6.37779 3.79078 6.58126 4.00879 6.58126 4.27711C6.58126 4.43825 6.70307 4.56876 6.85347 4.56876C7.00386 4.56876 7.12567 4.43825 7.12567 4.27711C7.12567 3.68724 6.6779 3.20747 6.12736 3.20747H6.03685V2.62417C6.03685 2.46303 5.91504 2.33252 5.76465 2.33252C5.61425 2.33252 5.49244 2.46303 5.49244 2.62417V3.20747C4.89224 3.20747 4.40363 3.73099 4.40363 4.37408C4.40363 5.01717 4.89224 5.54069 5.49244 5.54069H6.03685C6.33696 5.54069 6.58126 5.80244 6.58126 6.12399C6.58126 6.44555 6.33696 6.70729 6.03685 6.70729Z" fill="#292727"/>
                    <path d="M0.000111314 8.69482V12.7014C0.000111314 12.8626 0.121923 12.9931 0.272315 12.9931H1.90826C2.05865 12.9931 2.18046 12.8626 2.18046 12.7014V12.5366H3.6143L6.16822 13.8622C6.34107 13.9533 6.53025 14 6.72012 14C6.87664 14 7.03384 13.9686 7.18286 13.9052L10.9352 12.3128C11.3952 12.1174 11.6327 11.6092 11.4864 11.1309C11.3394 10.6482 10.8392 10.3536 10.3445 10.4623L10.1907 10.4958V2.38278C10.1907 2.30549 10.1621 2.23112 10.1111 2.17643L8.15941 0.0853079C8.10837 0.030623 8.03895 0 7.96682 0H2.53354C1.87482 0 1.33856 0.574549 1.33856 1.28036V8.40394H0.272204C0.121811 8.40394 0 8.53445 0 8.69559L0.000111314 8.69482ZM10.4534 11.0331C10.68 10.9842 10.9067 11.106 10.9693 11.3109C11.0251 11.4953 10.9264 11.6886 10.7345 11.7702L6.98215 13.3626C6.79841 13.4406 6.58404 13.4319 6.40644 13.3378L3.79468 11.9824C3.75793 11.9634 3.7171 11.9532 3.67627 11.9532H2.18051V9.43989H4.69228C4.82022 9.44135 4.92025 9.45082 4.99511 9.4902L7.08424 10.5941C7.20809 10.6597 7.29519 10.7655 7.33059 10.8916C7.36121 11.0032 7.34692 11.1213 7.28976 11.2241C7.28976 11.2248 7.28908 11.2255 7.2884 11.2263C7.15298 11.4669 6.82428 11.5566 6.5691 11.4224L4.28398 10.215C4.14924 10.1435 3.98593 10.2033 3.91922 10.3477C3.85253 10.492 3.90833 10.667 4.04307 10.7385L6.32819 11.9459C6.7957 12.1924 7.37549 12.0524 7.6824 11.6382L10.4527 11.0338L10.4534 11.0331ZM9.4871 2.33243H8.10363C8.05327 2.33243 8.01312 2.28868 8.01312 2.23545V0.753133L9.4871 2.33243ZM1.88317 1.27957C1.88317 0.895316 2.17511 0.582516 2.53374 0.582516H7.46877V2.23546C7.46877 2.61023 7.7539 2.91573 8.10368 2.91573H9.64572V10.6138L7.89073 10.9966C7.88937 10.9062 7.87712 10.8158 7.85262 10.7261C7.77504 10.4454 7.58313 10.2063 7.32523 10.0699L5.2361 8.96601C5.05917 8.87268 4.87407 8.8581 4.69577 8.85519H2.18052V8.69332C2.18052 8.53218 2.05871 8.40167 1.90832 8.40167H1.88314L1.88317 1.27957ZM0.544606 8.98645H1.63614V12.4097H0.544606V8.98645Z" fill="#292727"/>
                    </svg>

            </div>
            <h3>Budget</h3>
        </div>

        <div class="template-info-grid">
            <div class="info-row">
                <div class="info-label">Pay Per Panel</div>
                <div class="info-value">${{template['value']}}.00</div>
            </div>
            <div class="info-row">
                <div class="info-label">Max. Spend</div>
                <div class="info-value">${{template['budget']}}</div>
            </div>
        </div>
    </div>
</div>

<div class="template-details-container empty" *ngIf="!template">
    <div class="empty-state">
        <div class="empty-icon">
            <svg width="64" height="64" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path d="M12 22C17.5228 22 22 17.5228 22 12C22 6.47715 17.5228 2 12 2C6.47715 2 2 6.47715 2 12C2 17.5228 6.47715 22 12 22Z" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                <path d="M12 8V12" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                <path d="M12 16H12.01" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
            </svg>
        </div>
        <div class="empty-text">Template not found</div>
        <button-standard class="blue" (onAction)="goBack()">Back to Templates</button-standard>
    </div>
</div>


