import { Component, inject, HostListener } from '@angular/core';
import { CommonModule, NgIf, Location, KeyValuePipe } from '@angular/common';
import { PanelsService } from '../../services/panels.service';
import { PopupService } from '../../services/popup.service';
import { ButtonStandardComponent } from '../button-standard/button-standard.component';
import { PopoverComponent } from '../popover/popover.component';
import { fadeScaleInOut } from '../../modules/animations/animations.module';
import { ClipboardModule, ClipboardService } from 'ngx-clipboard';
import { ToastService } from '../../services/toast.service';
import { PercentageBarComponent } from '../percentage-bar/percentage-bar.component';
import { ResponseDataOverallBlockComponent } from '../response-data-overall-block/response-data-overall-block.component';
import { ConstantsService } from '../../services/constants.service';
import { ResponseDataAromaSpectrumComponent } from '../response-data-aroma-spectrum/response-data-aroma-spectrum.component';
import { ResponseDataAromaCategoryDistributionComponent } from '../response-data-aroma-category-distribution/response-data-aroma-category-distribution.component';
import { ResponseDataTopDescriptorsComponent } from '../response-data-top-descriptors/response-data-top-descriptors.component';
import { ResponseDataDemographicBreakdownComponent } from '../response-data-demographic-breakdown/response-data-demographic-breakdown.component';
import { ResponseDataCommentsComponent } from '../response-data-comments/response-data-comments.component';
import { ActivatedRoute, RouterModule, Router } from '@angular/router';
import { LoadingService } from '../../services/loading.service';
import { StatisticsService } from '../../services/statistics.service';
import {
  LambTableComponent,
  TableStyles,
  LambGPIconComponent
} from '@lamb-sensory/lamb-component-library';
// import { createTableConfig } from '../../config/table-styles.config';
import { ResponseDataDescriptorsComponent } from '../response-data-descriptors/response-data-descriptors.component';
import { EnvironmentService } from '../../services/environment.service';
import { StepTypeIconComponent } from '../step-type-icon/step-type-icon.component';
import { ResponseDataTrueFalseComponent } from '../response-data-true-false/response-data-true-false.component';
import { ResponseDataMultipleChoiceComponent } from '../response-data-multiple-choice/response-data-multiple-choice.component';
import { QuestionsService } from '../../services/questions.service';
import { QuestionAdapter } from '../../adapters/question.adapter';
import { TriangleTestService } from '../../services/triangle-test.service';

interface TriangleTestData {
  results: any[];
  expanded: boolean;
  count: number;
  correct: number;
  incorrect: number;
  pValue: number | null;
  chiSquared: number | null;
  degreesOfFreedom: number;
  isSignificant: boolean;
}

interface TriangleTestAggregate {
  panelId: string;
  totalTests: number;
  totalResponses: number;
  correctCount: number;
  overallAccuracy: number;
  productPairs: {
    [key: string]: {
      totalResponses: number;
      correctResponses: number;
      incorrectResponses: number;
      correctPercentage: number;
      expectedCorrect: number;
      expectedIncorrect: number;
      chiSquared: number;
      degreesOfFreedom: number;
      products: string[];
      pValue?: number;
      isSignificant?: boolean;
    }
  };
}

@Component({
  selector: 'panel-details',
  standalone: true,
  imports: [
    ButtonStandardComponent,
    PopoverComponent,
    CommonModule,
    ClipboardModule,
    PercentageBarComponent,
    ResponseDataOverallBlockComponent,
    ResponseDataAromaSpectrumComponent,
    ResponseDataTopDescriptorsComponent,
    ResponseDataAromaCategoryDistributionComponent,
    ResponseDataDemographicBreakdownComponent,
    ResponseDataCommentsComponent,
    RouterModule,
    NgIf,
    ResponseDataDescriptorsComponent,
    KeyValuePipe,
    StepTypeIconComponent,
    ResponseDataTrueFalseComponent,
    ResponseDataMultipleChoiceComponent,
    LambGPIconComponent
  ],
  templateUrl: './panel-details.component.html',
  styleUrl: './panel-details.component.scss',
  animations: [fadeScaleInOut],
})
export class PanelDetailsComponent {
  panelsService = inject(PanelsService);
  popupService = inject(PopupService);
  _clipboardService = inject(ClipboardService);
  toastService = inject(ToastService);
  constantsService = inject(ConstantsService);
  statisticsService = inject(StatisticsService);
  loadingService = inject(LoadingService);
  questionsService = inject(QuestionsService);
  triangleTestService = inject(TriangleTestService);
  route = inject(ActivatedRoute);
  location = inject(Location);
  router = inject(Router);
  private environmentService = inject(EnvironmentService);
  panel: any; // = this.panelsService.editingPanel;
  responseDetails: any; // = this.panelsService.responseDetails;
  showActionsPopover = false;
  tableConfig: any;
  
  allDescriptors: any;
  summerizedTTData: Record<string, TriangleTestData> = {};
  triangleTestAggregates: TriangleTestAggregate | null = null;

  _budgetPieces: any[] = [];
  backButtonData: any;
  ttProducts: any[] = [];


  isTriangleTest: any;

  expanded: boolean = false;
  panelSteps: any;
  questionAdapter = new QuestionAdapter();

  expandForDetails(id: any) {
    console.log(this.summerizedTTData[id]);
    this.summerizedTTData[id].expanded = !this.summerizedTTData[id].expanded;
  }
  isExpanded(id: string) {
    return this.summerizedTTData[id].expanded;
  }

  getInitial(name: string) {
    return name[0].toLocaleUpperCase();
  }

  constructor() {
    this.loadingService.setLoading('panels', true);
  }

  ngOnInit() {
    this.route.data.subscribe(async (data) => {
      this.panel = data['panel'];
      this.isTriangleTest = data['panel'].experiment_type === 'Triangle Test';
      console.log('panel', this.panel, this.isTriangleTest);
      this.panelSteps = await Promise.all(this.panel.questions.map(async (question: any) => {
        if(question.isDefault){
          return question;
        }
        return await this.questionAdapter.customToPanelStep(question);
      }));
      console.log('panelSteps', this.panelSteps);
      this.responseDetails = data['insights'];
      this.backButtonData = data['backButton'];
      this.loadingService.setLoading('panels', false);
      this._budgetPieces = this.budgetPieces;
      this.statisticsService.calculatedStats =
        this.statisticsService.calculateStats(this.responseDetails);
      // this.tableConfig = await this.configureTable(data['insights']);
      if (data['panel'].productsDetails) {
        this.ttProducts = data['panel'].productsDetails;
      }
      
      // Check if this is a triangle test and fetch the specific data
      if (this.isTriangleTest) {
        this.loadTriangleTestData();
      }
      
      console.log(this);
    });
  }

  async loadTriangleTestData() {
    try {
      this.loadingService.setLoading('panels', true);
      // Get dedicated triangle test data from API
      const aggregateData = await this.panelsService.getTriangleTestAggregates(this.panel.id);
      this.triangleTestAggregates = aggregateData as TriangleTestAggregate;
      console.log('Triangle test aggregates:', this.triangleTestAggregates);
      
      if (this.triangleTestAggregates && this.triangleTestAggregates.productPairs) {
        this.processTTAggregates(this.triangleTestAggregates);
      } else if (this.panel.ttData) {
        // Fallback to legacy data format
        this.processTTData(this.panel.ttData);
      }
    } catch (error) {
      console.error('Error loading triangle test data:', error);
      // Fallback to legacy data if API call fails
      if (this.panel.ttData) {
        this.processTTData(this.panel.ttData);
      }
    } finally {
      this.loadingService.setLoading('panels', false);
    }
  }

  get responseCount(){
    return this.responseDetails?.count || 0;
  }

  get privacyStatus(){
    return this.panel.isPublic ? 'Public' : 'Private';
  }

  getProductImageUrl(panel: any){
    return panel.product.image || '';
  }

  getProductInitials(panel: any){
    return panel.product.name.charAt(0);
  }

  getStepTypeLabel(stepType: string){
    switch(stepType){
      case 'multiple-choice':
        return 'Multiple Choice';
      case 'select-one':
        return 'Select One';
      case 'true-false':
        return 'True or False';
      default:
        return stepType;
    }
  }

  close(){
    this.popupService.closeAllPopups();
  }

  get budgetConfigured() {
    const budget = Number(this.panel.budget);
    const value = Number(this.panel.value);
    return !isNaN(budget) && !isNaN(value) && budget > 0 && value > 0;
  }

  get budgetInfo() {
    let budget = this.panel.budget;
    let pay = this.panel.value;
    let possibleResponses = budget / pay;
    let responses = this.responseDetails.count;
    let remaining = possibleResponses - responses;
    let percentUsed = (responses / possibleResponses) * 100;
    return {
      budget,
      pay,
      possibleResponses,
      responses,
      remaining,
      percentUsed,
    };
  }

  get budgetPieces() {
    let { percentUsed } = this.budgetInfo;
    return [
      { label: percentUsed.toString(), value: percentUsed, color: '#6A90E9' },
    ];
  }

  get isEnded() {
    if (!this.panel.end_date) return 's';
    const endDate = new Date(this.panel.end_date._seconds * 1000);
    const today = new Date();
    return endDate < today ? 'ed' : 's';
  }

  get endDate() {
    const date = new Date(this.panel.end_date._seconds * 1000);
    if (date.getFullYear() > 4000) {
      return 'Ongoing';
    }
    return date.toLocaleDateString();
  }

  get startDate() {
    return this.panel.start_date
      ? new Date(this.panel.start_date._seconds * 1000).toLocaleDateString()
      : 'Unknown';
  }

  toggleActionsPopover() {
    this.showActionsPopover = !this.showActionsPopover;
  }

  copyPanelLink(panel: any) {
    this._clipboardService.copy(
      `${this.environmentService.getReviewUrl()}/panels/${panel.id}`
    );
    this.toastService.goodToast('Link copied to clipboard');
  }

  replaceSpacesWithUnderscores(str: string) {
    return str.replace(/\s+/g, '_');
  }


  /**
   * Gets a display string for Triangle Test products
   */
  getTriangleTestProductsDisplay(): string {
    // Check if panel has products array
    if (
      !this.panel?.products ||
      !Array.isArray(this.panel.products) ||
      this.panel.products.length === 0
    ) {
      return '';
    }

    // If there are product objects with names
    if (typeof this.panel.productsDetail[0] === 'object') {
      const productNames = this.panel.productsDetail.map(
        (product: any) => product.name
      );

      if (productNames.length === 0) return '';

      return productNames;
    }

    // If there are just product IDs, return a generic message
    return `${this.panel.products.length} Products Comparison`;
  }

  get backButtonText(): string {
    return this.backButtonData?.backButtonText || 'Back to Panels';
  }

  /**
   * Checks if a response is correct based on the testCase data
   * @param testCase The triangle test case data
   * @returns true if the response is correct, false otherwise
   */
  checkIfResponseIfCorrect(testCase: any): boolean {
    if (!testCase || !testCase.hasOwnProperty('oddSamplePosition')) {
      return false;
    }
    
    // For now we're just checking if oddSamplePosition is 2
    // In a real implementation, we'd check against the actual user response
    const selected = 2; // Replace with actual user response
    return testCase.oddSamplePosition === selected;
  }

  /**
   * Process triangle test aggregate data from the dedicated endpoint
   * @param ttAggregates Triangle test aggregates from API
   */
  processTTAggregates(ttAggregates: TriangleTestAggregate): void {
    if (!ttAggregates || !ttAggregates.productPairs) {
      return;
    }
    
    console.log('Raw triangle test aggregates data:', JSON.stringify(ttAggregates));
    const pairData: Record<string, TriangleTestData> = {};
    
    // Process each product pair from the aggregates
    Object.entries(ttAggregates.productPairs).forEach(([pairKey, pairStats]) => {
      // Create a simplified pair key based on the product IDs
      const productsKey = pairStats.products.join('_');
      
      // Get data SPECIFICALLY for this product pair
      const totalResponses = pairStats.totalResponses;
      const correctResponses = pairStats.correctResponses;
      const incorrectResponses = pairStats.incorrectResponses;
      
      console.log(`Processing pair ${productsKey}:`, pairStats);
      
      // Always calculate chi-squared from scratch for each pair
      // In triangle tests, we expect 1/3 of responses to be correct by chance
      const expectedCorrect = totalResponses / 3;
      const expectedIncorrect = totalResponses * (2/3);
      
      // Calculate chi-squared using formula: Σ((observed - expected)² / expected)
      const correctTerm = Math.pow(correctResponses - expectedCorrect, 2) / expectedCorrect;
      const incorrectTerm = Math.pow(incorrectResponses - expectedIncorrect, 2) / expectedIncorrect;
      const chiSquared = correctTerm + incorrectTerm;
      
      // Debug log to check values for each pair
      console.log(`Pair ${productsKey} calculation:`, {
        totalResponses,
        correctResponses,
        incorrectResponses,
        expectedCorrect,
        expectedIncorrect,
        correctTerm,
        incorrectTerm,
        chiSquared
      });
      
      // Calculate p-value from chi-squared
      const result = this.calculatePValueFromChiSquared(chiSquared, 1);
      const pValue = result.pValue;
      const isSignificant = result.isSignificant;
      
      pairData[productsKey] = {
        results: [], // We don't have individual results in the aggregate data
        expanded: false,
        count: totalResponses,
        correct: correctResponses,
        incorrect: incorrectResponses,
        chiSquared,
        pValue,
        degreesOfFreedom: pairStats.degreesOfFreedom || 1,
        isSignificant
      };
    });
    
    this.summerizedTTData = pairData;
    console.log('Processed triangle test aggregates:', pairData);
  }

  /**
   * Legacy method to process triangle test data
   * @param ttData Triangle test data from API
   */
  processTTData(ttData: any[]): void {
    if (!ttData || !Array.isArray(ttData) || ttData.length === 0) {
      return;
    }
    
    console.log('Processing legacy triangle test data, entry sample:', ttData[0]);
    
    let ids: Record<string, TriangleTestData> = {};

    ttData.forEach(testPair => {
      let isCorrect = this.checkIfResponseIfCorrect(testPair);
      let splitId = testPair.permutationId.split("_");
      let id = splitId[0] + "_" + splitId[1];
      
      if (Object.keys(ids).includes(id)) {
        ids[id].results.push(testPair);
        ids[id].count++;

        if (isCorrect) {
          ids[id].correct++;
        } else {
          ids[id].incorrect++;
        }
      } else {
        ids[id] = {
          results: [testPair],
          expanded: false,
          count: 1,
          correct: isCorrect ? 1 : 0,
          incorrect: isCorrect ? 0 : 1,
          pValue: null,
          chiSquared: null,
          degreesOfFreedom: 1, // For triangle tests, df = 1
          isSignificant: false
        };
      }
    });
    
    console.log('Aggregated test pairs by product:', ids);
    
    // Calculate statistical values for each product pair
    Object.keys(ids).forEach(id => {
      const data = ids[id];
      console.log(`Calculating chi-square for pair ${id}:`, {
        correct: data.correct,
        total: data.count
      });
      
      const { chiSquared, pValue, isSignificant } = this.calculateChiSquare(data.correct, data.count);
      
      console.log(`Results for pair ${id}:`, {
        chiSquared,
        pValue,
        isSignificant
      });
      
      ids[id].chiSquared = chiSquared;
      ids[id].pValue = pValue;
      ids[id].isSignificant = isSignificant;
    });
    
    this.summerizedTTData = ids;
    console.log('Processed triangle test data:', ids);
  }

  /**
   * Calculate Chi-Square statistic and p-value for triangle test data
   * @param correct Number of correct responses
   * @param total Total number of responses
   * @returns Object containing chi-squared value, p-value, and significance
   */
  calculateChiSquare(correct: number, total: number): { chiSquared: number, pValue: number, isSignificant: boolean } {
    // For triangle tests, we expect 1/3 of responses to be correct by chance
    const expectedCorrect = total / 3;
    const incorrect = total - correct;
    const expectedIncorrect = total * (2/3);
    
    // Calculate chi-square using formula: Σ((observed - expected)² / expected)
    const correctTerm = Math.pow(correct - expectedCorrect, 2) / expectedCorrect;
    const incorrectTerm = Math.pow(incorrect - expectedIncorrect, 2) / expectedIncorrect;
    
    const chiSquared = correctTerm + incorrectTerm;
    
    // Debug log to check values
    console.log(`Legacy calculation for correct=${correct}, total=${total}:`, {
      expectedCorrect,
      incorrect,
      expectedIncorrect,
      correctTerm,
      incorrectTerm,
      chiSquared
    });
    
    // Calculate p-value from chi-squared
    const { pValue, isSignificant } = this.calculatePValueFromChiSquared(chiSquared, 1);
    
    return { chiSquared, pValue, isSignificant };
  }
  
  /**
   * Calculate p-value from a chi-squared statistic
   * @param chiSquared The chi-squared statistic
   * @param df Degrees of freedom (usually 1 for triangle tests)
   * @returns Object with p-value and significance flag
   */
  calculatePValueFromChiSquared(chiSquared: number, df: number): { pValue: number, isSignificant: boolean } {
    // Handle NaN or negative chi-squared values
    if (isNaN(chiSquared) || chiSquared < 0) {
      console.error(`Invalid chi-squared value: ${chiSquared}`);
      return { pValue: 0.99, isSignificant: false };
    }
    
    // Debug log the input
    console.log(`Calculating p-value for chi-squared: ${chiSquared}, df: ${df}`);
    
    // For triangle tests with df=1, we use a lookup table with chi-square critical values
    // These values correspond to common probability thresholds for hypothesis testing
    // First value is the chi-squared value, second is the corresponding p-value
    const chiSquareToPValue: [number, number][] = [
      [10.828, 0.001],  // Extremely significant
      [7.879, 0.005],
      [6.635, 0.01],
      [5.024, 0.025],
      [3.841, 0.05],    // Conventional significance threshold
      [2.706, 0.10],
      [1.642, 0.20],
      [1.074, 0.30],
      [0.455, 0.50],
      [0.148, 0.70],
      [0.064, 0.80],
      [0.016, 0.90],
      [0.004, 0.95],
      [0.001, 0.975]    // Very low chi-square value
    ];
    
    // Find the appropriate p-value based on the chi-squared value
    let pValue = 0.001; // Default for very high chi-squared values (highly significant)
    
    // If chi-squared is greater than the largest value in our table
    if (chiSquared >= chiSquareToPValue[0][0]) {
      pValue = 0.001; // Extremely significant
    } 
    // If chi-squared is smaller than the smallest value in our table
    else if (chiSquared < chiSquareToPValue[chiSquareToPValue.length - 1][0]) {
      pValue = 0.99; // Not significant at all
    } 
    else {
      // Look up the p-value in the table (sorted high to low for chi-squared values)
      for (let i = 0; i < chiSquareToPValue.length - 1; i++) {
        if (chiSquared >= chiSquareToPValue[i+1][0] && chiSquared < chiSquareToPValue[i][0]) {
          // Linear interpolation between the two nearest p-values
          const chiSquaredHigh = chiSquareToPValue[i][0];
          const chiSquaredLow = chiSquareToPValue[i+1][0];
          const pValueHigh = chiSquareToPValue[i][1];
          const pValueLow = chiSquareToPValue[i+1][1];
          
          // Calculate where the actual chi-squared falls between the two reference points
          const ratio = (chiSquared - chiSquaredLow) / (chiSquaredHigh - chiSquaredLow);
          // Interpolate the p-value
          pValue = pValueLow + ratio * (pValueHigh - pValueLow);
          
          console.log(`Interpolated p-value from ${pValueLow} to ${pValueHigh} with ratio ${ratio}`);
          break;
        }
      }
    }
    
    // Determine significance (typically p < 0.05 is considered significant)
    const isSignificant = pValue <= 0.05;
    
    // Debug log the output
    console.log(`Calculated p-value: ${pValue}, significant: ${isSignificant}`);
    
    return { pValue, isSignificant };
  }

  getTriangleTestProducts(id: any) {
    let pair = id.split("_");

    let productsNames: any[] = [];
    console.log(pair, this.panel.productsDetail[0]);
    pair.forEach((id: any) => {
      productsNames.push(this.panel.productsDetail?.find((product: any) => product.product_id === id));
    });
    console.log(productsNames, this.panel.productsDetail);
    return productsNames.map(prod => prod.name);
  }

  /**
   * Format p-value for display
   * @param pValue The p-value to format
   * @returns Formatted p-value string
   */
  formatPValue(pValue: number | null): string {
    if (pValue === null) return 'N/A';
    
    // For very small p-values, show as < 0.001
    if (pValue < 0.001) {
      return '< 0.001';
    }
    
    // For very large p-values, show as > 0.99
    if (pValue > 0.99) {
      return '> 0.99';
    }
    
    // Special handling for common threshold values
    if (pValue === 0.05) {
      return '0.050';
    }
    
    if (pValue === 0.01) {
      return '0.010';
    }
    
    // For all other values, show 3 decimal places
    return pValue.toFixed(3);
  }

  /**
   * Determine if a test result is statistically significant
   * @param pValue The p-value to check
   * @returns true if significant (p <= 0.05), false otherwise
   */
  isSignificant(pValue: number | null): boolean {
    if (pValue === null) return false;
    return pValue <= 0.05;
  }

  /**
   * Calculate the percentage of correct responses
   */
  getCorrectPercentage(correct: number, total: number): number {
    if (total === 0) return 0;
    return Math.round((correct / total) * 100);
  }

  /**
   * Calculate the percentage of incorrect responses
   */
  getIncorrectPercentage(correct: number, total: number): number {
    if (total === 0) return 0;
    const incorrect = total - correct;
    return Math.round((incorrect / total) * 100);
  }

  /**
   * Generate the CSS conic gradient for the donut chart based on correct/incorrect percentages
   */
  getDonutGradient(correct: number, total: number): string {
    if (total === 0) return 'conic-gradient(#6788e8 0% 100%)';

    const incorrectPercentage = this.getIncorrectPercentage(correct, total);

    return `conic-gradient(
      #6788e8 0% ${incorrectPercentage}%,
      #ffc55c ${incorrectPercentage}% 100%
    )`;
  }

  goBack(): void {
    if (this.backButtonData?.isFromProject) {
      this.router.navigate([this.backButtonData.backButtonRoute]);
    } else if (this.backButtonData?.isFromGroup) {
      this.router.navigate([this.backButtonData.backButtonRoute]);
    } else {
      this.router.navigate([this.backButtonData?.backButtonRoute || '/panels']);
    }
  }

  @HostListener('document:click', ['$event'])
  onDocumentClick(event: MouseEvent) {
    const popoverElement = document.querySelector('popover');
    if (this.showActionsPopover && popoverElement && !popoverElement.contains(event.target as Node)) {
      this.showActionsPopover = false;
    }
  }
}
