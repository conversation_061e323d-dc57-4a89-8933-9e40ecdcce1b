@import '../../../_colors';

:host{
    // --footer-height: 70px; set in styles.scss
    --footer-border-color: #{$lightBorderStroke};
    --footer-link-size: .75rem;
    --footer-copyright-size: .75rem;
    --footer-link-color: #000;
    --footer-copyright-color: #{$placeholderText};
    --footer-link-hover-color: #{$blue};
    --logo-fill: #000;
    
    width: 100%;
    border-top: 1px solid var(--footer-border-color);
    display: flex;
    align-items: center;
    justify-content: space-between;
    height: var(--footer-height);
    box-sizing: border-box;
    padding: 0 var(--standard-padding);

    .footer-content{
        width: 100%;
        display: flex;
        align-items: center;
        // align-content: space-between;
        // justify-content: space-between;
        // gap: 10px;

        .logo{
            width: auto;
            position: relative;
            top: -3px;
        }

        .footer-links{
            margin-left: 50px;
            font-size: var(--footer-link-size);
            display: flex;
            gap: 30px;

            a{
                color: var(--footer-link-color);
                transition: color 0.2s ease;

                &:hover{
                    color: var(--footer-link-hover-color);
                }
            }
        }
    }

    .copyright{
        width: 100%;
        font-size: var(--footer-copyright-size);
        color: var(--footer-copyright-color);
        text-align: right;
    }
}