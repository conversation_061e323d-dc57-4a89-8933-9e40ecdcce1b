export interface ProjectInterface {
    status: string;
    projectName: string;
    associatedSamples: string[];
    associatedSampleCount: number;
    associatedPanels: string[];
    associatedPanelCount: number;
    associatedGroups: string[];
    associatedGroupCount: number;
    responseCount: number;
    privacy: string;
    owner: string;
    startDate: string | Date;
    endDate: string | Date;
    completionPercentage: string;
    collaborators: string[];
    id: string;
    [key: string]: string | number | boolean | object;
}
