import { Component, Input } from '@angular/core';
import { Ng<PERSON><PERSON> } from '@angular/common';
import { FormControl, ReactiveFormsModule } from '@angular/forms';
@Component({
  selector: 'input-select',
  standalone: true,
  imports: [ReactiveFormsModule, NgFor],
  templateUrl: './input-select.component.html',
  styleUrl: './input-select.component.scss'
})
export class InputSelectComponent {
  @Input() control!: FormControl;
  @Input() id!: string;
  @Input() options!: any[];
  @Input() placeholder: string = 'Select an option';
}
