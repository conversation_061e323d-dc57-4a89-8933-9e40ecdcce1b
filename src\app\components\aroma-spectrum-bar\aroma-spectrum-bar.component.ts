import { Component, inject, Input } from '@angular/core';
import { CommonModule } from '@angular/common';
import { StatisticsService } from '../../services/statistics.service';
import { ConstantsService } from '../../services/constants.service';
import { GraphTooltipComponent, GraphTooltipConfig } from '@lamb-sensory/lamb-component-library';
import { fadeInOut } from '../../modules/animations/animations.module';

@Component({
  selector: 'aroma-spectrum-bar',
  standalone: true,
  imports: [CommonModule, GraphTooltipComponent],
  templateUrl: './aroma-spectrum-bar.component.html',
  styleUrls: ['./aroma-spectrum-bar.component.scss'],
  animations: [fadeInOut]
})
export class AromaSpectrumBarComponent {

  @Input() descriptor!: any;
  @Input() responseDescriptorsObj!: any;
  value: number = 0;
  tooltipConfig: GraphTooltipConfig = {
    mainText: {
      content: '',
    },
    subtext:{
      show: true,
      content: ''
    }
  };

  statisticsService = inject(StatisticsService);
  constantsService = inject(ConstantsService);


  
  ngOnInit(){
    this.checkResponse();
  }

  get count(){
    return this.statisticsService.calculatedStats[this.descriptor.option_id]['count'];
  }

  checkResponse(){
    //console.log(this.responseDescriptorsObj[this.descriptor.option_id])
    if(this.responseDescriptorsObj.hasOwnProperty(this.descriptor.option_id)){
      this.value = Math.round(this.statisticsService.calculatedStats[this.descriptor.option_id]['shrinkageEstimate']);
      this.tooltipConfig.mainText!.content = this.value + '%';
      let descriptorName = this.responseDescriptorsObj[this.descriptor.option_id].descriptor.name;
      this.tooltipConfig.subtext!.content = descriptorName.split(' ').map((word:string) => 
        word.charAt(0).toUpperCase() + word.slice(1)
      ).join(' ');
    }
  }
}
