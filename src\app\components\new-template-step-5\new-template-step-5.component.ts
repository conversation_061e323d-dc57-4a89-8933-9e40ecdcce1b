import { Component, inject } from '@angular/core';
import { CommonModule } from '@angular/common';
import { TemplateFormService } from '../../services/template-form.service';
import { FormControl, ReactiveFormsModule } from '@angular/forms';
import { InputTextComponent } from '../input-text/input-text.component';
import { InputRadioBlockComponent } from '../input-radio-block/input-radio-block.component';
import { ConstantsService } from '../../services/constants.service';
import { fadeShrink } from '../../modules/animations/animations.module';

@Component({
  selector: 'new-template-step-5',
  standalone: true,
  imports: [CommonModule, ReactiveFormsModule, InputTextComponent, InputRadioBlockComponent],
  templateUrl: './new-template-step-5.component.html',
  styleUrl: './new-template-step-5.component.scss',
  animations: [fadeShrink]
})
export class NewTemplateStep5Component {
  templateFormService = inject(TemplateFormService);
  constantsService = inject(ConstantsService);
  form = this.templateFormService.step4Form;
  payControl = this.form.get('pay')! as FormControl;
  customPayControl = this.form.get('customPay')! as FormControl;
  maxSpendControl = this.form.get('maxSpend')! as FormControl;
  templatePayOptions = this.constantsService.panelPayOptions;

  get maxResponses() {
    return Math.floor(this.maxSpendControl.value / (this.payControl.value === 'custom' ? this.customPayControl.value : this.payControl.value));
  }

  ngOnInit() {
    // Make sure the form controls have the correct values
    console.log('Template step 5 form values:', this.form.value);
  }
  
  removeCustomPay() {
    this.customPayControl.setValue(false);
    this.payControl.setValue(5);
  }
}
