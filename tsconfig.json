{
  "compileOnSave": false,
  "compilerOptions": {
    "outDir": "./dist/out-tsc",
    "strict": true,
    "noImplicitOverride": true,
    "noPropertyAccessFromIndexSignature": true,
    "noImplicitReturns": true,
    "noFallthroughCasesInSwitch": true,
    "skipLibCheck": true,
    "isolatedModules": true,
    "esModuleInterop": true,
    "sourceMap": true,
    "declaration": false,
    "experimentalDecorators": true,
    "moduleResolution": "bundler",
    "importHelpers": true,
    "target": "ES2022",
    "module": "ES2022",
    "lib": [
      "ES2022",
      "dom"
    ],
    "paths": {
      "@spartan-ng/ui-calendar-helm": [
        "./libs/ui/ui-calendar-helm/src/index.ts"
      ],
      "@spartan-ng/ui-popover-helm": [
        "./libs/ui/ui-popover-helm/src/index.ts"
      ],
      // "@lamb-sensory/lamb-component-library": [
      //   "../library-workspace/projects/lamb-component-library/src/public-api",
      //   "../library-workspace/dist/lamb-component-library"
      // ]
    }
  },
  "angularCompilerOptions": {
    "enableI18nLegacyMessageIdFormat": false,
    "strictInjectionParameters": true,
    "strictInputAccessModifiers": true,
    "strictTemplates": true
  }
}
