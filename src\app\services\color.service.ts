import { Injectable } from '@angular/core';

@Injectable({
  providedIn: 'root',
})
export class ColorService {

  // colorSteps: any = [{color: '#ff0000', step: 0}, {color: '#d4ff00', step: 0.5}, {color: '#2bff00', step: 1}];
  colorSteps: any = [{color: '#9C9C9C', step: 0}, {color: '#C7C7C7', step: 0.5}, {color: '#6B6B6B', step: 1}];

  h2r(hex: any) {
    let result = /^#?([a-f\d]{2})([a-f\d]{2})([a-f\d]{2})$/i.exec(hex);
    return result ? [
        parseInt(result[1], 16),
        parseInt(result[2], 16),
        parseInt(result[3], 16)
    ] : null;
  }

  r2h(rgb: any) {
    return "#" + ((1 << 24) + (rgb[0] << 16) + (rgb[1] << 8) + rgb[2]).toString(16).slice(1);
  }

  _interpolateColor(color1: any, color2: any, factor: any = 0.5) {
    let result = color1.slice();
    for (let i=0;i<3;i++) {
      result[i] = Math.round(result[i] + factor*(color2[i]-color1[i]));
    }
    return result;
  }
}
