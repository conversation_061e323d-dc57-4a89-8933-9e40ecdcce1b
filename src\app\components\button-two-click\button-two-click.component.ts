import { Component, Input, Output, EventEmitter } from '@angular/core';
import { NgIf } from '@angular/common';
import { buttonTextSlide } from '../../modules/animations/animations.module';

@Component({
  selector: 'button-two-click',
  standalone: true,
  imports: [NgIf],
  templateUrl: './button-two-click.component.html',
  styleUrl: './button-two-click.component.scss',
  animations: [buttonTextSlide]
})
export class ButtonTwoClickComponent {

  @Input() type: 'close' = 'close';

  buttonPhase: number = 0;

  @Output() onConfirm = new EventEmitter();

  resetTimer: any;
  resetTime: number = 500;
  get initialText() {
    return this.type === 'close' ? 'Close' : 'Save';
  }

  get confirmText() {
    return this.type === 'close' ? 'Click again to discard and close' : 'Click again to save';
  }

  changePhase(){
    this.buttonPhase = this.buttonPhase === 0 ? 1 : 0;
  }

  buttonClick(){
    if(this.buttonPhase === 0){
      this.changePhase();
    } else {
      this.onConfirm.emit();
    }
  }

  onMouseLeave() {
    if (this.buttonPhase === 1) {
      this.resetTimer = setTimeout(() => {
        this.buttonPhase = 0;
      }, this.resetTime);
    }
  }

  onMouseEnter() {
    if (this.resetTimer) {
      clearTimeout(this.resetTimer);
    }
  }
}
