import { Component, inject, TemplateRef, ViewChild } from '@angular/core';

import { LambTableComponent, HeaderCell } from '@lamb-sensory/lamb-component-library';
import { PageHeaderComponent } from "../page-header/page-header.component";
import { PopupService } from '../../services/popup.service';
import { PanelsService } from '../../services/panels.service';
import { Router } from '@angular/router';
import { TABLE_STYLES } from '../../config/table-styles.config';
import { TemplatesService } from '../../services/templates.service';
import { PaginationComponent } from '../pagination/pagination.component';
import { PaginationParams } from '../../interfaces/paginationParams';
import { map, Observable, share, tap } from 'rxjs';
import { LoadingService } from '../../services/loading.service';
import { AsyncPipe, CommonModule } from '@angular/common';
import { ButtonStandardComponent } from "../button-standard/button-standard.component";
import { PopoverComponent } from '../popover/popover.component';
import { fadeInOut} from '../../modules/animations/animations.module';

@Component({
  selector: 'templates',
  standalone: true,
  imports: [PopoverComponent, LambTableComponent, PageHeaderComponent, PaginationComponent, AsyncPipe, CommonModule, ButtonStandardComponent],
  templateUrl: './templates.component.html',
  styleUrl: './templates.component.scss',
  animations: [fadeInOut],
})
export class TemplatesComponent {
  popupService = inject(PopupService);
  panelsService = inject(PanelsService);
  router = inject(Router);
  templatesService = inject(TemplatesService);
  
  loadingService = inject(LoadingService);

  tableStyles = TABLE_STYLES;
  templatesSource$!: Observable<any>;
  templates$!: Observable<any>;

  showActionsPopover = false;

  data:any;

  paginationState$!: Observable<any>;

  popoverPosition = { top: 0, left: 0 };

  @ViewChild('templateOptions', {static: true}) templateOptions!: TemplateRef<any>;

  // insertCustomOptionsCell(template: any){
  //   template['options__template'] = this.templateOptions;
  // }

  // insertCustomCells(templates: any[]){
  //   console.log(templates);
  //   return templates.forEach((template: any) => {
  //     this.insertCustomOptionsCell(template);
  //   })
  // }

  tableHeaders: HeaderCell[] = [

    {
      value: 'Template Name',
      key: 'name'
    },
    {
      value: 'Type',
      key: 'type'
    },
    {
      value: 'Consumption Method',
      key: 'conMethod'
    },
    {
      value: 'Created On',
      key: 'createdOn'
    },{
      value: 'Used By',
      key: 'usedBy'
    },{
      value: '',
      key: 'options',
      style: {textAlign: 'center', width: '8ch'}
    }
  ];


  rawTemplates: any[] = [];
  currentTemplateId: string = '';

  getTableData(data:any) {
    this.rawTemplates = data;
    const tableData: any[] = data.map((template:any, index: number) => {
      return {
        id: template.id,
        index: index,
        name: template.name,
        type: template.experiment_type,
        options__template: this.templateOptions,
        options__templateContext: {$implicit: template},
        conMethod: template.consumption_option,
        createdOn: this.getDateFromSeconds(template.created._seconds),
        usedBy: template.usedBy && `${template.usedBy.length} panels`,
      }
    });

    // const templateData = this.insertCustomCells(tableData)
    console.log(tableData);
    return tableData ;
  }

  onOptionsClick(event: MouseEvent, context: any) {
    // Stop the event from bubbling up to the row
    console.log(context);
    event.stopPropagation();
    
    // Get the template ID from the context

    const templateId = context.id;
      // Show a popover or perform other actions
      this.showOptionsPopover(event, templateId);


      const buttonElement = event.currentTarget as HTMLElement;
      const rect = buttonElement.getBoundingClientRect();
      
      // Position the popover below the button
      this.popoverPosition = {
        top: rect.bottom + window.scrollY,
        left: rect.left + window.scrollX
      };

      console.log(this.popoverPosition);
    
  }
  
  showOptionsPopover(event: MouseEvent, templateId: string) {
    // Position and show a popover with options
    // You can use your existing PopoverComponent here
    this.showActionsPopover = !this.showActionsPopover;
    console.log(templateId);
    
    // You might need to store the current template ID for reference
    this.currentTemplateId = templateId;
    
    // Prevent the popover from closing immediately
    event.preventDefault();
  }


  getDateFromSeconds(seconds: number | string){
    const date = new Date(seconds as number * 1000);
    if (date.getFullYear() > 4000) {
        return "Ongoing";
    }
    return date.toLocaleDateString();
}

  onRowClick(event: any){
    console.log(event);
    this.router.navigate(['/templates', event.cell.data.id]);
  }

  ngOnInit(){

    this.loadingService.setLoading('templates', true);
    
    // Create a shared source observable
    this.templatesSource$ = this.templatesService.templates$.pipe(
      tap(() => this.loadingService.setLoading('templates', false)),
      share()
    );
    
    // Derive templates$ from the shared source
    this.templates$ = this.templatesSource$.pipe(
      tap((response) => {
        this.data = this.getTableData(response['templates']);
      })
    );
    
    this.paginationState$= this.templatesService.templates$.pipe(
      map((response:any) => ({
        currentPage: response.currentPage,
        totalPages: response.totalPages,
        hasMore: response.hasMore
      }))
    );

    this.loadTemplates();
  }

  loadTemplates() {
    this.templatesService.templates$.subscribe((response:any) => {
      console.log(response);
      this.data = this.getTableData(response['templates']);

    });
  }

  // Pagination methods
  onPageChange(page: number) {
    this.loadTemplates();
    this.templatesService.setPage(page);
  }

  editTemplate(templateId: string) {
    const template = this.rawTemplates.find(t => t.id === templateId);
    if (template) {
      this.popupService.editTemplate(template, 5);
      this.showActionsPopover = false;
    }
  }
  
  duplicateTemplate(templateId: string) {
    const template = this.rawTemplates.find(t => t.id === templateId);
    if (template) {
      let {id:_, template_id:__, created:___, ...templateData} = template;
      this.templatesService.createPanelTemplate({...templateData, name: `${template.name} (copy)`})
        .subscribe((response: any) => {
          if (response.message === 'Panel template created successfully') {
            this.loadTemplates();
          }
        });
      this.showActionsPopover = false;
    }
  }
  
  useTemplateToCreatePanel(templateId: string) {
    const template = this.rawTemplates.find(t => t.id === templateId);
    if (template) {
      this.popupService.openNewPanel('Use a Template', template);
      this.showActionsPopover = false;
    }
  }
  
  deleteTemplate(templateId: string) {
    // Implement delete functionality
    this.showActionsPopover = false;
  }

}
