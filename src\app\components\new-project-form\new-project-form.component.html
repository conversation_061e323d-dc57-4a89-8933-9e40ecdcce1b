<button-two-click [type]="'close'" (onConfirm)="popupService.closeNewProject()" class="light-bg">
    <svg width="14" height="14" viewBox="0 0 14 14" fill="none" xmlns="http://www.w3.org/2000/svg">
        <path d="M7 5.58549L12.2929 0.292606C12.683 -0.0975353 13.3164 -0.0975353 13.7074 0.292606C14.0975 0.683605 14.0975 1.31695 13.7074 1.70711L8.41451 7L13.7074 12.2929C14.0975 12.683 14.0975 13.3164 13.7074 13.7074C13.3164 14.0975 12.683 14.0975 12.2929 13.7074L7 8.41451L1.70711 13.7074C1.31697 14.0975 0.683627 14.0975 0.292606 13.7074C-0.0975353 13.3164 -0.0975353 12.683 0.292606 12.2929L5.58549 7L0.292606 1.70711C-0.0975353 1.31697 -0.0975353 0.683627 0.292606 0.292606C0.683605 -0.0975353 1.31695 -0.0975353 1.70711 0.292606L7 5.58549Z" fill="black"/>
    </svg>
</button-two-click>

<div class="form-header">Create a Project</div>

<form [formGroup]="projectsService.form">
    <div class="input-container name" [class.error-shake]="projectsService.formErrors['projectName']">
        <label for="project-name">Project Name</label>
        <input-text [control]="projectNameControl" [id]="'project-name'" [placeholder]="'project name'"></input-text>
    </div>

    <div class="input-container name" [class.error-shake]="projectsService.formErrors['description']">
        <label for="project-description">Description</label>
        <input-text [control]="descriptionControl" [id]="'project-description'" [placeholder]="'project description'"></input-text>
    </div>

    <div class="input-container radio-container">
        <label>Start</label>
        <input-radio-block [control]="startOptionControl" [id]="'start-option-control-now'" [value]="'now'" [name]="'start-option-control'" [class.active]="startOptionControl.value === 'now'">Immediately</input-radio-block>
        <input-radio-block [control]="startOptionControl" [id]="'start-option-control-custom'" [value]="'custom'" [name]="'start-option-control'" [class.active]="startOptionControl.value === 'custom'">Custom Start Date</input-radio-block>
    </div>

    <div class="input-container date-picker-container" *ngIf="startOptionControl.value === 'custom'" [class.error-shake]="projectsService.formErrors['startDate']">
        <svg xmlns="http://www.w3.org/2000/svg" width="18" height="34" viewBox="0 0 18 34" fill="none">
            <path d="M17.3536 30.3536C17.5488 30.1583 17.5488 29.8417 17.3536 29.6464L14.1716 26.4645C13.9763 26.2692 13.6597 26.2692 13.4645 26.4645C13.2692 26.6597 13.2692 26.9763 13.4645 27.1716L16.2929 30L13.4645 32.8284C13.2692 33.0237 13.2692 33.3403 13.4645 33.5355C13.6597 33.7308 13.9763 33.7308 14.1716 33.5355L17.3536 30.3536ZM0.5 0V20H1.5V0H0.5ZM11 30.5H17V29.5H11V30.5ZM0.5 20C0.5 25.799 5.20101 30.5 11 30.5V29.5C5.75329 29.5 1.5 25.2467 1.5 20H0.5Z" fill="#9D9D9D"/>
        </svg>
        <input-bs-datepicker [control]="startDateControl"></input-bs-datepicker>
    </div>

    <div class="input-container radio-container">
        <label>End</label>
        <input-radio-block [control]="endOptionControl" [id]="'end-option-control-never'" [value]="'never'" [name]="'end-option-control'" [class.active]="endOptionControl.value === 'never'">No End Date</input-radio-block>
        <input-radio-block [control]="endOptionControl" [id]="'end-option-control-custom'" [value]="'custom'" [name]="'end-option-control'" [class.active]="endOptionControl.value === 'custom'">Custom End Date</input-radio-block>
    </div>

    <div class="input-container date-picker-container" *ngIf="endOptionControl.value === 'custom'" [class.error-shake]="projectsService.formErrors['endDate']">
        <svg xmlns="http://www.w3.org/2000/svg" width="18" height="34" viewBox="0 0 18 34" fill="none">
            <path d="M17.3536 30.3536C17.5488 30.1583 17.5488 29.8417 17.3536 29.6464L14.1716 26.4645C13.9763 26.2692 13.6597 26.2692 13.4645 26.4645C13.2692 26.6597 13.2692 26.9763 13.4645 27.1716L16.2929 30L13.4645 32.8284C13.2692 33.0237 13.2692 33.3403 13.4645 33.5355C13.6597 33.7308 13.9763 33.7308 14.1716 33.5355L17.3536 30.3536ZM0.5 0V20H1.5V0H0.5ZM11 30.5H17V29.5H11V30.5ZM0.5 20C0.5 25.799 5.20101 30.5 11 30.5V29.5C5.75329 29.5 1.5 25.2467 1.5 20H0.5Z" fill="#9D9D9D"/>
        </svg>
        <input-bs-datepicker [control]="endDateControl"></input-bs-datepicker>
    </div>

    <div class="input-container collaborators">
        <label>Collaborators</label>
        <div class="collaborators-container">
            <div class="no-collaborators" *ngIf="!selectedUsersArray.length">None</div>

            <lamb-gp-icon 
                (mouseenter)="showTooltip($event, user, 'user')"
                (mouseleave)="hideTooltip()" 
                *ngFor="let user of selectedUsersArray" 
                [userInitials]="user.name.charAt(0)" 
                [textColorOption]="'computedFromBackground'">
            </lamb-gp-icon>
            
            <!-- <div class="button-container"> -->
                <button type="button" class="add-collaborator-btn" (click)="openCollaboratorsModal()" #modalButton><svg xmlns="http://www.w3.org/2000/svg" width="10" height="10" viewBox="0 0 10 10" fill="none">
                    <path d="M4.11765 0V4.11765H0V5.88235H4.11765V10H5.88235V5.88235H10V4.11765H5.88235V0H4.11765Z" fill="#292727"/>
                </svg></button>

                <div class="collaborators-modal" [style.top]="modalOffset.y + 22 + 'px'" [style.left]="modalOffset.x - 25 + 'px'" *ngIf="collaboratorsModalOpen" [@fadeScale] #collaboratorsModal>
                    <lamb-table *ngIf="!loadingUsers" [headers]="usersHeaders" [data]="users" [styles]="userTableStyles"></lamb-table>
                    <div *ngIf="loadingUsers" class="loading-spinner">
                        <svg class="spinner" viewBox="0 0 50 50">
                            <circle class="path" cx="25" cy="25" r="20" fill="none" stroke-width="5"></circle>
                        </svg>
                    </div>
                </div>
            <!-- </div> -->

        </div>
    </div>

    <div class="input-container radio-container">
        <label>Privacy</label>
        <input-radio-block [control]="privacyControl" [id]="'privacy-control-private'" [value]="'private'" [name]="'privacy-control'" [class.active]="privacyControl.value === 'private'">Private</input-radio-block>
        <input-radio-block [control]="privacyControl" [id]="'privacy-control-public'" [value]="'public'" [name]="'privacy-control'" [class.active]="privacyControl.value === 'public'">Public</input-radio-block>
    </div>

    <div class="input-container table-input-container">
        <label class="associated-panels">
            <span>Included Panels</span>
            <div class="arrow-container" *ngIf="selectedPanelsArray.length || selectedGroupsArray.length">
                <svg xmlns="http://www.w3.org/2000/svg" width="14" height="20" viewBox="0 0 14 20" fill="none">
                    <path d="M13.3536 16.3536C13.5488 16.1583 13.5488 15.8417 13.3536 15.6464L10.1716 12.4645C9.97631 12.2692 9.65973 12.2692 9.46447 12.4645C9.2692 12.6597 9.2692 12.9763 9.46447 13.1716L12.2929 16L9.46447 18.8284C9.2692 19.0237 9.2692 19.3403 9.46447 19.5355C9.65973 19.7308 9.97631 19.7308 10.1716 19.5355L13.3536 16.3536ZM0 0.5V10H1V0.5H0ZM6.5 16.5H13V15.5H6.5V16.5ZM0 10C0 13.5899 2.91015 16.5 6.5 16.5V15.5C3.46243 15.5 1 13.0376 1 10H0Z" fill="#C6C6C6"/>
                </svg>
            </div>
            <div class="selected-panels-list" *ngIf="selectedPanelsArray.length || selectedGroupsArray.length">
                <div *ngFor="let panel of selectedPanelsArray" class="selected-item panel">{{panel.panelName}}</div>
                <div *ngFor="let group of selectedGroupsArray" class="selected-item group">{{group.groupName}}</div>
            </div>
        </label>

        <div class="table-outer-container">
            <div class="table-select-container" [class]="tableSelectControl.value === 'panels' ? 'panels' : 'groups'">
                <input-radio-block [control]="tableSelectControl" [id]="'table-select-control-panels'" [value]="'panels'" [name]="'table-select-control'" [class.active]="tableSelectControl.value === 'panels'">Panels</input-radio-block>
                <input-radio-block [control]="tableSelectControl" [id]="'table-select-control-groups'" [value]="'groups'" [name]="'table-select-control'" [class.active]="tableSelectControl.value === 'groups'">Groups</input-radio-block>
            </div>

            <div class="table-name-and-search-container">
                <div class="table-name">All {{tableSelectControl.value === 'panels' ? 'Panels' : 'Groups'}}</div>
                <div class="search-container"></div>
                <div class="filter-container"></div>
            </div>
            
            <div class="table-inner-container">
                <lamb-table [headers]="tableHeaders" [data]="tableData" [styles]="tableStyles"></lamb-table>
                <button
                    *ngIf="tableSelectControl.value === 'panels' && state.hasMore && !isLoadingMore"
                    (click)="loadMorePanels()"
                    class="load-more-btn">
                        Load More Panels
                </button>
                <div *ngIf="isLoadingMore" class="loading-spinner">
                    <svg class="spinner" viewBox="0 0 50 50">
                        <circle class="path" cx="25" cy="25" r="20" fill="none" stroke-width="5"></circle>
                    </svg>
                </div>
            </div>
        </div>
    </div>
</form>

<form-footer [service]="projectsService"></form-footer>

<ng-template #selectCellTemplate let-item>
    <div class="select-cell">
        <!-- <input-radio-standard [control]="item.selectControl" [id]="item.selectId" [value]="item.selectValue" [name]="item.selectName"></input-radio-standard> -->
        <input type="checkbox" (change)="toggleSelection(item)" [checked]="tableSelectControl.value === 'panels' ? selectedPanelsArray.includes(item) : selectedGroupsArray.includes(item)">
    </div>
</ng-template>

<ng-template #productImageCellTemplate let-item>
    <div class="product-image-cell">
        <lamb-gp-icon 
            (mouseenter)="showTooltip($event, item, 'sample')"
            (mouseleave)="hideTooltip()" 
            [styles]="{'width': '20px', 'height': '20px', 'borderRadius': '5px'}" 
            [imageUrl]="item.productImage" 
            [userInitials]="item.productName?.charAt(0) || null">
        </lamb-gp-icon>
    </div>
</ng-template>

<ng-template #selectUserCellTemplate let-user>
    <div class="select-cell">
        <input type="checkbox" (change)="toggleUserSelection(user)" [checked]="selectedUsersArray.includes(user)">
    </div>
</ng-template>

<lamb-graph-tooltip 
    *ngIf="activeTooltipItem"
    [style.position]="'absolute'"
    [style.left.px]="tooltipPosition.x"
    [style.top.px]="tooltipPosition.y"
    [config]="{mainText: {content: activeTooltipText}}"
    [@fadeInOut]
    #tooltip>
</lamb-graph-tooltip>