// @import "../../../styles.scss";
@import "../../../_colors.scss";

:host {
    position: fixed;
    top: 0;
    left: 0;
    width: 100vw;
    height: 100vh;
    overflow-y: auto;
    background: $blackToDarkPurple;
    box-sizing: border-box;
    padding-bottom: 50px;
    display: flex;
    align-items: center;
    justify-content: center;

    .login-wrap{
        display: flex;
        background: url('../../../assets/images/banner-bg\ 2.png'), $darkPurpleToBlack;
        background-size: contain;
        background-repeat: no-repeat;
        background-position: top;
        width: calc(100vw - 40px);
        margin: 0 auto;
        border-radius: 40px;
        height: auto;
        padding-bottom: 30px;
        justify-content: flex-start;
        align-items: center;
        flex-flow: column nowrap;
        position: relative;
        overflow: hidden;

        
        .logo{
            margin-top: 40px;
            margin-bottom: 20px;
            position: relative;
        }

        .welcome{
            font-size: 1.9rem;
            margin-bottom: 10px;
            font-weight: 700;
            color: #fff;
        }

        .sub-welcome{
            font-size: 1rem;
            margin-bottom: 20px;
            color: #fff;
        }

        .sso-login-container{
            display: grid;
            // grid-template-columns: 1fr 1fr;
            grid-template-columns: 1fr;
            gap: 20px;
            margin-bottom: 20px;
            justify-items: center;
            width: 100%;
            padding: 0px 40px;
            box-sizing: border-box;

            gray-button{
                width: 100%;
            }

            .or-container{
                width: 100%;
                display: grid;
                grid-template-columns: 1fr auto 1fr;
                gap: 10px;
                // margin-bottom: 20px;
                align-items: center;
                justify-content: center;

                .or-line{
                    border-top: 1px solid $darkGray;
                    position: relative;
                    top: 1px;
                }

                .or-text{
                    color: #fff;
                    font-size: .9rem;
                    font-weight: 400;
                }
            }
        }

        form{
            display: grid;
            grid-template-columns: 1fr;
            margin-bottom: 20px;
            justify-items: center;
            width: 100%;
            padding: 10px 40px;
            box-sizing: border-box;

            .form-group{
                margin-bottom: 15px;
                width: 100%;
                position: relative;

                &.error{

                    label{
                        color: $red;
                        animation: errorShake .7s ease 0s 1 normal forwards;	
                    }

                    input{
                        outline: 2px solid $red;
                        animation: errorShake .7s ease 0s 1 normal forwards;	
                    }
                }

                label{
                    display: block;
                    margin-bottom: 5px;
                    font-size: .8rem;
                    font-weight: 500;
                    color: #fff;
                }
                
                input{
                    width: 100%;
                }

                $showPasswordSize: 20px;

                .showPassword{
                    width: $showPasswordSize;
                    height: $showPasswordSize;
                    position: absolute;
                    right: 13px;
                    bottom: calc(50% - $showPasswordSize);                  
                    cursor: pointer;

                    svg{
                        width: 100%;
                        height: 100%;
                        position: absolute;

                        line{
                            transform: translate(-18px, -19px);
                        }
                    }
                }
            }

            .reset{
                font-size: .8rem;
                font-weight: 500;
                cursor: pointer;
                width: 100%;
                text-align: right;
                position: relative;
                top: -5px;
                color: #fff;
            }

            .bottom{
                width: 100%;
                margin-top: 15px;

                gray-button{
                    width: 100%;
                }
            }
        }

        .signup-link{
            width: 320px;
            margin: 0 auto;
            border-top: 1px solid $loginBorderStroke;
            padding-top: 20px;
            font-weight: 700;
            font-size: .9rem;
            color: #fff;
            margin-top: 20px;
        
            a{
                text-decoration: underline;
                cursor: pointer;
                font-weight: 500;
                color: #fff;
                // color: $appText;

                &:visited{
                    color: inherit;
                }
            }
        }

        p.error{
            color: #fff;
            font-size: 12px;
            margin-bottom: 20px;
            background-color: $red;
            padding: 10px;
            border-radius: 5px;
            animation: errorShake .7s ease 0s 1 normal forwards;	
            margin-top: 0;
            text-align: center;
            font-weight: 700;
        }
    }

    @media (min-width: 426px) {
        .login-wrap{
            width: 400px;
        }
    }

    .loading {
        width: 100%;
        position: fixed;
        top: 0;
        left: 0;
        height: 100%;
        display: flex;
        background-color: rgba(0, 0, 0, .7);
        backdrop-filter: blur(3px);
    }
}

@keyframes errorShake {
	0%,
	100% {
		transform: translateX(0);
	}

	10%,
	30%,
	50%,
	70% {
		transform: translateX(-5px);
	}

	20%,
	40%,
	60% {
		transform: translateX(5px);
	}

	80% {
		transform: translateX(3px);
	}

	90% {
		transform: translateX(-3px);
	}
}