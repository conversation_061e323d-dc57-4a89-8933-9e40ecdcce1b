import { Component, Input } from '@angular/core';
import { FormControl } from '@angular/forms';
@Component({
  selector: 'input-file',
  standalone: true,
  imports: [],
  templateUrl: './input-file.component.html',
  styleUrl: './input-file.component.scss'
})
export class InputFileComponent {

  @Input() control: FormControl = new FormControl();
  @Input() id: string = '';
  @Input() placeholder: string = '';
}
