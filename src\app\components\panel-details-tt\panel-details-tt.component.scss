@import "../../../_colors";

:host {
  .tt-response-details-header {
    display: flex;
    flex-direction: row;
    align-items: center;
    gap: 10px;
    margin: 40px 0 20px 0;

    .header-label {
      font-size: 20px;
      font-weight: 600;
      margin-left: 10px;
    }

    .border-line {
      flex: 1;
      height: 1px;
      background-color: #ddd;
      margin-left: 20px;
    }
  }

  .tt-details-header {
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    align-items: center;
    padding: 16px 20px;
    border-bottom: 1px solid #e0e0e0;
    background-color: #f8f9fa;

    .tt-product-names {
      display: flex;
      flex-direction: row;
      align-items: center;
      gap: 10px;
      flex: 1;

      .tt-divider-block {
        margin-right: 5px;
      }
    }

    .p-value {
      display: flex;
      align-items: center;
      gap: 8px;
      font-weight: 500;
      padding: 5px 10px;
      border-radius: 6px;
      background-color: #e4ebfe;
      color: #11295f;
      font-size: 13px;
      min-width: 130px;

      &.significant {
        background-color: rgba(255, 197, 92, 0.2);
        color: #905e00;

        svg path {
          fill: #905e00;
        }
      }

      .significance-badge {
        background-color: #ffc55c;
        color: #905e00;
        font-size: 12px;
        padding: 2px 8px;
        border-radius: 12px;
        font-weight: 600;
        margin-left: 5px;
      }
    }

    .expand-icon {
      cursor: pointer;
      transition: transform 0.3s ease;
      margin-left: 15px;

      &:hover {
        transform: scale(1.2);
      }
    }
  }

  .tt-divider-block {
    background-color: #d463e0;
    display: flex;
    justify-content: center;
    align-items: center;
    width: 20px;
    height: 20px;
    border-radius: 5px;
    // margin-right: 3px;

    .name-initial {
      font-size: 12px;
      font-weight: 600;
      color: #5d1765;
    }
  }

  .tt-product-names {
    display: flex;
    flex-direction: row;
    align-items: center;
    gap: 10px;
    flex-wrap: wrap;
  }

  .tt-response-details-section {
    border-radius: 12px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
    margin-bottom: 20px;
    overflow: hidden;
    background-color: white;
  }

  .tt-stats {
    display: flex;
    flex-direction: column;
    gap: 8px;
    margin-top: 15px;
    padding: 12px 15px;
    border-radius: 8px;
    background-color: rgba(240, 240, 240, 0.5);

    .tt-stat-item {
      display: flex;
      justify-content: space-between;
      padding: 3px 0;

      .stat-label {
        font-weight: 500;
        color: #555;
      }

      .stat-value {
        font-weight: 600;
      }
    }
  }

  .tt-data-details {
    padding: 0 20px 20px;
  }

  .tt-data-description {
    padding: 20px;
    background-color: #f8f9fa;
    border-radius: 12px;
    margin-top: 20px;

    & > div {
      display: flex;
      align-items: center;
      gap: 12px;

      p {
        font-size: 14px;
        margin: 0;
        line-height: 1.5;
      }
    }
  }

  .tt-data-breakdown {
    margin-top: 20px;

    .breakdown-container {
      display: flex;
      align-items: center;
      gap: 40px;
      background-color: #f8f9fa;
      border-radius: 12px;
      padding: 30px;

      .response-breakdown-table {
        flex: 1;
        border-collapse: collapse;
    
        th,
        td {
          padding: 12px 20px;
          text-align: left;
          border-bottom: 1px solid #e0e0e0;
        }
    
        th {
          font-weight: 600;
          color: #555;
        }
    
        .stat-value {
          font-size: 18px;
          font-weight: 600;
          color: #333;
        }
      }
    }

    .donut-chart-container {
      display: flex;
      flex-direction: column;
      align-items: flex-start;
      gap: 15px;
      min-width: 180px;

      .donut-chart {
        position: relative;
        width: 140px;
        height: 140px;
        border-radius: 50%;
        // Dynamic background will be set via [style.background]

        &::before {
          content: "";
          position: absolute;
          top: 50%;
          left: 50%;
          transform: translate(-50%, -50%);
          width: 90px;
          height: 90px;
          background-color: white;
          border-radius: 50%;
        }
      }

      .donut-legend {
        display: flex;
        flex-direction: column;
        gap: 8px;
        margin-top: 5px;
        width: 100%;

        .legend-item {
          display: flex;
          align-items: center;
          gap: 8px;

          .color-dot {
            width: 20px;
            height: 20px;
            border-radius: 5px;

            &.incorrect {
              background-color: #6788e8; // Blue color from screenshot
            }

            &.correct {
              background-color: #ffc55c; // Yellow/gold color from screenshot
            }
          }

          .legend-text {
            font-size: 14px;
            color: #333;
            white-space: nowrap;
          }

          .legend-percentage {
            font-size: 14px;
            font-weight: 600;
            color: #333;
            margin-left: 20px;
          }
        }
      }
    }
}
}