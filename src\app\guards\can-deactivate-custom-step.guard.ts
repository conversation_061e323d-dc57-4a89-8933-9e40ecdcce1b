import { Injectable, inject } from '@angular/core';
import { CanDeactivate } from '@angular/router';
import { PopupService } from '../services/popup.service';

@Injectable({
  providedIn: 'root'
})
export class CanDeactivateCustomStepGuard implements CanDeactivate<unknown> {
  private popupService = inject(PopupService);

  canDeactivate(): boolean {
    if (this.popupService.popups['customStep']) {
      return confirm( 'You will lose all unsaved changes in the custom step form. Are you sure you want to leave?');
    }
    return true;
  }
} 