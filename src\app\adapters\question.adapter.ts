import { PanelStep } from "../interfaces/panel-step.interface";
import { PanelStepOption } from "../interfaces/panel-step-option.interface";

export class QuestionAdapter {

    async customToPanelStep(question: any): Promise<PanelStep> {
        console.log('question', question);
        
        
        return {
            id: question.id || question.question_id,
            name: question.name,
            type: question.stepType,
            instructions: question.stepInstructions || "",
            options: question.options || question.optionIds || [],
            required: question.required || false,
            isDefault: false
        };
    }

    async defaultToPanelStep(question: any): Promise<PanelStep> {
        // console.log('question', question);
        
        return {
            id: question.id,
            name: question.name,
            type: question.type,
            instructions: question.instructions || "",
            options: question.options || [],
            required: true,
            isDefault: true
        };
    }

    async adaptMany(questions: any[], callback: (question: any) => Promise<PanelStep>): Promise<PanelStep[]> {
        return await Promise.all(questions.map(callback));
    }

    // async adapt(question: any): Promise<PanelStep> {
    //     return {
    //         id: question.id,
    //         name: question.name,
    //         type: question.type || question.stepType,
    //         options: question.options || question.optionIds,
    //         instructions: question.description || question.stepInstructions
    //     }
    // }

    // async adaptMany(questions: any[]): Promise<PanelStep[]> {
    //     return await Promise.all(questions.map(this.adapt));
    // }

    async adaptOption(option: any): Promise<PanelStepOption> {
        // console.log('adaptOption', option);
        
        return {
            id: option.id,
            label: option.name,
            instructions: option.instructions,
            priority: option.priority,
            sliderLeft: option.labels[0],
            sliderCenter: option.labels.length > 2 ? option.labels[1] : '',
            sliderRight: option.labels.at(-1)
        }
    }
}
