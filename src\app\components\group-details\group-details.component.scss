@import "../../../_colors";

:host {
  // host styles in styles.scss .full-screen-form
  position: fixed;
  z-index: 999998;
  width: 100%;
  height: 100%;
  top: 0;
  left: 0;
  background-color: #fff;
  display: block;
  padding: 15px;
  box-sizing: border-box;
  overflow-y: auto;

  @mixin border() {
    border: 1px solid $mediumLightGray;
    border-radius: 18px;
  }

  a {
    text-decoration: none;
    color: unset;
  }

  button-standard {
    span {
      margin-left: 10px;
    }

    &.back-button {
      position: absolute;
      top: 15px;
      left: 15px;
    }
  }

  & > div {
    --group-details-width: calc(100% - 30px);
    width: var(--group-details-width);
    --group-details-max-width: 932px;
    max-width: var(--group-details-max-width);
    margin: 0 auto;
  }

  .table-container {
    background-color: #fff;
    border-radius: 10px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
    padding: 25px;
    margin-bottom: 30px;

    .table-label {
      display: grid;
      grid-template-columns:auto 1fr;
      gap: 0;
      align-items: center;
      justify-content: flex-start;

      &.panels {

        .table-panel-text{
          background-color: #0084FF;
        }

        .decorator-line {
          background-color: #CBE5FD;
        }
      }

      &.samples {
        .table-panel-text{
          background-color: #9D3CFE;
        }

        .decorator-line {
          background-color: #F5D9FF;
        }
      }

      &.panelists {
        .table-panel-text{
          background-color: #D0397A;
        }

        .decorator-line {
          background-color: #FCD5E6;
        }
      }

      .table-panel-text {
        font-size: 0.875rem;
        font-weight: 600;
        color: #fff;
        background-color: dimgray;        
        padding: 8px 12px;
        border-radius: 12px 12px 0px 12px;
      }

      .decorator-line {
        height: 1px;
        width: 100%;
        background-color: dimgray;
      }
    }

    ::ng-deep {

      // View Results button styling
      // .view-results-button {
      //   color: #00519C;
      //   font-weight: 600;
      //   cursor: pointer;
      //   display: flex;
      //   align-items: center;
      //   gap: 5px;
      // }

      // User initials styling
      .user-initials {
        display: flex;
        justify-content: center;
        align-items: center;
        width: 32px;
        height: 32px;
        border-radius: 50%;
        color: #FFFFFF;
        font-weight: 600;
      }
      
      // Table row hover effect
      .row:hover {
        background-color: #f0f9ff !important;
        cursor: pointer;
      }
    }
  }

  .group-name-row {
    margin-top: 70px;
    margin-bottom: 30px;
    display: grid;
    grid-template-columns: 2fr 1fr;
    align-items: flex-start;

    .group-name-grid-area {
      .group-name {
        --group-name-font-size: 1.625rem;
        font-size: var(--group-name-font-size);
        font-weight: 700;
      }

      .group-product-container {
        display: flex;
        align-items: center;
        justify-content: flex-start;
        flex-direction: row;
        flex-wrap: nowrap;
        --group-product-container-font-size: 0.875rem;
        font-size: var(--group-product-container-font-size);
        line-height: var(--group-product-container-font-size);
        color: $darkestGray;
        margin-top: 10px;

        .arrow-container {
          position: relative;
          top: calc(var(--group-product-container-font-size) * -0.5);
          margin-left: 5px;
          margin-right: 10px;
        }

        span {
          font-style: italic;
          font-weight: 500;
          margin-right: 10px;
        }

        .divider-block {
          --divider-block-size: 20px;
          width: var(--divider-block-size);
          height: var(--divider-block-size);
          border-radius: 5px;
          background: $mediumLightGray;
          margin-right: 3px;
        }

        .product-name {
          font-weight: 700;
        }
      }
    }

    .buttons-grid-area {
      display: flex;
      flex-direction: row;
      flex-wrap: wrap;
      justify-content: flex-end;
      align-items: center;
      gap: 10px;

      .target-panelists-button {
        margin: 0;

        svg {
        }

        span {
        }
      }

      .actions-button-container {
        position: relative;
        width: fit-content;
        height: fit-content;

        .actions-button {
          margin: 0;

          svg {
          }

          span {
          }
        }

        popover {
          --router-link-color: #{$darkGray};
          --router-link-size: 0.9rem;
          --router-link-hover-color: #{$blue};

          .popover-item {
            text-decoration: none;
            color: var(--router-link-color);
            font-size: var(--router-link-size);
            transition: color 0.2s ease;
            cursor: pointer;
            width: fit-content;
            white-space: nowrap;

            &:hover {
              color: var(--router-link-hover-color);
            }
          }
        }
      }
    }
  }

  .group-summary-row {
    @include border();
    display: flex;
    flex-flow: row nowrap;
    margin-bottom: 45px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);

    .group-summary-section {
      flex-grow: 1;
      padding: 20px 15px;

      &:not(:last-child) {
        border-right: 1px solid $mediumLightGray;
      }

      &.budget-section {
        flex-grow: 3;

        .section-label {
          margin-bottom: 10px;
        }

        .section-content {
          font-size: 0.75rem;

          percentage-bar {
            width: 126px;
          }

          .spent {
            font-weight: 700;
            margin-left: 12px;
          }

          .remaining {
            font-weight: 500;
            margin-left: 8px;
          }
        }
      }

      .section-label {
        font-size: 0.75rem;
        font-weight: 500;
        color: $darkGray;
        margin-bottom: 12px;
        display: flex;

        span {
          margin-left: auto;
          text-decoration: underline;
          cursor: pointer;
        }
      }

      .section-content {
        font-weight: 700;
        display: flex;
        font-size: 1rem;
        align-items: center;

        .owner-name-container{
          margin-left: 10px;
          font-size: 0.875rem;
          font-weight: 500;
        }
      }
    }
  }

  .group-details-header {
    display: flex;
    flex-flow: row nowrap;
    align-items: center;
    justify-content: space-between;
    gap: 10px;
    margin-bottom: 35px;

    .header-label {
      font-size: 1.25rem;
      font-weight: 700;
      color: #292727;
    }

    .header-elem {
      display: flex;
      flex-direction: row;
      justify-content: space-between;
      align-items: center;
      font-size: 0.875rem;
      font-weight: 700;

      p {
        margin: 0 0 0 8px;
      }

      .search-container {
        flex: 2;
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-right: 10px;

        .search-input {
          width: 100%;

          ::ng-deep input {
            background-image: url("data:image/svg+xml,%3Csvg width='10' height='13' viewBox='0 0 10 13' fill='none' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath fill-rule='evenodd' clip-rule='evenodd' d='M7.23259 9.71276C5.05989 10.8734 2.33687 10.2213 0.896706 8.09053C-0.673857 5.76576 -0.127797 2.55724 2.11586 0.929125C4.35868 -0.698219 7.45525 -0.132417 9.02592 2.19236C10.4703 4.32995 10.1246 7.21541 8.3157 8.9317L9.87965 11.23C10.0892 11.5386 10.0182 11.965 9.72045 12.183C9.42266 12.4001 9.01023 12.3266 8.80071 12.018L7.23259 9.71276ZM7.0503 8.23243C8.69758 7.03757 9.09764 4.68192 7.94529 2.97613C6.7913 1.27015 4.51867 0.854776 2.8724 2.04963C1.22512 3.24449 0.825061 5.59927 1.97741 7.30593C3.1281 9.00762 5.39242 9.42472 7.0389 8.24096C7.04219 8.2384 7.04617 8.23499 7.0503 8.23243Z' fill='%23292727'/%3E%3C/svg%3E");
            background-repeat: no-repeat;
            background-position: 10px center;
            background-size: 12px;
            padding-left: 30px;
            border-radius: 8px;
          }
        }
      }
      .filter-container {
        flex: 1;
        display: flex;
        justify-content: space-between;
        align-items: center;

        .filter-input {
          width: 100%;
          
          ::ng-deep input {
            border-radius: 8px;
          }
        }
      }
    }
  }

  .response-details-section {
    width: 100%;
    margin-bottom: 65px;
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 20px;

    .section-header {
      font-size: 0.875rem;
      font-weight: 700;
      margin-bottom: 20px;
      grid-column: span 2;
    }

    .section-content {
      grid-column: span 2;

      &:not(.overall) {
        border: 1px solid $lightGray;
        border-radius: 18px;
        padding: 15px;
      }

      &.overall {
        display: grid;
        grid-template-columns: repeat(
          auto-fit,
          minmax(140px, 1fr) minmax(140px, 1fr) minmax(140px, 1fr)
        );
        gap: 10px;
      }

      &.top-descriptors {
        grid-column: span 1;
        .tabs-container {
          display: flex;
          flex-direction: row;
          justify-content: space-between;
          align-items: end;

          .header-elem {
            display: flex;
            flex-direction: row;
            justify-content: space-between;
            align-items: start;
          }

          .toggle-tabs {
            margin: 0;

            .tab-button {
              border: none;
              background: none;
              padding: 6px 12px;
              border-radius: 20px;
              cursor: pointer;
              font-size: 14px;
              font-weight: 500;
              color: #666;
              transition: all 0.3s ease;

              &.active {
                background-color: #007bff;
                color: white;
                box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
              }

              &:hover:not(.active) {
                background-color: #e6e6e6;
              }
            }
          }
        }
      }
      &.aroma-distribution {
        grid-column: span 1;
      }

      .descriptors-table {
        margin-top: 15px;
      }
    }

    &.demographic-breakdown,
    &.additional-comments {
      grid-template-columns: 1fr;
    }
  }
}

.status-cell{
  display: flex;
  align-items: center;
  gap: 8px;

  .color-dot{
      width: 10px;
      height: 10px;
      border-radius: 50%;
  }

  .status-text{
      text-transform: capitalize;
  }
}

.user-icon-cell{
  justify-self: end;
}
