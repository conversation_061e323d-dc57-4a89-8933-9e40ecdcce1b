import { Component, Input, inject } from '@angular/core';
import { CommonModule } from '@angular/common';
import { ResponseDataCommentBlockComponent } from '../response-data-comment-block/response-data-comment-block.component';
import { ActivatedRoute } from '@angular/router';

@Component({
  selector: 'response-data-comments',
  standalone: true,
  imports: [CommonModule,
    ResponseDataCommentBlockComponent],
  templateUrl: './response-data-comments.component.html',
  styleUrl: './response-data-comments.component.scss'
})
export class ResponseDataCommentsComponent {
  @Input() comments: any;

  @Input() step: any;
  private route = inject(ActivatedRoute);

  constructor() {
    
  }
  
  ngOnInit(){
    this.comments = this.comments.filter((comment: any) => {
      return comment.comment.length > 0 && comment.question_id === this.step.id;
    }).map((comment: any) => ({
      comment: comment.comment,
      date: new Date(comment.created?._seconds * 1000).toLocaleDateString()
    }));
  }

  mockComments = [
    {
      comment: 'This is a comment',
      respondent: '<PERSON>',
      date: '2021-01-01'
    },
    {
      comment: 'Lorem ipsum dolor sit amet, consectetur adipiscing elit. Sed do eiusmod tempor incididunt ut labore.',
      respondent: 'Jane <PERSON>',
      date: '2021-02-15'
    },
    {
      comment: 'Ut enim ad minim veniam, quis nostrud exercitation ullamco laboris nisi ut aliquip ex ea commodo.',
      respondent: 'Robert Johnson',
      date: '2021-03-22'
    },
    {
      comment: 'Duis aute irure dolor in reprehenderit in voluptate velit esse cillum dolore eu fugiat nulla pariatur.',
      respondent: 'Sarah Williams',
      date: '2021-04-30'
    },
    {
      comment: 'Excepteur sint occaecat cupidatat non proident, sunt in culpa qui officia deserunt mollit anim id est.',
      respondent: 'Michael Brown',
      date: '2021-05-12'
    },
    {
      comment: 'Sed ut perspiciatis unde omnis iste natus error sit voluptatem accusantium doloremque laudantium.',
      respondent: 'Emily Davis',
      date: '2021-06-18'
    },
    {
      comment: 'Nemo enim ipsam voluptatem quia voluptas sit aspernatur aut odit aut fugit, sed quia consequuntur.',
      respondent: 'David Miller',
      date: '2021-07-25'
    },
    {
      comment: 'Neque porro quisquam est, qui dolorem ipsum quia dolor sit amet, consectetur, adipisci velit.',
      respondent: 'Lisa Anderson',
      date: '2021-08-03'
    },
    {
      comment: 'Quis autem vel eum iure reprehenderit qui in ea voluptate velit esse quam nihil molestiae consequatur.',
      respondent: 'James Wilson',
      date: '2021-09-14'
    },
    {
      comment: 'At vero eos et accusamus et iusto odio dignissimos ducimus qui blanditiis praesentium voluptatum.',
      respondent: 'Jennifer Taylor',
      date: '2021-10-27'
    },
    {
      comment: 'Et harum quidem rerum facilis est et expedita distinctio. Nam libero tempore, cum soluta nobis.',
      respondent: 'William Moore',
      date: '2021-11-30'
    }
  ]
}
