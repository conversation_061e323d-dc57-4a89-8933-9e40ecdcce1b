:host{
    --table-columns: 1fr 1fr 1fr 1fr 1fr 1fr 1fr 1fr 1fr;
    display: flex;
    flex-direction: column;
    justify-content: flex-start;
    align-items: center;

    .table-header{
        background: var(--table-header-background);
        color: var(--table-header-text-color);
        font-size: var(--table-header-text-size);
        font-weight: var(--table-header-text-weight);
        display: grid;
        grid-template-columns: var(--table-columns);
        column-gap: var(--table-column-gap);
        row-gap: var(--table-row-gap);
        padding: var(--table-header-cell-padding);
        align-items: center;
        height: auto;
        min-height: var(--table-header-height);
        border-radius: var(--table-header-border-radius);
    }

    panelists-table-row{
        background: var(--table-row-odd-background);
        display: grid;
        grid-template-columns: var(--table-columns);
        column-gap: var(--table-column-gap);
        row-gap: var(--table-row-gap);
        padding: var(--table-cell-padding);
        align-items: center;
        height: auto;
        min-height: var(--table-row-height);
        border-radius: var(--table-row-border-radius);
        
        &:nth-child(even){
            background: var(--table-row-even-background);
        }
    }
}