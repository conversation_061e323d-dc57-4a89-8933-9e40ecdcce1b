import { Component, Input } from '@angular/core';
import { NgFor } from '@angular/common';

@Component({
  selector: 'form-steps',
  standalone: true,
  imports: [NgFor],
  templateUrl: './form-steps.component.html',
  styleUrl: './form-steps.component.scss'
})
export class FormStepsComponent {

  @Input() currentStep: number = 0;
  @Input() totalSteps: number = 0;
  formSteps: number[] = [];

  ngOnInit(){
    this.formSteps = Array.from({length: this.totalSteps}, (_, i) => i + 1);
  }
}
