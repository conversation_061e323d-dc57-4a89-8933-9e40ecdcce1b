@import '../../../styles.scss';

:host{
    display: block;
    // @extend %fullscreen-fixed;
    // @extend %settings-host;

    

    .settings-section{
        
        &.general{
            grid-template-columns: auto 1fr;
            align-items: flex-start;
            gap: 20px;

            .section-header{
                grid-column: span 2;
            }

            .users-table-header{
                display:flex;
                background-color: #5E1268;
                width:100%;
                flex-direction: row;
                justify-content: space-between;
            }
            .profile-image{
                width: 66px;
                height: 66px;
                background: linear-gradient(152.4deg, #FEC33C 13.49%, #5E1268 87.07%);
                border-radius: 100%;
                display: flex;
                align-items: center;
                justify-content: center;
                overflow: hidden;
                color: white;

                img{
                    height: 100%;
                    object-fit: cover;
                }
            }

            .inputs-section{

                input[type="text"]{
                    border-radius: 14px;
                    width: 100%;
                    margin-bottom: 20px;
                }
            }
        }
        
        &.about-me{

            .inputs-section{
                display: grid;
                column-gap: 20px;
                grid-template-columns: 1fr;

                label.primary{
                    grid-column: 1 / -1;
                }

                &.gender,
                &.age{
                    grid-template-columns: repeat(3, 1fr);
                }

                &.ethnicity{
                    grid-template-columns: repeat(2, 1fr);
                }

                .radio-container{
                    input:checked + label{
                        background-color: $blue;
                        font-weight: 700;
                        border: none;
                        color: #fff;
                        box-shadow: 0px 4px 10px 0px rgba(0, 0, 0, 0.18);
                    }

                    label{
                        background-color: $lightestGray;
                        border: $inputBorder;
                        font-weight: 500;
                        font-size: .9rem;
                        display: flex;
                        align-items: center;
                        justify-content: center;
                        border-radius: 14px;
                        height: 40px;
                        cursor: pointer;
                        margin-bottom: 20px;
                        transition: all .2s ease;
                    }
                }

                select{
                    background-color: #e4e4e4;
                    width: 100%;
                    // outline: none;
                    margin-bottom: 20px;

                    // &:focus{
                        // outline: 1px solid $focusInputBorder;
                    // }
                }
            }
        }
    }
}
