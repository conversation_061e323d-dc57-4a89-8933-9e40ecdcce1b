<div class="option-header">
    <button *ngIf="showRemoveButton()" class="remove-option-button" type="button" (click)="removeOption()">
        <svg xmlns="http://www.w3.org/2000/svg" width="11" height="11" viewBox="0 0 11 11" fill="none">
            <path d="M5.5 4.3886L9.6587 0.229905C9.96523 -0.0766349 10.4629 -0.0766349 10.7701 0.229905C11.0766 0.537118 11.0766 1.03475 10.7701 1.3413L6.6114 5.5L10.7701 9.6587C11.0766 9.96523 11.0766 10.4629 10.7701 10.7701C10.4629 11.0766 9.96525 11.0766 9.6587 10.7701L5.5 6.6114L1.3413 10.7701C1.03477 11.0766 0.537135 11.0766 0.229905 10.7701C-0.0766349 10.4629 -0.0766349 9.96525 0.229905 9.6587L4.3886 5.5L0.229905 1.3413C-0.0766349 1.03477 -0.0766349 0.537135 0.229905 0.229905C0.537118 -0.0766349 1.03475 -0.0766349 1.3413 0.229905L5.5 4.3886Z" fill="#292727"/>
        </svg>
    </button>
    <div class="option-container-label">Option {{index + 1 | digitToWord: true}}</div>
</div>

<div class="input-container">
    <label for="option-label-{{index}}"><span>Option Text</span></label>
    <input-text [id]="'option-label-{{index}}'" [control]="option.controls['label']" [placeholder]="'option text'"></input-text>
</div>

<div class="input-container">
    <label for="option-instructions-{{index}}">
        <span>Option Instructions</span>
        <div class="sub-label">
            <span>Optional</span>
        </div>
    </label>
    <input-text-area [id]="'option-instructions-{{index}}'" [control]="option.controls['instructions']" [placeholder]="'instructions'"></input-text-area>
</div>