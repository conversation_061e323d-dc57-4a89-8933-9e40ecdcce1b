{"name": "sensei-admin", "version": "0.0.0", "scripts": {"ng": "ng", "start": "ng serve", "build": "ng build", "watch": "ng build --watch --configuration development", "test": "ng test"}, "private": true, "dependencies": {"@angular/animations": "^18.2.0", "@angular/common": "^18.2.0", "@angular/compiler": "^18.2.0", "@angular/core": "^18.2.0", "@angular/fire": "^18.0.1", "@angular/forms": "^18.2.0", "@angular/platform-browser": "^18.2.0", "@angular/platform-browser-dynamic": "^18.2.0", "@angular/router": "^18.2.0", "@lamb-sensory/lamb-component-library": "^2.0.0", "chart.js": "^4.4.7", "image-resize-compress": "^2.0.0", "ngx-bootstrap": "^18.1.3", "ngx-clipboard": "^16.0.0", "ngx-drag-drop": "^18.0.2", "rxjs": "~7.8.0", "tslib": "^2.3.0", "zone.js": "~0.14.10"}, "devDependencies": {"@angular-devkit/build-angular": "^18.2.12", "@angular/cli": "^18.2.12", "@angular/compiler-cli": "^18.2.0", "@spartan-ng/cli": "^0.0.1-alpha.380", "@types/jasmine": "~5.1.0", "jasmine-core": "~5.2.0", "karma": "~6.4.0", "karma-chrome-launcher": "~3.2.0", "karma-coverage": "~2.2.0", "karma-jasmine": "~5.1.0", "karma-jasmine-html-reporter": "~2.1.0", "tailwind-merge": "^2.2.0", "tailwindcss": "^3.0.2", "tailwindcss-animate": "^1.0.6", "typescript": "~5.5.2"}}