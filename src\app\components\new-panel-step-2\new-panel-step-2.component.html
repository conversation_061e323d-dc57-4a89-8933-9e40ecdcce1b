<div class="form-step-container">
  <form [formGroup]="form">
    <div class="form-step-title">Choose the product to collect data on</div>
    <ng-container *ngIf="products$ | async as productsResponse">
      <input-radio-block
        *ngFor="let product of productsService.products; let i = index"
        [control]="productControl"
        [id]="'product-control-' + i"
        [value]="product.id"
        [name]="'product-control'"
        [class.active]="productControl.value === product.id"
      >
        <product-block [product]="product"></product-block>
      </input-radio-block>
      <div *ngIf="!productsResponse.products?.length" class="no-products">
        No products available
      </div>
    </ng-container>
  </form>
  <pagination
    [state]="paginationState$ | async"
    (pageChange)="onPageChange($event)"
  ></pagination>
</div>
