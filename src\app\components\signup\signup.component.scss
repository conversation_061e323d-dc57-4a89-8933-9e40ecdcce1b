// @import "../../../styles.scss";
@import "../../../_colors.scss";

:host {
    position: fixed;
    top: 0;
    left: 0;
    width: 100vw;
    height: 100vh;
    overflow-y: auto;
    background: $blackToDarkPurple;
    box-sizing: border-box;
    padding-bottom: 50px;

    .login-wrap{
        display: flex;
        background: $darkPurpleToBlack;
        position: relative;
        background-size: contain, auto;
        background-repeat: no-repeat, no-repeat;
        background-position: center -50px, center;
        width: calc(100vw - 40px);
        margin: auto;
        margin-top: 50px;
        border-radius: 40px;
        height: auto;
        padding-bottom: 30px;
        justify-content: flex-start;
        align-items: center;
        flex-flow: column nowrap;
        overflow: hidden;

        &::after {
            content: '';
            position: absolute;
            top: -50px;
            left: 0;
            width: 100%;
            height: 100%;
            background-image: url('../../../assets/images/banner-bg\ 2.png');
            background-size: contain;
            background-repeat: no-repeat;
            background-position: center top;
            pointer-events: none;
            z-index: 1;
        }

        // > * {
        //     position: relative;
        //     z-index: 2;
        // }

        .logo{
            margin-top: 40px;
            margin-bottom: 20px;
            position: relative;
        }

        .welcome{
            font-size: 1.9rem;
            margin-bottom: 10px;
            font-weight: 700;
            color: #fff;
        }

        .sub-welcome{
            font-size: 1rem;
            margin-bottom: 20px;
            color: #fff;
        }

        .sso-login-container{
            display: grid;
            // grid-template-columns: 1fr 1fr;
            grid-template-columns: 1fr;
            gap: 20px;
            margin-bottom: 20px;
            justify-items: center;
            width: 100%;
            padding: 0px 40px;
            box-sizing: border-box;

            gray-button{
                width: 100%;
            }

            .or-container{
                width: 100%;
                display: grid;
                grid-template-columns: 1fr auto 1fr;
                gap: 10px;
                // margin-bottom: 20px;
                align-items: center;
                justify-content: center;

                .or-line{
                    border-top: 1px solid $darkGray;
                    position: relative;
                    top: 1px;
                }

                .or-text{
                    color: #fff;
                    font-size: .9rem;
                    font-weight: 400;
                }
            }
        }

        form{
            display: grid;
            grid-template-columns: 1fr;
            margin-bottom: 20px;
            justify-items: center;
            width: 100%;
            padding: 0px 40px;
            box-sizing: border-box;

            .form-group{
                margin-bottom: 15px;
                width: 100%;
                position: relative;

                &.checkbox-group{
                    
                    .form-group-inner{
                        display: flex;
                        align-items: flex-start;
                        gap: 7px;
                        margin-bottom: 15px;

                        label{
                            margin-bottom: 0;
                            line-height: 16px;
                        }

                        input{
        
                            &[type="checkbox"] {
                                width: 16px;
                                height: 16px;
                                background-color: #fff;
                                border: 2px solid #fff;
                                outline: $inputBorder;
                                border-radius: 50%;
                                margin-right: 10px;
                                cursor: pointer;
                                flex-shrink: 0;
            
                                &:checked {
                                    background-color: $blue;
                                }
            
                                &:focus {
                                    outline-color: $blue;
                                }
                            }
                        }
                    }
                }

                &.error{

                    label{
                        color: $red;
                        animation: errorShake .7s ease 0s 1 normal forwards;	
                    }

                    input{
                        outline: 2px solid $red;
                        animation: errorShake .7s ease 0s 1 normal forwards;	
                    }
                }

                label{
                    display: block;
                    margin-bottom: 5px;
                    font-size: .8rem;
                    font-weight: 500;
                    color: #fff;
                }
                
                input{
                    width: 100%;
                }

                $showPasswordSize: 20px;

                .showPassword{
                    width: $showPasswordSize;
                    height: $showPasswordSize;
                    position: absolute;
                    right: 13px;
                    bottom: calc(50% - $showPasswordSize);                  
                    cursor: pointer;

                    svg{
                        width: 100%;
                        height: 100%;
                        position: absolute;

                        line{
                            transform: translate(-18px, -19px);
                        }
                    }
                }

                span.optional{
                    text-transform: uppercase;
                    font-size: .6rem;
                    font-weight: 700;
                    color: #fff;
                    margin-left: 5px;
                }
            }

            .reset{
                font-size: .8rem;
                font-weight: 500;
                cursor: pointer;
                width: 100%;
                text-align: right;
                position: relative;
                top: -5px;
                color: #fff;
            }

            .bottom{
                width: 100%;
                margin-top: 15px;

                gray-button{
                    width: 100%;
                }
            }
        }

        .signup-link{
            width: 265px;
            margin: 0 auto;
            border-top: 1px solid $loginBorderStroke;
            padding-top: 20px;
            font-weight: 700;
            font-size: .9rem;
            color: #fff;
            margin-top: 20px;
        
            a{
                text-decoration: underline;
                cursor: pointer;
                font-weight: 500;
                color: #fff;
                // color: $appText;

                &:visited{
                    color: inherit;
                }
            }
        }

        p.error{
            color: #fff;
            font-size: 12px;
            margin-bottom: 20px;
            background-color: $red;
            padding: 10px;
            border-radius: 5px;
            animation: errorShake .7s ease 0s 1 normal forwards;	
            margin-top: 0;
            text-align: center;
            font-weight: 700;
        }
    }

    .loading {
        width: 100%;
        position: fixed;
        top: 0;
        left: 0;
        height: 100%;
        display: flex;
        background-color: rgba(0, 0, 0, .7);
        backdrop-filter: blur(3px);
    }

    @media (min-width: 426px) {
        .login-wrap{
            width: 400px;
            background-position-y: 0px;
        }
    }

    @media (min-width: 768px) {
        .login-wrap{
            width: 700px;

            &::after {
                top:-140px;
            }

            form{
                grid-template-columns: repeat(8, 1fr);
                column-gap: 25px;
                row-gap: 10px;
                grid-auto-rows: max-content;

                .section-label{
                    grid-column: span 8;
                    color: rgba(255, 255, 255);
                    border-bottom: 1px solid rgba(255, 255, 255, .5);
                    font-size: .8rem;
                    font-weight: 700;
                    margin-bottom: 10px;
                    width: 100%;
                    padding-bottom: 2px;
                    margin-top: 15px;

                    &.affiliate{
                        grid-column: span 3;
                    }

                    &.general{
                        grid-column: span 5;
                    }
                }

                .form-group{
                    grid-column: span 4;

                    &.affiliate-group{
                        grid-column: span 3;
                    }

                    &.checkbox-group{
                        grid-column: span 5;
                    }
                }

                .bottom{
                    grid-column: span 8;

                    gray-button{
                        width: 300px;
                    }
                }
            }

            .signup-link{
                width: calc(100% - 80px);
                text-align: center;
            }
        }
    }

}

@keyframes errorShake {
	0%,
	100% {
		transform: translateX(0);
	}

	10%,
	30%,
	50%,
	70% {
		transform: translateX(-5px);
	}

	20%,
	40%,
	60% {
		transform: translateX(5px);
	}

	80% {
		transform: translateX(3px);
	}

	90% {
		transform: translateX(-3px);
	}
}