@import '../../../_colors';

:host{
    --bar-height: 25px;
    --bar-padding: 0px 0px 0px 0px;
    --bar-background-color: #{$lightGray};
    --bar-border-radius: 8px;

    display: flex;
    width: 100%;
    height: var(--bar-height);
    align-items: center;
    gap: 0px;
    flex-flow: row nowrap;
    padding: var(--bar-padding);
    background-color: var(--bar-background-color);
    border-radius: var(--bar-border-radius);
    overflow: hidden;
    // position: relative;

    .percentage-bar-inner{
        width: 100%;
        height: 100%;
        overflow: hidden;
        border-radius: calc(var(--bar-border-radius) - 4px);
        display: flex;
        flex-flow: row nowrap;
        gap: 0px;

        .percentage-bar-piece{
            --piece-color: #{$mediumGray};
            height: 100%;
            background-color: var(--piece-color);
            // position: relative;
            
            &:hover {
                cursor: pointer;
            }
        }
    }
}
