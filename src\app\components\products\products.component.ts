import { Component, inject, OnInit } from '@angular/core';
import { AsyncPipe, CommonModule } from '@angular/common';
import { PageHeaderComponent } from '../page-header/page-header.component';
import { ProductsService } from '../../services/products.service';
import { ConstantsService } from '../../services/constants.service';
// import { ProductsTableComponent } from '../products-table/products-table.component';
import { PopupService } from '../../services/popup.service';
import { ActivatedRoute } from '@angular/router';
import { Observable, map, switchMap, tap, share } from 'rxjs';
import { PaginationComponent } from '../pagination/pagination.component';
import { LoadingService } from '../../services/loading.service';
import { Router } from '@angular/router';
import { LambTableComponent, HeaderCell } from '@lamb-sensory/lamb-component-library';
import { TABLE_STYLES } from '../../config/table-styles.config';
// import { createTableConfig } from '../../config/table-styles.config';
import { SampleAdapter } from '../../adapters/sample.adapter';
import { ApiService } from '../../services/api.service';

@Component({
  selector: 'products',
  standalone: true,
  imports: [PageHeaderComponent, AsyncPipe, PaginationComponent, LambTableComponent, CommonModule],
  templateUrl: './products.component.html',
  styleUrl: './products.component.scss'
})
export class ProductsComponent implements OnInit {

  productsService = inject(ProductsService);
  popupService = inject(PopupService);
  route = inject(ActivatedRoute);
  loadingService = inject(LoadingService);
  router = inject(Router);
  constantsService = inject(ConstantsService);
  apiService = inject(ApiService);
  // productsSubscription!: Subscription;
  // tableConfig: any;
  // productsArray: any[] = [];
  tableHeaders: HeaderCell[] = [
    {
      value: 'Sample ID',
      key: 'sampleId'
    },
    {
      value: 'Name',
      key: 'name'
    },
    {
      value: 'Type',
      key: 'sampleType',
      style: {
        width: '25ch'
      }
    },
    {
      value: '# Panels Associated',
      key: 'associatedPanelCount',
      style: {
        width: '23ch'
      }
    },
    {
      value: '# Responses Associated',
      key: 'associatedResponseCount',
      style: {
        width: '28ch'
      }
    },
    {
      value: 'Producer',
      key: 'producer',
      style: {
        width: '25ch'
      }
    }
  ];
  tableStyles = TABLE_STYLES;
  
  // Shared source Observable
  private productsSource$!: Observable<any>;
  products$!: Observable<any>;
  // tableConfig!: TableConfig;
  productsArray: any[] = [];
  paginationState$!: Observable<any>;

  ngOnInit() {
    this.loadingService.setLoading('products', true);
    
        // Create a shared source observable
    this.productsSource$ = this.productsService.products$.pipe(
      tap(() => this.loadingService.setLoading('products', false)),
      share()
    );
    
    // Derive products$ from the shared source
    this.products$ = this.productsSource$.pipe(
      switchMap(async (data) => {
        this.productsArray = data['products'];
        this.productsService.productsArray = await this.getTableData(data['products'])
        // this.tableConfig = await this.configureTable(data['products']);
        return data['products'];
      })
    );
    
    // Derive paginationState$ from the same shared source
    this.paginationState$ = this.productsSource$.pipe(
      map(response => ({
        currentPage: response.currentPage,
        totalPages: response.totalPages,
        hasMore: response.hasMore
      }))
    );
    this.setTableHeaderColors();
  }

  setTableHeaderColors(){
    this.tableStyles = structuredClone(TABLE_STYLES);
    this.tableStyles.header!.style = {
      background: '#F5D9FF',
      color: "#5B1E98"
    }
  }

  onPageChange(page: number) {
    this.productsService.setPage(page);
  }

  async getTableData(data: any){
    const adapter = new SampleAdapter(this.constantsService, this.apiService);
    return await adapter.adapt(data)
  }
  
  onRowClick(event: any){
    console.log('event', event);
    this.router.navigate([`/products/${event.cell.data.sampleId}`]);
  }
}
