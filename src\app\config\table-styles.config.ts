// import { TableConfig } from '@lamb-sensory/lamb-component-library';

// export const TABLE_STYLES = {
//   header: {
//     style: {
//       background: '#CBE5FD',
//       color: '#00519C',
//       padding: '10px',
//       borderRadius: '18px'
//     },
//     cell: {
//       style: {
//         padding: '6px 10px'
//       }
//     }
//   },
//   body: {
//     row: {
//       style: {
//         background: '#F7F7F7',
//         padding: '10px',
//         borderRadius: '18px'
//       }
//     },
//     cell: {
//       style: {
//         padding: '22px 10px',
//         fontWeight: '500'
//       }
//     }
//   },
//   base: {
//     fontSize: '0.8125rem',
//     borderWidth: '0px'
//   }
// } as const;

// export function createTableConfig(headerData: any[], bodyData: any[]): TableConfig {
//   return {
//     header: {
//       data: headerData,
//       ...TABLE_STYLES.header
//     },
//     body: {
//       data: bodyData,
//       ...TABLE_STYLES.body
//     },
//     style: {
//       ...TABLE_STYLES.base
//     }
//   };
// }

import { TableStyles } from '@lamb-sensory/lamb-component-library';

export const TABLE_STYLES: TableStyles = {
  header: {
    style: {
      background: '#CBE5FD',
      color: '#00519C',
      padding: '10px',
      borderRadius: '5px',
    },
    cell: {
      style: {
        padding: '6px 10px'
      }
    }
  },
  body: {
    style: {
      fontWeight: '700'
    },
    row: {
      style: {
        background: '#F7F7F7',
        padding: '10px',
        borderRadius: '5px',
        height: '50px',
      }
    },
    cell: {
      style: {
        padding: '12px 10px',
      }
    },
  },
  style: {
    fontSize: '0.8125rem',
    borderWidth: '0px',
    fontFamily: 'Inter'
  }
} as const;

