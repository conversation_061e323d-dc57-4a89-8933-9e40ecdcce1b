<nav></nav>
<router-outlet />
<footer></footer>

<popup-overlay *ngIf="popupService.needPopupOverlay" (click)="popupService.closeAllPopups()"></popup-overlay>
<new-panel-form class="full-screen-form" *ngIf="popupService.popups['newPanel']" [@slideUp]></new-panel-form>
<new-product-form class="full-screen-form" *ngIf="popupService.popups['newProduct']" [@slideUp]></new-product-form>
<new-group-form class="full-screen-form" *ngIf="popupService.popups['newGroup']" [@slideUp]></new-group-form>
<new-project-form class="full-screen-form" *ngIf="popupService.popups['newProject']" [@slideUp]></new-project-form>
<new-template-form class="full-screen-form" *ngIf="popupService.popups['newTemplate']" [@slideUp]></new-template-form>
<!-- <panel-details class="full-screen-form" *ngIf="popupService.popups['panelDetails']" [@slideUp]></panel-details> -->
<!-- <product-details class="full-screen-form" *ngIf="popupService.popups['productDetails']" [@slideUp]></product-details> -->
<!-- <toast-component *ngIf="toastService.showToast"></toast-component> -->
<custom-step-form class="full-screen-form" *ngIf="popupService.popups['customStep']" [@slideUp]></custom-step-form>
