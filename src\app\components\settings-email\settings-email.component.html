<form [formGroup]="emailForm">
    <div class="settings-sub-page-header">
        <!-- <button-standard>Back</button-standard> -->
        <div class="header-title">Email Address</div>
    </div>

    <div class="settings-section">
        <div class="section-header">
            <span>Current</span>
        </div>
        <div class="section-content">
            <label class="primary">Email Address</label>
            <div class="current-email">{{ profileService.authUser?.email }}</div>
            <div class="verification-status-container" *ngIf="!profileService.authUser?.emailVerified">
                <div class="status">
                    <svg xmlns="http://www.w3.org/2000/svg" width="13" height="13" viewBox="0 0 13 13" fill="none">
                        <path fill-rule="evenodd" clip-rule="evenodd" d="M6.5 0C10.0897 0 13 2.91027 13 6.5C13 10.0897 10.0897 13 6.5 13C2.91027 13 0 10.0897 0 6.5C0 2.91027 2.91027 0 6.5 0ZM6.00009 8.9037C6.00009 9.72635 6.99992 9.60449 6.99992 8.98552C6.99992 8.16342 6.00009 8.28529 6.00009 8.9037ZM6.00009 4.01397V7.19911C6.00009 7.85758 6.99992 7.85758 6.99992 7.19911V4.01397C6.99992 3.35607 6.00009 3.35607 6.00009 4.01397ZM10.3893 2.61072C8.24129 0.462684 4.75888 0.462684 2.61128 2.61072C0.463248 4.75875 0.463248 8.24116 2.61128 10.3888C4.75931 12.5368 8.24173 12.5368 10.3893 10.3888C12.5368 8.24131 12.5374 4.75875 10.3893 2.61072Z" fill="black"/>
                    </svg>
                    Unverified
                </div>
                <div class="resend-verification">Didn't receive the verification link? <span (click)="sendVerificationEmail()">Send it again</span></div>
            </div>
        </div>
    </div>

    <div class="settings-section">
        <div class="section-header">
            <span>Change Email</span>
        </div>

        <div class="section-content" *ngIf="!emailUpdated" [@fadeIn]>
            <div class="inputs-section">
                <label class="primary">New Email Address</label>
                <input type="text" formControlName="new_email">
            </div>
            <div class="inputs-section">
                <label class="primary">Confirm New Email Address</label>
                <input type="text" formControlName="confirm_new_email">
            </div>

            <button-standard class="blue" (onAction)="updateEmail()">Update Email Address</button-standard>
        </div>
    </div>

    <div class="settings-section variation-1" *ngIf="emailUpdated" [@fadeIn]>
        <div class="section-header">
            <span>Check Your Email</span>
        </div>
        <div class="section-content">
            <div class="text">We've sent you an email with a link to verify your new email address. Please check your inbox and click the link to continue.</div>
            <button-standard class="blue" (onAction)="dismissEmailUpdated()">OK</button-standard>
        </div>
    </div>
</form>