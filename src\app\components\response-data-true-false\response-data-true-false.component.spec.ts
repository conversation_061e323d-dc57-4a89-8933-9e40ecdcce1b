import { ComponentFixture, TestBed } from '@angular/core/testing';

import { ResponseDataTrueFalseComponent } from './response-data-true-false.component';

describe('ResponseDataTrueFalseComponent', () => {
  let component: ResponseDataTrueFalseComponent;
  let fixture: ComponentFixture<ResponseDataTrueFalseComponent>;

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      imports: [ResponseDataTrueFalseComponent]
    })
    .compileComponents();

    fixture = TestBed.createComponent(ResponseDataTrueFalseComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
