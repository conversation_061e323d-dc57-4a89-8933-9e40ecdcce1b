import { Component, inject, ViewChild, TemplateRef } from '@angular/core';
import { PageHeaderComponent } from '../page-header/page-header.component';
import { GroupsService } from '../../services/groups.service';
import { AsyncPipe, NgIf } from '@angular/common';
import { LambTableComponent, HeaderCell, LambGPIconComponent } from '@lamb-sensory/lamb-component-library';
import { TABLE_STYLES } from '../../config/table-styles.config';
import { map, Observable, Subscription, share } from 'rxjs';
import { ProfileService } from '../../services/profile.service';
import { PopupService } from '../../services/popup.service';
import { PaginationComponent } from '../pagination/pagination.component';
import { ActivatedRoute, Router, RouterModule } from '@angular/router';

@Component({
  selector: 'groups',
  standalone: true,
  imports: [NgIf,PageHeaderComponent, LambTableComponent, AsyncPipe, PaginationComponent, RouterModule, LambGPIconComponent],
  templateUrl: './groups.component.html',
  styleUrl: './groups.component.scss'
})
export class GroupsComponent {
  profileService = inject(ProfileService);
  groupsService = inject(GroupsService);
  popupService = inject(PopupService);
  // groups:any;
  // subscription: Subscription= new Subscription();
  tableHeaders:HeaderCell[] = [
    {
      value: 'Group Name',
      key: 'name'
    },
    {
      value: 'Group Description',
      key: 'description'
    },
    {
      value: 'Created By',
      key: 'created_by'
    },
    {
      value: '# Panels',
      key: 'panels_count'
    },
    {
      value: '# Users',
      key: 'users_count'
    },
    {
      value: '# Samples',
      key: 'Samples_count'
    }
  ];
  tableData:any[] = [];
  tableStyles = structuredClone(TABLE_STYLES);

  
  route = inject(ActivatedRoute);
  router = inject(Router);

  groupsSource$!: Observable<any>;
  groups$!: Observable<any>;
  paginationState$!: Observable<any>;

  @ViewChild('creatorCellTemplate', {static: true}) creatorCellTemplate!: TemplateRef<any>;

  ngOnInit(){
    this.groupsSource$ = this.groupsService.groups$.pipe(
      // tap(() => this.loadingService.setLoading('groups', false)),
      share()
    );

    this.groups$ = this.groupsSource$.pipe(
      map(async (data) => {
        console.log('data', data);
        
        this.tableData = await this.getTableData(data['groups']);
        return data['groups'];
      })
    );

    this.paginationState$ = this.groupsSource$.pipe(
      map(response => ({
        currentPage: response.currentPage,
        totalPages: response.totalPages,
        hasMore: response.hasMore
      }))
    );

    this.setTableHeaderColors();

  }

  async getTableData(data:any) {
    const tableData: any[] = data.map((group:any) => {
      return {
        name: group.name,
        description: group.description,
        created_by: this.getOwnerName(group),
        created_by__template: this.creatorCellTemplate,
        created_by__templateContext: {$implicit: group},
        panels_count: group.panels.length,
        users_count: group.users.length,
        Samples_count: group.samples.length,
        id: group.id,
        group: group
      }
    });

    return tableData;
  }

  getOwnerName(group:any):string{
    if(!group?.owner){
      return 'undefined';
    }
    let ownerName = group.owner.first_name + ' ' + group.owner.last_name;
    if(ownerName.length){
      return ownerName.trim();
    }
    return 'undefined';
  }

  getOwnerInitials(group:any):string{
    console.log('group for initials', group);
    if(!group?.owner){
      return '';
    }
    return this.getInitialsFromString(group.owner.first_name + ' ' + group.owner.last_name, 2);
  }

  getInitialsFromString(string:string, slice:number = 2):string{
    return string.split(' ').map(name => name[0]).join('').slice(0, slice).toUpperCase();
  }

  onRowClick(event:any) {
    console.log(event);
    const selectedGroup = event.cell.data.group;
    this.groupsService.setSelectedGroup(selectedGroup);
    this.router.navigate([`/groups/${event.cell.data.id}`]);
  }

 getRandomInt(min:number, max:number) {
    min = Math.ceil(min);
    max = Math.floor(max);
    return Math.floor(Math.random() * (max - min + 1)) + min;
  }

  setTableHeaderColors(){
    this.tableStyles = structuredClone(TABLE_STYLES);
    this.tableStyles.header!.style = {
      background: '#CEFFFC',
      color: "#10837B"
    }
  }
  onPageChange(page:number){
    console.log("onPageChange", page);
    this.groupsService.setPage(page);
  }

}
