import { Injectable, inject } from '@angular/core';
import { HttpClient, HttpHeaders } from '@angular/common/http';
import { Observable, BehaviorSubject } from 'rxjs';
import { EnvironmentService } from './environment.service';

export interface Organization {
  id: string;
  name: string;
  type?: string;
  description?: string;
}

@Injectable({
  providedIn: 'root'
})
export class OrganizationsService {
  private http = inject(HttpClient);
  private environmentService = inject(EnvironmentService);
  
  private currentOrganizationSubject = new BehaviorSubject<Organization | null>(null);
  public currentOrganization$ = this.currentOrganizationSubject.asObservable();
  
  private organizationsSubject = new BehaviorSubject<Organization[]>([]);
  public organizations$ = this.organizationsSubject.asObservable();

  private getHeaders() {
    return new HttpHeaders({
      'Content-Type': 'application/json',
    });
  }

  /**
   * Get organizations that the user has access to
   */
  getOrganizations(): Observable<Organization[]> {
    return this.http.get<Organization[]>(
      `${this.environmentService.getBaseUrl()}/organizations`,
      {
        headers: this.getHeaders(),
        withCredentials: true
      }
    );
  }

  /**
   * Get organization details by ID
   */
  getOrganizationById(id: string): Observable<Organization> {
    return this.http.get<Organization>(
      `${this.environmentService.getBaseUrl()}/organizations/${id}`,
      {
        headers: this.getHeaders(),
        withCredentials: true
      }
    );
  }

  /**
   * Set the current organization
   */
  setCurrentOrganization(organization: Organization) {
    this.currentOrganizationSubject.next(organization);
    // Store in localStorage for persistence
    localStorage.setItem('currentOrgId', organization.id);
    localStorage.setItem('currentOrg', JSON.stringify(organization));
  }

  /**
   * Get the current organization from localStorage or subject
   */
  getCurrentOrganization(): Organization | null {
    const current = this.currentOrganizationSubject.value;
    if (current) {
      return current;
    }
    
    // Try to get from localStorage
    const storedOrg = localStorage.getItem('currentOrg');
    if (storedOrg) {
      try {
        const org = JSON.parse(storedOrg);
        this.currentOrganizationSubject.next(org);
        return org;
      } catch (error) {
        console.error('Error parsing stored organization:', error);
      }
    }
    
    return null;
  }

  /**
   * Load organizations for the current user
   */
  async loadUserOrganizations(organizationIds: string[]): Promise<Organization[]> {
    try {
      const organizations: Organization[] = [];
      
      // Fetch each organization by ID
      for (const orgId of organizationIds) {
        try {
          const org = await this.getOrganizationById(orgId).toPromise();
          if (org) {
            organizations.push(org);
          }
        } catch (error) {
          console.error(`Error fetching organization ${orgId}:`, error);
          // Create a fallback organization object
          organizations.push({
            id: orgId,
            name: `Organization ${orgId}`,
            type: 'unknown'
          });
        }
      }
      
      this.organizationsSubject.next(organizations);
      
      // Set the first organization as current if none is set
      if (organizations.length > 0 && !this.getCurrentOrganization()) {
        this.setCurrentOrganization(organizations[0]);
      }
      
      return organizations;
    } catch (error) {
      console.error('Error loading user organizations:', error);
      return [];
    }
  }

  /**
   * Clear current organization (for logout)
   */
  clearCurrentOrganization() {
    this.currentOrganizationSubject.next(null);
    this.organizationsSubject.next([]);
    localStorage.removeItem('currentOrgId');
    localStorage.removeItem('currentOrg');
  }
}
