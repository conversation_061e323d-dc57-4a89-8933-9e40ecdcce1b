:host{
    display: block;
    width: 100%;
    min-height: var(--standard-height);
    height: auto;
    padding: var(--standard-padding);
    box-sizing: border-box;

    .table-container{
        margin-top: 20px;

        pagination {
            display: flex;
            justify-content: center;
            margin-top: 20px;
        }
    }

    popover {
        position: absolute;
        top: 100%;
        right: 0;
        margin-top: 8px;
        z-index: 10;
        background-color: #FFFFFF;
        border-radius: 4px;
        box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
        overflow: hidden;
  
        .popover-item {
          padding: 12px 16px;
          cursor: pointer;
          white-space: nowrap;
          transition: background-color 0.2s ease;
          font-size: 14px;
  
          &:hover {
            background-color: #F5F5F5;
          }
  
          &.delete {
            color: #FF0000;
          }
        }
      }
}