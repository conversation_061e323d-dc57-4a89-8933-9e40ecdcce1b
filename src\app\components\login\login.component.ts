import { Component, inject } from '@angular/core';
import { NgIf } from '@angular/common';
import { FormBuilder, Validators, ReactiveFormsModule, FormGroup } from '@angular/forms';
import { Auth, signInWithEmailAndPassword, sendPasswordResetEmail } from '@angular/fire/auth';
import { Router, RouterModule } from '@angular/router';
import { Subject } from "rxjs";
import { takeUntil } from "rxjs/operators";
import { GrayButtonComponent } from "../gray-button/gray-button.component";
import { ToastService } from "../../services/toast.service";
import { AuthService } from "../../services/auth.service";
import { ButtonStandardComponent } from "../button-standard/button-standard.component";
import { BlurEllipseComponent } from "../blur-ellipse/blur-ellipse.component";
import { ProfileService } from "../../services/profile.service";
import { debounceTime } from 'rxjs/operators';

@Component({
  selector: 'app-login',
  standalone: true,
  imports: [ReactiveFormsModule, NgIf, RouterModule, ButtonStandardComponent, BlurEllipseComponent, GrayButtonComponent],
  templateUrl: './login.component.html',
  styleUrl: './login.component.scss'
})
export class LoginComponent {
  loginForm: FormGroup;

  errorMessage = "";
  hasError = false;
  loading = false;

  private destroy$ = new Subject<void>();
  private submitSubject = new Subject<void>();
  profileService = inject(ProfileService);

  showPassword = false;

  
  ngOnDestroy(){
    this.destroy$.next();
    this.destroy$.complete();
  }

  constructor(
    private fb: FormBuilder,
    private auth: Auth,
    private router: Router,
    private toastService: ToastService,
    public authService: AuthService
  ) {
    this.loginForm = this.fb.group({
      email: ['', [Validators.required, Validators.email]],
      password: ['', [Validators.required, Validators.minLength(6)]]
    });

    // Setup the debounced submit handler
    this.submitSubject.pipe(
      debounceTime(100),
      takeUntil(this.destroy$)
    ).subscribe(() => this.handleSubmit());
  }

  ngOnInit(){
    this.loading = false;
    this.authService.loading = false;
  }

  googleLogin(event: Event){
    console.log("googleLogin", event);
    this.authService.googleSignIn();
  }

  microsoftLogin(event: Event){
    console.log("microsoftLogin", event);
  }

  togglePasswordVisibility(){
    this.showPassword = !this.showPassword;
  }

  async resetError(){
    this.hasError = false;
    return;
  }

  
  onSubmit = () => {
    this.submitSubject.next();
  }

  private async handleSubmit() {
    try {
      await this.resetError();
      if (this.loginForm.invalid) {
        this.hasError = true;
        throw new Error("Invalid credentials. Try again."); 
      }

      if(this.authService.loading){
        return;
      }
      
      // Assert that email and password are not null or undefined
      const email = this.loginForm.value.email as string;
      const password = this.loginForm.value.password as string;
      const userCredential = await signInWithEmailAndPassword(this.auth, email, password);
      console.log('Logged in:', userCredential.user);
      this.authService.loading = true;
      this.profileService.authUser = userCredential.user;
      this.router.navigate(["/"]);
    } catch (error: any) {
      // Handle authentication errors here
      this.errorMessage = "Invalid credentials. Try again.";
      this.hasError = true;
      this.authService.loading = false;
      // console.error('Login error:', error);
      // Depending on error types, you may want to provide feedback to the user
    }
  }

  async sendResetEmail(){
    try{
      await this.resetError();
      // console.log("Invalid email: ",this.loginForm.get('email'))
      let email = this.loginForm.get('email');
      if(email && !email.errors){
        let reset = await sendPasswordResetEmail(this.auth, email['value'] ? email['value'] : "");
        this.toastService.goodToast("Password Reset Instructions sent to " + email['value']);
      } else {
        this.errorMessage = "Please provide valid email for password reset."
        throw new Error("Please provide valid email for password reset.")
      }
    }catch(error: any){
      this.hasError = true;
      this.errorMessage = error.message; //"Please provide valid email for password reset."
    }
  }
}
