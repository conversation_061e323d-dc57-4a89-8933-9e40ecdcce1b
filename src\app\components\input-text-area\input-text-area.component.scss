@import '../../../_colors.scss';

:host{
    display: block;
    width: 265px;
    height: fit-content;

    textarea{
        appearance: none;
        -webkit-appearance: none;
        -moz-appearance: none;
        border: none;
        outline: none;
        background: none;
        padding: 0;
        margin: 0;
        border-radius: 0;
        font-family: inherit;
        color: inherit;
        box-sizing: border-box;

        background-color: $inputBackground;
        outline: $inputBorder;
        width: 100%;
        max-width: 100%;
        min-width: 100%;
        height: 80px;
        padding: 7px 10px;
        border-radius:10px;
        font-size: 13px;
        color: $appText;

        &:focus{
            outline: $inputFocusBorder;
        }

        &::placeholder{
            color: $placeholderText;
            font-size: .75rem;
            font-weight: 400;
        }
    }
}