@import '../../../_colors';

:host{
    --form-footer-margin-top: 100px;
    --form-footer-margin-bottom: 20px;

    width: 100%;
    display: grid;
    grid-template-columns: auto 1fr auto;
    gap: 20px;
    align-items: center;
    padding: 0 20px;
    margin-bottom: var(--form-footer-margin-bottom);
    margin-top: var(--form-footer-margin-top);


    .back-button{
        grid-column: 1;

        svg{
            margin-right: 12px;
        }
    }

    .next-step-flavor-text{
        grid-column: 2;
        justify-self: right;
        color: #000;
        font-size: 0.75rem;
        font-weight: 500;

        &.error-message{
            color: $red;
        }
    }

    .next-button{
        grid-column: 3;
        width: 170px;

        svg{
            margin-left: 12px;
        }
    }
}
