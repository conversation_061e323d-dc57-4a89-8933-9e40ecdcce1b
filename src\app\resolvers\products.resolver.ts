import { inject } from '@angular/core';
import { ResolveFn } from '@angular/router';
import { ProductsService  } from '../services/products.service';
import { LoadingService } from '../services/loading.service';
import { firstValueFrom } from 'rxjs';
import { finalize } from 'rxjs/operators';

export const productsResolver: ResolveFn<any> = (route, state) => {
  const productsService = inject(ProductsService);
  const loadingService = inject(LoadingService);

  loadingService.setLoading('products', true);

  return firstValueFrom(productsService.products$.pipe(
    finalize(() => loadingService.setLoading('products', false))
  ));
};

export const productDetailsResolver: ResolveFn<any> = (route, state) => {
  const productsService = inject(ProductsService);
  const loadingService = inject(LoadingService);
  loadingService.setLoading('products', true);
  const productId = route.params['id'];
  if(productsService.productsObject.hasOwnProperty(productId)){
    return productsService.productsObject[productId];
  }
  return productsService.getProductDetails(productId).then(product => {
    loadingService.setLoading('products', false);
    return product;
  });
};

export const productQuestionDetailsResolver: ResolveFn<any> = (route, state) => {
  const productsService = inject(ProductsService);
  const loadingService = inject(LoadingService);
  loadingService.setLoading('products', true);
  const productId = route.params['id'];
  return productsService.getProductQuestionDetails(productId).then(questionDetails => {
    loadingService.setLoading('products', false);
    return questionDetails;
  });
};

export const productInsightsResolver: ResolveFn<any> = (route, state) => {
  const productsService = inject(ProductsService);
  const loadingService = inject(LoadingService);
  loadingService.setLoading('products', true);
  const productId = route.params['id'];
  return productsService.getProductInsights(productId).then(insights => {
    loadingService.setLoading('products', false);
    return insights;
  });
};

export const productCommentsResolver: ResolveFn<any> = (route, state) => {
  const productsService = inject(ProductsService);
  const loadingService = inject(LoadingService);
  const productId = route.params['id'];
  loadingService.setLoading('products', true);
  return productsService.getProductComments(productId).then(comments => {
    loadingService.setLoading('products', false);
    return comments;
  });
};

export const productBackButtonResolver: ResolveFn<any> = (route, state) => {
  const productId = route.params['id'];
  const fromQueryParam = route.queryParams['from'];
  const groupId = route.queryParams['groupId'];

  const getBackButtonRoute = () => {
    if(fromQueryParam === 'group'){
      return `/groups/${groupId}`;
    } else {
      return '/products';
    }
  }

  const getBackButtonText = () => {
    if(fromQueryParam === 'group'){
      return 'Back to Group';
    } else {
      return 'Back to Products';
    }
  }

  return {
    backButtonRoute: getBackButtonRoute(),
    backButtonText: getBackButtonText(),
    isFromGroup: fromQueryParam === 'group',
    groupId: groupId
  }
}