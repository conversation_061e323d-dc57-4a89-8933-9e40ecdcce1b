@import '../../../_colors';
:host{
    display: block;
    margin-bottom: 30px;
    box-shadow: 0px 4px 11px 0px rgba(0, 0, 0, 0.10);
    padding: 20px;
    border-radius: 12px;

    input-text,
    input-text-area{
        width: 100%;
    }

    .question-header{
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 30px;

        button.remove-question-button{
            margin-right: 10px;
            opacity: .4;
            cursor: pointer;
            transition: opacity .2s ease-in-out;

            &:hover{
                opacity: 1;
            }
        }

        .question-label{
            // text-transform: capitalize;
            margin-right: auto;
            font-size: .75rem;
            font-weight: 700;
        }

        .required-toggle-container{

            input-radio-standard::ng-deep{

                input{
                    border-radius: 12px;
                    outline: 1px solid #D8D8D8;
                    border-color: #f7f7f7;
                    background: #F7F7F7;
                    width: 15px;
                    height: 15px;

                    &:checked {
                        background-color: $blue;
                    }
            
                    &:focus {
                        outline-color: $blue;
                    }
                }

                label{
                    font-size: .75rem;
                    font-weight: 500;
                }
            }
        }
    }

    .input-container{

        .option-container{
            display: flex;
            gap: 10px;
            align-items: center;
            grid-column: 2;
            position: relative;

            .remove-option-button{
                position: absolute;
                left: -10px;
                opacity: .4;
                transition: opacity .2s ease-in-out;

                &:hover{
                    opacity: 1;
                }
            }

            .option-label{
                border-right: 1px solid #E5E5E5;
                padding: 10px 10px 10px 0;
                font-size: 0.75rem;
                font-weight: 500;
                white-space: nowrap;
                width: 70px;
                text-align: right;
            }

            input-text{
                width: calc(100% - 80px);
            }
        }

        button.add-option-button{
            display: flex;
            cursor: pointer;
            align-items: center;
            grid-column: 2;
            margin-left: 60px;

            .plus-container{
                background-color: #D9D9D9;
                width: 19px;
                height: 19px;
                border-radius: 6px;
                display: flex;
                align-items: center;
                justify-content: center;
                margin-right: 5px;
            }

            .add-option-text{
                font-size: .75rem;
                font-weight: 500;
                color: #5D5D5D;
            }
        }
    }
}