import { Injectable, inject } from '@angular/core';
import { HttpClient, HttpHeaders, HttpParams } from '@angular/common/http';
import { firstValueFrom, Observable, ReplaySubject, take } from 'rxjs';
import { PaginationParams } from '../interfaces/paginationParams';
import { Group } from '../interfaces/group.interface';
import { EnvironmentService } from './environment.service';

@Injectable({
  providedIn: 'root'
})
export class ApiService {

  http = inject(HttpClient);
  private environmentService = inject(EnvironmentService);

  private getHeaders() {
    return new HttpHeaders({
      'Content-Type': 'application/json'
    });
  }

  async getConstants() {
    const headers = new HttpHeaders({
      'Content-Type': 'application/json',
    });
    const baseUrl = this.environmentService.getBaseUrl();
    const effects = await firstValueFrom(this.http.get(`${baseUrl}/constants/effect`, { headers, withCredentials: true }));
    const flavors = await firstValueFrom(this.http.get(`${baseUrl}/constants/flavor`, { headers, withCredentials: true }));
    const metrics = await firstValueFrom(this.http.get(`${baseUrl}/constants/metric`, { headers, withCredentials: true }));
    const questions = await firstValueFrom(this.http.get(`${baseUrl}/constants/question`, { headers, withCredentials: true }));
    const matrix = await firstValueFrom(this.http.get(`${baseUrl}/constants/matrix`, { headers, withCredentials: true }));
    const packaging = await firstValueFrom(this.http.get(`${baseUrl}/constants/packaging`, { headers, withCredentials: true }));
    const consumption = await firstValueFrom(this.http.get(`${baseUrl}/constants/consumption`, { headers, withCredentials: true }));
    const demographics = await firstValueFrom(this.http.get(`${baseUrl}/constants/demographics`, { headers, withCredentials: true }));
    return {effects, flavors, metrics, questions, matrix, packaging, consumption, demographics};
  }

  async getSession(token: string) {
    // console.log('token', token);
    const headers = new HttpHeaders({
      'Content-Type': 'application/json',
      'Authorization': token,
    });
    return await firstValueFrom(this.http.get(`${this.environmentService.getBaseUrl()}/auth/session`, { headers, withCredentials: true }));
  }

  async logout() {
    return await firstValueFrom(this.http.get(`${this.environmentService.getBaseUrl()}/auth/logout`, { withCredentials: true }));
  }

  updateEmail(email: string){
    return this.http.put(`${this.environmentService.getBaseUrl()}/profile/email`, { email }, { withCredentials: true });
  }

  getProfileData(){
    return this.http.get(`${this.environmentService.getBaseUrl()}/profile/me`, { withCredentials: true });
  }

  checkSession(): Observable<boolean> {
    // This should be a very lightweight endpoint that just validates the session
    return this.http.get<boolean>(`${this.environmentService.getBaseUrl()}/auth/session-validity`, {
      // Important: Include credentials to send cookies
      withCredentials: true
    });
  }

  async getProfile() {
    const headers = new HttpHeaders({
      'Content-Type': 'application/json',
    });
    return await firstValueFrom(this.http.get(`${this.environmentService.getBaseUrl()}/profile/me`, { headers, withCredentials: true }));
  }

  async getUsersInOrg() {
    const headers = new HttpHeaders({
      'Content-Type': 'application/json',
    });
    return this.http.get(`${this.environmentService.getBaseUrl()}/users`, { headers, withCredentials: true });
  }


  getUserDetails(id: string) {
    return this.http.get(`${this.environmentService.getBaseUrl()}/users/${id}`, { withCredentials: true });
  }

  getProjectDashboardStats(){
    return this.http.get(`${this.environmentService.getBaseUrl()}/projects/dashboard-stats`, { withCredentials: true });
  }

  getProductTypeDistribution(){
    return this.http.get(`${this.environmentService.getBaseUrl()}/projects/distribution/product-types`, { withCredentials: true });
  }

  deleteProject(id: string) {
    return this.http.delete(`${this.environmentService.getBaseUrl()}/projects/${id}`, { withCredentials: true });
  }

  updateProject(id: string, projectData: any) {
    return this.http.put(`${this.environmentService.getBaseUrl()}/projects/${id}`, projectData, { withCredentials: true });
  }

  addPanelToProject(projectId: string, panelId: string) {
    const headers = this.getHeaders();
    return this.http.post(`${this.environmentService.getBaseUrl()}/projects/${projectId}/panels/${panelId}`, {}, {
      headers,
      withCredentials: true
    });
  }

  removePanelFromProject(projectId: string, panelId: string) {
    const headers = this.getHeaders();
    return this.http.delete(`${this.environmentService.getBaseUrl()}/projects/${projectId}/panels/${panelId}`, {
      headers,
      withCredentials: true
    });
  }

  addCollaboratorToProject(projectId: string, userId: string) {
    const headers = this.getHeaders();
    return this.http.post(`${this.environmentService.getBaseUrl()}/projects/${projectId}/collaborators/${userId}`, {}, {
      headers,
      withCredentials: true
    });
  }

  removeCollaboratorFromProject(projectId: string, userId: string) {
    const headers = this.getHeaders();
    return this.http.delete(`${this.environmentService.getBaseUrl()}/projects/${projectId}/collaborators/${userId}`, {
      headers,
      withCredentials: true
    });
  }

  getProjects(params: PaginationParams) {
    // Convert params object to HttpParams
    const queryParams = new HttpParams()
      .set('page', params.page.toString())
      .set('limit', params.limit.toString())
      .set('sort', params.sort || '')
      .set('filter', params.filter || '')
      .set('include_references', 'true')
      // .set('full_details', 'true');
    // console.log(queryParams);
    return this.http.get(`${this.environmentService.getBaseUrl()}/projects`, {
      headers: this.getHeaders(),
      params: queryParams,
      withCredentials: true
    });
  }

  async getProjectById(id:string, hydrateReferences: boolean = false, hydrateFullDetails: boolean = false){
    const headers = new HttpHeaders({
      'Content-Type': 'application/json',
    });
    return await firstValueFrom(this.http.get(`${this.environmentService.getBaseUrl()}/projects/${id}?include_references=${hydrateReferences ? 'true' : 'false'}&full_details=${hydrateFullDetails ? 'true' : 'false'}`, { headers, withCredentials: true }));
  }

  async getProjectPanels(id: string) {
    const headers = new HttpHeaders({
      'Content-Type': 'application/json',
    });
    return await firstValueFrom(this.http.get(`${this.environmentService.getBaseUrl()}/projects/${id}/panels`, { headers, withCredentials: true }));
  }

  getPanels(params: PaginationParams) {
    // Convert params object to HttpParams
    const queryParams = new HttpParams()
      .set('page', params.page.toString())
      .set('limit', params.limit.toString())
      .set('sort', params.sort || '')
      .set('filter', params.filter || '');
    // console.log(queryParams);
    return this.http.get(`${this.environmentService.getBaseUrl()}/insights/panels`, {
      headers: this.getHeaders(),
      params: queryParams,
      withCredentials: true
    });
  }

  async getPanelInsights(id: string) {
    const headers = new HttpHeaders({
      'Content-Type': 'application/json',
    });
    return await firstValueFrom(this.http.get(`${this.environmentService.getBaseUrl()}/insights/panels/${id}`, { headers, withCredentials: true }));
  }

  async getPanel(id: string) {
    const headers = new HttpHeaders({
      'Content-Type': 'application/json',
    });
    return await firstValueFrom(this.http.get(`${this.environmentService.getBaseUrl()}/panels/${id}?hydrate=true`, { headers, withCredentials: true }));
  }

  async getPanelResponseCount(id: string) {
    const headers = new HttpHeaders({
      'Content-Type': 'application/json',
    });
    return await firstValueFrom(this.http.get(`${this.environmentService.getBaseUrl()}/insights/panels/count/${id}`, { headers, withCredentials: true }));
  }

  async savePanel(panel: any) {
    const headers = new HttpHeaders({
      'Content-Type': 'application/json',
    });
  return await firstValueFrom(this.http.post(`${this.environmentService.getBaseUrl()}/panels`, panel, { headers, withCredentials: true }));
  }

  async updatePanel(panel: any, id: string) {
    const headers = new HttpHeaders({
      'Content-Type': 'application/json',
    });
    return await firstValueFrom(this.http.put(`${this.environmentService.getBaseUrl()}/panels/${id}`, panel, { headers, withCredentials: true }));
  }

  getProducts(params: PaginationParams) {
    // Convert params object to HttpParams
    const queryParams = new HttpParams()
      .set('page', params.page.toString())
      .set('limit', params.limit.toString())
      .set('sort', params.sort || '')
      .set('filter', params.filter || '');
    // console.log(queryParams);
    return this.http.get(`${this.environmentService.getBaseUrl()}/insights/products`, {
      headers: this.getHeaders(),
      params: queryParams,
      withCredentials: true
    });
  }

  // For caching paginated results, we can use a Map with the query string as key
  private productCache = new Map<string, ReplaySubject<any>>();

  getProductsCached(params: PaginationParams) {
    // Create a cache key from the params
    const cacheKey = this.createCacheKey(params);

    if (!this.productCache.has(cacheKey)) {
      this.productCache.set(cacheKey, new ReplaySubject<any>(1));

      const queryParams = new HttpParams()
        .set('page', params.page.toString())
        .set('limit', params.limit.toString())
        .set('sort', params.sort || '')
        .set('filter', params.filter || '');

      this.http.get(`${this.environmentService.getBaseUrl()}/products`, {
        headers: this.getHeaders(),
        params: queryParams,
        withCredentials: true
      }).pipe(
        take(1)
      ).subscribe(data => {
        this.productCache.get(cacheKey)?.next(data);
      });
    }

    return this.productCache.get(cacheKey)!.asObservable();
  }

  private createCacheKey(params: PaginationParams): string {
    return Object.entries(params)
      .filter(([_, value]) => value !== undefined)
      .sort(([a], [b]) => a.localeCompare(b))
      .map(([key, value]) => `${key}:${value}`)
      .join('|');
  }


  async getPanelCount(id: string) {
    const headers = new HttpHeaders({
      'Content-Type': 'application/json',
    });
    return await firstValueFrom(this.http.get(`${this.environmentService.getBaseUrl()}/insights/count/panels-using-product/${id}`, { headers, withCredentials: true }));
  }

  async getProductComments(id: string) {
    const headers = new HttpHeaders({
      'Content-Type': 'application/json',
    });
    return await firstValueFrom(this.http.get(`${this.environmentService.getBaseUrl()}/insights/products/comments/${id}`, { headers, withCredentials: true }));
  }

  async getPanelComments(id: string) {
    const headers = new HttpHeaders({
      'Content-Type': 'application/json',
    });
    return await firstValueFrom(this.http.get(`${this.environmentService.getBaseUrl()}/insights/panels/comments/${id}`, { headers, withCredentials: true }));
  }

  /**
   * Get triangle test aggregate data for a panel
   * @param id The panel ID
   * @returns Triangle test aggregate data including chi-squared statistics
   */
  async getTriangleTestAggregates(id: string) {
    const headers = new HttpHeaders({
      'Content-Type': 'application/json',
    });
    return await firstValueFrom(
      this.http.get(
        `${this.environmentService.getBaseUrl()}/insights/panels/${id}/triangle-test/aggregates`, 
        { headers, withCredentials: true }
      )
    );
  }

  async getProductQuestionDetails(id: string){
    const headers = new HttpHeaders({
      'Content-Type': 'application/json',
    });
    return await firstValueFrom(this.http.get(`${this.environmentService.getBaseUrl()}/insights/products/panels/${id}?hydrate=true`, { headers, withCredentials: true }));
  }

  // Method to clear cache for specific params or all cache
  clearProductCache(params?: PaginationParams) {
    if (params) {
      const cacheKey = this.createCacheKey(params);
      this.productCache.delete(cacheKey);
    } else {
      this.productCache.clear();
    }
  }

  async getProductDetails(id: string) {
    const headers = new HttpHeaders({
      'Content-Type': 'application/json',
    });
    return await firstValueFrom(this.http.get(`${this.environmentService.getBaseUrl()}/products/${id}`, { headers, withCredentials: true }));
  }

  async getProductInsights(id: string) {
    const headers = new HttpHeaders({
      'Content-Type': 'application/json',
    });
    return await firstValueFrom(this.http.get(`${this.environmentService.getBaseUrl()}/insights/products/${id}`, { headers, withCredentials: true }));
  }

  async getResponseCount(id: string, entityType: string) {
    const headers = new HttpHeaders({
      'Content-Type': 'application/json',
    });
    return await firstValueFrom(this.http.get(`${this.environmentService.getBaseUrl()}/insights/${entityType}/count/${id}`, { headers, withCredentials: true }));
  }

  async saveProduct(product: any) {
    const headers = new HttpHeaders({
      'Content-Type': 'application/json',
    });
    return await firstValueFrom(this.http.post(`${this.environmentService.getBaseUrl()}/products`, product, { headers, withCredentials: true }));
  }

  async updateProduct(product: any, id: string) {
    const headers = new HttpHeaders({
      'Content-Type': 'application/json',
    });
    return await firstValueFrom(this.http.put(`${this.environmentService.getBaseUrl()}/products/${id}`, product, { headers, withCredentials: true }));
  }

  getGroups(params: PaginationParams = { page: 1, limit: 20 }, includeReferences: boolean = false) {
    const queryParams = new HttpParams()
      .set('page', params.page.toString())
      .set('limit', params.limit.toString())
      .set('sort', params.sort || '')
      .set('filter', params.filter || '')
      .set('includeReferences', includeReferences ? 'true' : 'false');
    return this.http.get(`${this.environmentService.getBaseUrl()}/groups`, { headers: this.getHeaders(), params: queryParams, withCredentials: true });

  }

 async getGroupsAsync(params: PaginationParams = { page: 1, limit: 20 }, includeReferences: boolean = false) {
    const queryParams = new HttpParams()
      .set('page', params.page.toString())
      .set('limit', params.limit.toString())
      .set('sort', params.sort || '')
      .set('filter', params.filter || '')
      .set('includeReferences', includeReferences ? 'true' : 'false');
    return this.http.get(`${this.environmentService.getBaseUrl()}/groups`, { headers: this.getHeaders(), params: queryParams, withCredentials: true });

  }

  getGroupById(id:string, includeReferences: boolean = false):Observable<any>{
    return this.http.get(`${this.environmentService.getBaseUrl()}/groups/${id}?includeReferences=${includeReferences ? 'true' : 'false'}`, { withCredentials: true });
  }

  async createGroup(body:Group) {
    return await this.http.post(`${this.environmentService.getBaseUrl()}/groups`, body, { withCredentials: true });
  }

  async updateGroup(id:string, body:Group) {
    return await this.http.put(`${this.environmentService.getBaseUrl()}/groups/${id}`, body, { withCredentials: true });
  }

   deletePanel(id:string) {
    return  this.http.delete(`${this.environmentService.getBaseUrl()}/groups/${id}`, { withCredentials: true });
  }

  getGroupDetailData(id:string){
    return this.http.get(`${this.environmentService.getBaseUrl()}/groups/${id}`, { withCredentials: true });
  }

  async createProject(projectData: any) {
    const headers = new HttpHeaders({
      'Content-Type': 'application/json',
    });

    // console.log('projectData', projectData);

    try {
      // Ensure the project data format matches what the API expects
      const apiProjectData: Record<string, any> = {
        name: projectData.name,
        description: projectData.description || '',
        organization_id: projectData.organization_id,
        status: projectData.status || 'draft',
        start_date: projectData.start_date,
        end_date: projectData.end_date,
        is_public: projectData.is_public || false
      };

      // Add arrays only if they exist and have items
      if (projectData.assigned_panels && projectData.assigned_panels.length > 0) {
        apiProjectData['assigned_panels'] = projectData.assigned_panels;
      } else if (projectData.panels && projectData.panels.length > 0) {
        // Fallback for backward compatibility
        apiProjectData['assigned_panels'] = projectData.panels;
      }

      if (projectData.assigned_samples && projectData.assigned_samples.length > 0) {
        apiProjectData['assigned_samples'] = projectData.assigned_samples;
      } else if (projectData.samples && projectData.samples.length > 0) {
        // Fallback for backward compatibility
        apiProjectData['assigned_samples'] = projectData.samples;
      }

      if (projectData.collaborators && projectData.collaborators.length > 0) {
        apiProjectData['collaborators'] = projectData.collaborators;
      }

      if (projectData.assigned_groups) {
        apiProjectData['assigned_groups'] = projectData.assigned_groups;
      }

      // console.log('Creating project with data:', apiProjectData);
      return this.http.post(`${this.environmentService.getBaseUrl()}/projects`, apiProjectData, {
        headers,
        withCredentials: true
      });
    } catch (error) {
      // console.error('Error in createProject API call:', error);
      throw error;
    }
  }

  generatePDS(url: string, params: HttpParams) {
    return this.http.get(url, { params, responseType: 'blob' });
  }

  getTemplates(params: PaginationParams = { page: 1, limit: 10 }){
    const headers = new HttpHeaders({
      'Content-Type': 'application/json',
    });

    const queryParams = new HttpParams()
      .set('page', params.page.toString())
      .set('limit', params.limit.toString())
      .set('sort', params.sort || '')
      .set('filter', params.filter || '');

    return this.http.get(`${this.environmentService.getBaseUrl()}/templates`, {
      headers,
      params: queryParams,
      withCredentials: true
    });
  }

  async getTemplateById(id: string) {
    const headers = new HttpHeaders({
      'Content-Type': 'application/json',
    });
    return await firstValueFrom(this.http.get(`${this.environmentService.getBaseUrl()}/templates/${id}`, { headers, withCredentials: true }));
  }

  updateTemplate(id:string,templateData:any){
    const headers = new HttpHeaders({
      'Content-Type': 'application/json',
    });
    return this.http.put(`${this.environmentService.getBaseUrl()}/templates/${id}`, templateData, { headers, withCredentials: true });
  }

  deleteTemplate(id:string){
    return this.http.delete(`${this.environmentService.getBaseUrl()}/templates/${id}`, { withCredentials: true });
  }

  createTemplate(templateData:any){
    console.log(templateData);
    const headers = new HttpHeaders({
      'Content-Type': 'application/json',
    });
    return this.http.post(`${this.environmentService.getBaseUrl()}/templates`, templateData, { headers, withCredentials: true });
  }

  getQuestionById(id: string): Observable<any> {
    const headers = this.getHeaders();
    return this.http.get(`${this.environmentService.getBaseUrl()}/questions/${id}`, {
      headers,
      withCredentials: true
    });
  }

  getOrgQuestions() {
    return this.http.get(`${this.environmentService.getBaseUrl()}/questions`, { withCredentials: true });
  }

  createQuestion(payload: any) {
    const headers = this.getHeaders();
    return this.http.post(`${this.environmentService.getBaseUrl()}/questions`, payload, { headers, withCredentials: true });
  }
}
