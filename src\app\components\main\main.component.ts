import { Component, inject } from '@angular/core';
import { NgIf } from '@angular/common';
import { RouterOutlet } from '@angular/router';
import { NavComponent } from '../nav/nav.component';
import { FooterComponent } from '../footer/footer.component';
import { PopupService } from '../../services/popup.service';
import { NewPanelFormComponent } from '../new-panel-form/new-panel-form.component';
import { NewProductFormComponent } from '../new-product-form/new-product-form.component';
import { slideUpAnimation } from '../../modules/animations/animations.module';
import { PopupOverlayComponent } from '../popup-overlay/popup-overlay.component';
import { PanelDetailsComponent } from '../panel-details/panel-details.component';
import { ProductDetailsComponent } from '../product-details/product-details.component';
import { NewGroupFormComponent } from '../new-group-form/new-group-form.component';
import { NewProjectFormComponent } from '../new-project-form/new-project-form.component';
import { NewTemplateFormComponent } from '../new-template-form/new-template-form.component';
import { CustomStepFormComponent } from "../custom-step-form/custom-step-form.component";
// import { ProfileService } from '../../services/profile.service';

@Component({
  selector: 'main',
  standalone: true,
  imports: [RouterOutlet,
    NavComponent,
    FooterComponent,
    NgIf,
    NewPanelFormComponent,
    NewProductFormComponent,
    PopupOverlayComponent,
    PanelDetailsComponent,
    ProductDetailsComponent,
    NewGroupFormComponent,
    NewProjectFormComponent, 
    NewTemplateFormComponent,
    CustomStepFormComponent],
  templateUrl: './main.component.html',
  styleUrl: './main.component.scss',
  animations: [slideUpAnimation]
})
export class MainComponent {

  popupService = inject(PopupService);

  // profileService = inject(ProfileService);
  ngOnInit(){
    // this.profileService.checkDemographics();
  }


}
