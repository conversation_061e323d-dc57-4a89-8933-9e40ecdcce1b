import { Component, inject, ElementRef } from '@angular/core';
import { NgIf } from '@angular/common';
import { PopupService } from '../../services/popup.service';
import { TemplateFormService } from '../../services/template-form.service';
import { FormHeaderComponent } from '../form-header/form-header.component';
import { NewTemplateStep1Component } from '../new-template-step-1/new-template-step-1.component';
import { NewTemplateStep3Component } from '../new-template-step-3/new-template-step-3.component';
import { NewTemplateStep4Component } from '../new-template-step-4/new-template-step-4.component';
import { NewTemplateStep5Component } from '../new-template-step-5/new-template-step-5.component';
import { NewTemplateStep6Component } from '../new-template-step-6/new-template-step-6.component';
import { FormFooterComponent } from '../form-footer/form-footer.component';
import { ButtonTwoClickComponent } from '../button-two-click/button-two-click.component';
import { Subscription } from 'rxjs';

@Component({
  selector: 'new-template-form',
  standalone: true,
  imports: [
    FormHeaderComponent,
    NewTemplateStep1Component,
    NewTemplateStep3Component,
    NewTemplateStep4Component,
    NewTemplateStep5Component,
    NewTemplateStep6Component,
    FormFooterComponent,
    NgIf,
    ButtonTwoClickComponent
  ],
  templateUrl: './new-template-form.component.html',
  styleUrl: './new-template-form.component.scss'
})
export class NewTemplateFormComponent {
  private elementRef = inject(ElementRef);
  popupService = inject(PopupService);
  templateFormService = inject(TemplateFormService);

  subscription: Subscription = new Subscription();

  ngOnInit() {
    this.subscription = this.templateFormService.scrollResetSubject.subscribe(() => {
      this.elementRef.nativeElement.scrollTop = 0;
    });
  }

  ngOnDestroy() {
    // Clean up subscription to prevent memory leaks
    this.subscription.unsubscribe();
  }

  close() {
    this.popupService.closeNewTemplate();
  }
}
