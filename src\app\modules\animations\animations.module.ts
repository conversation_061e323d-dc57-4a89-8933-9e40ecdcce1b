// import { NgModule, } from '@angular/core';
// import { CommonModule } from '@angular/common';
import { animate, style, transition, trigger, state } from '@angular/animations';



// @NgModule({
//   declarations: [],
//   imports: [
//     CommonModule
//   ]
// })
// export class AnimationsModule { }

export const fadeShrink = trigger('fadeShrink', [
  transition(':enter', [
    style({ opacity: 0, width: 0 }),
    animate('150ms ease-out', style({ opacity: 1, width: '*' }))
  ]),
  transition(':leave', [
    style({ opacity: 1, width: '*' }),
    animate('150ms ease-in', style({ opacity: 0, width: 0 }))
  ])
]);


export const toastSlide = trigger('toastSlide', [
  transition(':enter', [
    style({ transform: 'translateY(-100%)' }),
    animate('300ms ease-out', style({ transform: 'translateY(30px)' })),
  ]),
  transition(':leave', [
    animate('300ms 3s', style({ transform: 'translateY(-100%)' }))
  ])
]);

export const slideUpAnimation = trigger('slideUp', [
  transition(':enter', [
    style({ transform: 'translateY(100%)' }),
    animate('300ms ease-out', style({ transform: 'translateY(0%)' }))
  ]),
  transition(':leave', [
    animate('300ms ease-in', style({ transform: 'translateY(100%)' }))
  ])
]);

export const fadeScaleInOut = trigger('fadeScale', [
  transition(':enter', [
    style({ opacity: 0, transform: 'scale(0.9)' }),
    animate('100ms ease-out', style({ opacity: 1, transform: 'scale(1)' }))
  ]),
  transition(':leave', [
    animate('100ms ease-in', style({ opacity: 0, transform: 'scale(0.95)' }))
  ])
]);


export const tabFade = trigger('tabFade', [
  transition(':enter', [
    style({ opacity: 0}),
    animate('150ms 150ms', style({ opacity: 1}))
  ]),
  transition(':leave', [
    style({ opacity: 1}),
    animate('150ms 0ms', style({ opacity: 0}))
  ])
]);

// export const tabFade = trigger('tabFade', [
//   transition('* <=> *', [
//     style({ opacity: 0 }),
//     animate('300ms ease-in', style({ opacity: 1 }))
//   ])
// ]);



// export const tabSlideAnimation = trigger('tabSlide', [
//   transition('* => right', [
//     style({ transform: 'translateX(-100%)' }),
//     animate('300ms ease-out', style({ transform: 'translateX(0%)' }))
//   ]),
//   transition('* => left', [
//     style({ transform: 'translateX(100%)' }),
//     animate('300ms ease-out', style({ transform: 'translateX(0%)' }))
//   ]),
//   transition('left => *', [
//     animate('300ms ease-out', style({ transform: 'translateX(-100%)' }))
//   ]),
//   transition('right => *', [
//     animate('300ms ease-out', style({ transform: 'translateX(100%)' }))
//   ]),

//   // transition('void => left', [
//   //   style({ transform: 'translateX(100%)' }),
//   //   animate('300ms ease-out', style({ transform: 'translateX(0%)' }))
//   // ]),
//   // transition('void => right', [
//   //   style({ transform: 'translateX(-100%)' }),
//   //   animate('300ms ease-out', style({ transform: 'translateX(0%)' }))
//   // ]),
//   transition('right => void', [
//     animate('300ms ease-in', style({ transform: 'translateX(-100%)' }))
//   ]),
//   transition('left => void', [
//     animate('300ms ease-in', style({ transform: 'translateX(100%)' }))
//   ]),
//   // transition('right => left', [
//   //   animate('300ms ease-in', style({ transform: 'translateX(100%)' }))
//   // ]),
//   // transition('left => right', [
//   //   animate('300ms ease-in', style({ transform: 'translateX(-100%)' }))
//   // ]),

// ]);

export const fadeInOut = trigger('fadeInOut', [
  transition(':enter', [
    style({ opacity: 0 }),
    animate('300ms 0ms', style({ opacity: 1 }))
  ]),
  transition(':leave', [
    style({ opacity: 1 }),
    animate('300ms 0ms', style({ opacity: 0 }))
  ])
]);

export const fadeIn = trigger('fadeIn', [
  transition(':enter', [
    style({ opacity: 0 }),
    animate('300ms 0ms', style({ opacity: 1 }))
  ])
]);

export const slideInOut = trigger('slideInOut', [
  transition(':enter', [
    style({ transform: 'translateX(100%)' }),
    animate('300ms ease-out', style({ transform: 'translateX(0%)' }))
  ]),
  transition(':leave', [
    animate('300ms ease-in', style({ transform: 'translateX(100%)' }))
  ])
]);

export const buttonTextSlide = trigger('buttonTextSlide', [
  transition(':enter', [
    style({ opacity: 0, transform: 'translateY(100%)' }),
    animate('150ms ease-out', style({ opacity: 1, transform: 'translateY(0)' }))
  ]),
  transition(':leave', [
    animate('150ms ease-out', style({ opacity: 0, transform: 'translateY(-100%)' }))
  ])
]);


