import { Component, inject, Input } from '@angular/core';
import { <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, NgI<PERSON> } from '@angular/common';
// import { createTableConfig } from '../../config/table-styles.config';
// import {
//   TableConfig,
//   TableComponent,
// } from '@lamb-sensory/lamb-component-library';

import { TABLE_STYLES } from '../../config/table-styles.config';
import { LambTableComponent, HeaderCell } from '@lamb-sensory/lamb-component-library';
import { ProfileService } from '../../services/profile.service';
import { Subscription } from 'rxjs';

@Component({
  selector: 'users-table',
  standalone: true,
  imports: [NgFor, NgIf, LambTableComponent],
  templateUrl: './users-table.component.html',
  styleUrl: './users-table.component.scss',
})
export class UsersTableComponent {
  users: any = [];
  // tableConfig!: TableConfig;
  tableStyles = structuredClone(TABLE_STYLES);
  tableHeaders: HeaderCell[] = [
    {
      value: 'Name',
      key: 'name',
    },
    {
      value: 'Email Address',
      key: 'email',
    },
    {
      value: 'Access Level',
      key: 'role',
    },
  ];
  tableData: any[] = [];
  profileService = inject(ProfileService);
  subscription: Subscription=new Subscription();

  getTableData() {
    const tableData: any[] = this.users.map((user: any) => {
      return {
        name: user.first_name + ' ' + user.last_name,
        email: user.email,
        role: this.roleDisplay(user.roles),
      }
    });

    return tableData;
  }

  roleDisplay(roles: string[]) {
    return roles
      .map(
        (role) => String(role).charAt(0).toUpperCase() + String(role).slice(1)
      )
      .join(', ');
  }

  onRowClick(event: any) {
    console.log(event);
  }

  ngOnInit() {
    this.profileService.getUsersListInMyOrg().then((users) => {
      this.subscription = users.subscribe((users) => {
        this.users = users;
        console.log('users', this.users);
        this.tableData = this.getTableData();
      });
    });
  }

  ngOnDestroy() {
    this.subscription.unsubscribe();
  }
}
