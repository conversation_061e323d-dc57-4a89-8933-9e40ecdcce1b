import { Component, Input } from '@angular/core';
import { Ng<PERSON><PERSON> } from '@angular/common';
import { FormControl, ReactiveFormsModule } from '@angular/forms';
import { InputRadioStandardComponent } from '../input-radio-standard/input-radio-standard.component';

@Component({
  selector: 'custom-step-type-radio',
  standalone: true,
  imports: [ReactiveFormsModule, InputRadioStandardComponent, NgFor],
  templateUrl: './custom-step-type-radio.component.html',
  styleUrl: './custom-step-type-radio.component.scss'
})
export class CustomStepTypeRadioComponent {

  @Input() id: string = '';
  @Input() control!: FormControl;
  @Input() options: any[] = [];
}
