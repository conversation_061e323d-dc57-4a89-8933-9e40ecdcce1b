:host{
    display: block;
    --input-container-label-width: 150px;

    form{

        .input-container{
            grid-template-columns: var(--input-container-label-width) auto auto;

            .nested-input-container{
                display: flex;
                align-items: center;
                gap: 20px;

                &.custom{
                    gap: 0;
                } 

                input-radio-block{
                    width: 39px;
                    height: 37px;
                    border-radius: 14px;

                    &.custom{
                        width: fit-content;
                    }
                }
            }
        }

        .spend-preview-container {
            margin-top: 40px;

            .spend-preview-header {
                font-size: 14px;
                color: #666;
                margin-bottom: 20px;
            }

            .spend-preview-body {
                display: grid;
                grid-template-columns: auto auto auto auto auto;
                align-items: center;
                gap: 20px;
                padding: 20px;
                background: #F5F5F5;
                border-radius: 14px;

                .spend-preview-body-item {
                    display: flex;
                    flex-direction: column;
                    gap: 8px;
                    align-items: center;
                    justify-content: center;

                    &-label {
                        font-size: 12px;
                        color: #666;
                    }

                    &-value {
                        font-size: 16px;
                        font-weight: 500;
                    }

                    svg {
                        margin: 12px 0;
                    }
                }
            }
        }
    }

}