<div class="form-step-container">
    <form [formGroup]="form">
        <div class="form-step-title">Start by entering the product details below</div>

        <div class="form-wrap">
            <div class="form-third">
                <div class="input-container" [class.error-shake]="productsService.formErrors['step1']['image']">
                    <label for="product-image">Product Image</label>
                    <input-img [control]="imageControl" [id]="'product-image'"></input-img>
                </div>
            </div>

            <div class="form-two-thirds">
                <div class="input-container" [class.error-shake]="productsService.formErrors['step1']['name']">
                    <label for="product-name">Product Name</label>
                    <input-text [control]="nameControl" [id]="'product-name'" [placeholder]="'product name'"></input-text>
                </div>

                <div class="input-container" [class.error-shake]="productsService.formErrors['step1']['description']">
                    <label for="product-description">Product Description</label>
                    <input-text-area [control]="descriptionControl" [id]="'product-description'" [placeholder]="'product description'"></input-text-area>
                </div>

                <div class="input-container">
                    <label for="product-code">Product Code<span class="optional">OPTIONAL UNIQUE ID</span></label>
                    <input-text [control]="codeControl" [id]="'product-code'" [placeholder]="'product code'"></input-text>
                </div>

                <div class="input-container" [class.error-shake]="productsService.formErrors['step1']['type']">
                    <label for="product-price">Product Type</label>
                    <input-select [control]="typeControl" [id]="'product-type'" [options]="matrixOptions"></input-select>
                </div>

        <!-- <div class="input-container" [class.error-shake]="productsService.formErrors['step1']['subtype']">
            <label for="product-price">Product Subtype</label>
            <input-select [control]="subtypeControl" [id]="'product-subtype'" [options]="['one-time', 'recurring']"></input-select>
        </div> -->
            </div>
        </div>
    </form>
</div>