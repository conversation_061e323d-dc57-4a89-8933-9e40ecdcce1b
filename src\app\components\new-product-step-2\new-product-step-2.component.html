<div class="form-step-container">
    <form [formGroup]="form">
        <div class="form-step-title">Continue entering product details</div>

        <div class="input-container" [class.error-shake]="productsService.formErrors['step2']['packaging']">
            <label for="product-packaging">Packaging Container</label>
            <input-select [control]="packagingControl" [id]="'product-packaging'" [options]="packagingOptions"></input-select>
        </div>

        <div class="input-container" [class.error-shake]="productsService.formErrors['step2']['lotNumber']">
            <label for="product-lot-number">Lot Number</label>
            <input-text [control]="lotNumberControl" [id]="'product-lot-number'" [placeholder]="'product lot number'"></input-text>
        </div>

        <div class="input-container" [class.error-shake]="productsService.formErrors['step2']['manuDate']">
            <label for="product-manu-date">Manufacture Date</label>
            <input-bs-datepicker [control]="manuDateControl" [id]="'product-manu-date'"></input-bs-datepicker>
        </div>

        <div class="input-container" [class.error-shake]="productsService.formErrors['step2']['packageDate']">
            <label for="product-package-date">Package Date</label>
            <input-bs-datepicker [control]="packageDateControl" [id]="'product-package-date'"></input-bs-datepicker>
        </div>

        <div class="input-container" [class.error-shake]="productsService.formErrors['step2']['producer']">
            <label for="product-producer">Producer</label>
            <input-text [control]="producerControl" [id]="'product-producer'" [placeholder]="'product producer'"></input-text>
        </div>

        <div class="input-container">
            <label for="product-notes">Notes<span class="optional">OPTIONAL</span></label>
            <input-text-area [control]="notesControl" [id]="'product-notes'" [placeholder]="'product notes'"></input-text-area>
        </div>
    </form>
</div>