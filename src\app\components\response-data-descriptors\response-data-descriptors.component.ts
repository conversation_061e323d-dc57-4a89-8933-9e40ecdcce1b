import { Component, Input, OnInit, inject } from '@angular/core';
import { CommonModule, NgIf } from '@angular/common';
import { LambTableComponent, TableStyles, HeaderCell } from '@lamb-sensory/lamb-component-library';
import { ResponseDataTopDescriptorsComponent } from '../response-data-top-descriptors/response-data-top-descriptors.component';
import { PanelsService } from '../../services/panels.service';
import { ProductsService } from '../../services/products.service';
import { StatisticsService } from '../../services/statistics.service';
import { TABLE_STYLES } from '../../config/table-styles.config';
import { StepTypeIconComponent } from '../step-type-icon/step-type-icon.component';
// import { createTableConfig } from '../../config/table-styles.config';

@Component({
  selector: 'response-data-descriptors',
  standalone: true,
  imports: [
    CommonModule,
    NgIf,
    LambTableComponent,
    ResponseDataTopDescriptorsComponent,
    StepTypeIconComponent
  ],
  templateUrl: './response-data-descriptors.component.html',
  styleUrl: './response-data-descriptors.component.scss'
})
export class ResponseDataDescriptorsComponent implements OnInit {
  @Input() responseDetails: any;
  @Input() service_type: string = 'product';
  
  activeTab: string = 'top';
  // tableConfig: any;
  
  panelsService = inject(PanelsService);
  statisticsService = inject(StatisticsService);
  productsService = inject(ProductsService);

  headerData: HeaderCell[] = [];
  bodyData: any[] = [];
  tableStyles = structuredClone(TABLE_STYLES);

  service_to_use: any;
  ngOnInit() {
    // console.log('responseDetails', this.responseDetails);
    
    this.configureTable(this.responseDetails);
    if(this.service_type === 'product'){
      this.service_to_use = this.productsService;
    } else {
      this.service_to_use = this.panelsService;
    }
  }
  
  setActiveTab(tab: string): void {
    this.activeTab = tab;
  }
  
  onRowClick(event: any) {
    console.log(event);
  }
  
  getTableHeaders() {
    return [
      {
        value: 'RANK',
        key: 'rank'
      },
      {
        value: 'NAME',
        key: 'name'
      },
      {
        value: 'AVG. VALUE',
        key: 'avg'
      }
    ];
  }
  
  async getDescriptors(data: any) {
    let descriptors = await this.panelsService.getResponseDescriptors(data);
    let csArr = descriptors.map(cs => {
      cs['stat_count'] = cs.count;
      cs['stat_sum'] = cs.sum;

      const descStat = this.statisticsService.calculatedStats[cs.descriptor.option_id];

      return { stat: descStat, descriptor: { ...cs.descriptor } };
    });

    return csArr.sort((a: any, b: any) => {
      return b.stat.shrinkageEstimate - a.stat.shrinkageEstimate;
    });
  }
  
  async getTableData(data: any) {
    let descriptorsWithStat = await this.getDescriptors(data);
    const tableData: any[] = descriptorsWithStat.map((des: { stat: any, descriptor: any }, i: number) => {
      return {
        rank: i + 1,
        name: des.descriptor.name,
        avg: des.stat.shrinkageEstimate.toFixed(2)
      }
      // return [
      //   {
      //     value: i + 1,
      //     key: 'rank'
      //   },
      //   {
      //     value: des.descriptor.name,
      //     key: 'name'
      //   },
      //   {
      //     value: des.stat.shrinkageEstimate.toFixed(2),
      //     key: 'avg'
      //   }
      // ];
    });

    return tableData;
  }
  
  async configureTable(data: any) {
    this.headerData = this.getTableHeaders();
    this.bodyData = await this.getTableData(data);
    // this.tableConfig = createTableConfig(headerData, bodyData);
  }
}
