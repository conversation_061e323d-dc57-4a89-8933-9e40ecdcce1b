import { Injectable, inject } from '@angular/core';
import { User } from '@angular/fire/auth';
import { ApiService } from './api.service';
import { HttpClient } from '@angular/common/http';
import { EnvironmentService } from './environment.service';

@Injectable({
  providedIn: 'root'
})
export class ProfileService {

  authUser: User | null = null;
  
  profileData: any;
  http = inject(HttpClient);
  private environmentService = inject(EnvironmentService);

  apiService = inject(ApiService);


  async getProfile(){
    const profile = await this.apiService.getProfile();
    console.log(profile);
  }

  async getUsersListInMyOrg(){
   return await this.apiService.getUsersInOrg();
  }

  getInitials() {
    return this.profileData?.first_name?.charAt(0) + this.profileData?.last_name?.charAt(0) || '';
  }

  updateEmail(email: string){
    return this.http.put(`${this.environmentService.getBaseUrl()}/profile/email`, { email }, { withCredentials: true });
  }
}
