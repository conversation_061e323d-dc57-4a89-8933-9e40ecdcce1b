import { Component, Input, inject } from '@angular/core';
import { NgIf } from '@angular/common';
import { ButtonStandardComponent } from '../button-standard/button-standard.component';
import { PopupService } from '../../services/popup.service';
import { DigitToWordPipe } from '../../pipes/digit-to-word.pipe';

@Component({
  selector: 'form-footer',
  standalone: true,
  imports: [ButtonStandardComponent, NgIf, DigitToWordPipe],
  templateUrl: './form-footer.component.html',
  styleUrl: './form-footer.component.scss'
})
export class FormFooterComponent {

  @Input() service: any = null;
  popupService = inject(PopupService);

  save(){
    try{
      this.service.save();
      this.popupService.closeNewPanel();
      this.popupService.closeNewProduct();
      this.popupService.closeNewGroup();
      this.popupService.closeNewProject();
      this.popupService.closeNewTemplate();
    } catch(error){
      console.log('error to handle here');
      console.log('error', error);
    } 
    
  }
}
