import { Component, Input, inject, ElementRef, ViewChild, AfterViewInit } from '@angular/core';
import { PanelsService } from '../../services/panels.service';
import { CommonModule } from '@angular/common';
import { Chart, LogarithmicScale } from 'chart.js/auto';
import { LoadingService } from '../../services/loading.service';
import { ConstantsService } from '../../services/constants.service';
import { StatisticsService } from '../../services/statistics.service';
import { StepTypeIconComponent } from '../step-type-icon/step-type-icon.component';

@Component({
  selector: 'response-data-aroma-category-distribution',
  standalone: true,
  imports: [CommonModule, StepTypeIconComponent],
  templateUrl: './response-data-aroma-category-distribution.component.html',
  styleUrl: './response-data-aroma-category-distribution.component.scss'
})
export class ResponseDataAromaCategoryDistributionComponent implements AfterViewInit {
  @ViewChild('chartCanvas') chartCanvas!: ElementRef<HTMLCanvasElement>;
  @Input() responseDetails: any;
  @Input() csvFileName: string = 'aroma-distribution';
  aromaCategories: any[] = [];
  normalizedAromaCategories: any[] = [];
  topThreeCategories: any[] = [];
  panelsService = inject(PanelsService);
  chart: Chart | undefined;
  loadingService = inject(LoadingService);
  constantsService = inject(ConstantsService);
  statisticsService = inject(StatisticsService);

  spectrumPosition: any = 0;

  async ngOnInit() {
    //await this.getAromaCategoriesByCount();
    await this.calculateCategoryDistribution();
    this.spectrumPosition = await this.getSpectrumPosition();
  }

  ngAfterViewInit() {
    this.createChart();
  }

  getCategoryValue(category: string) {
    return this.normalizedAromaCategories.find(cat => cat.category === category)?.percentage || 0;
  }

  exportDistributionCSV() {
    this.loadingService.setLoading('panels', true);
    let csvContent = "data:text/csv;charset=utf-8,";
    csvContent += this.constantsService.flavorCategories.join(",");
    csvContent += ",spectrum_position";
    csvContent += "\n";
    csvContent += this.constantsService.flavorCategories.map(cat => {return Math.round(this.getCategoryValue(cat))}).join(",");
    csvContent += "," + this.spectrumPosition;
    csvContent += "\n";
    const encodedUri = encodeURI(csvContent);
    // window.open(encodedUri); // Method a: opens in a new tab and starts the download - i don't like this, it's not as smooth for the user
    // Method b: creates a link in the DOM, clicks it, and starts the download, then removes the link from the DOM - I like this better, it feels better to me
    const link = document.createElement("a");
    link.setAttribute("href", encodedUri);
    link.setAttribute("download", `${this.csvFileName}.csv`);
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    setTimeout(() => {
      this.loadingService.setLoading('panels', false);
    }, 500);
  }

  getTopNote(descriptors: any[], varName: string = 'count') {
    let topNote = descriptors.sort((a: any, b: any) => {
      return b[varName] - a[varName];
    })[0].descriptor.name;
    return topNote;
  }

  async getSpectrumPosition() {
    // Get sorted flavors by priority
    let sorted_flavors = this.constantsService.flavors.sort((a: any, b: any) => {
      return a['priority'] - b['priority'];
    }).map((flavor: any) => {
      return flavor.option_id;
    });
    
    // Calculate the sensory position
    this.spectrumPosition = await this.calculateSensoryPosition(sorted_flavors);
    // console.log('Sensory spectrum position:', this.spectrumPosition);
    return this.spectrumPosition;
  }
  
  async calculateSensoryPosition(descriptorNames: any[]): Promise<number> {

    // Step 2: Extract descriptor values from response data
    // We'll need to adapt this to your data structure
    const descriptorValues: { [key: string]: number } = {};
    
    // Initialize all descriptors to 0
    descriptorNames.forEach(name => {
      descriptorValues[name] = 0;
    });
    
    // Fill in values from your response data
    // This part needs to be adapted to how your data is structured
    const descriptors = await this.panelsService.getResponseDescriptors(this.responseDetails) || [];
    descriptors.forEach((descriptor: any) => {
      // console.log('descriptor', descriptor);
      const name = descriptor.descriptor?.option_id;
      if (descriptorNames.includes(name)) {
        descriptorValues[name] = this.statisticsService.calculatedStats[descriptor.descriptor.option_id].shrinkageEstimate || 0;
      }
    });
    // console.log('descriptorValues', descriptorValues);
    // Step 3: Normalize the sensory term levels (so they sum to 1)
    const sum = Object.values(descriptorValues).reduce((acc, val) => acc + val, 0);
    const normalizedValues: { [key: string]: number } = {};
    
    for (const [key, value] of Object.entries(descriptorValues)) {
      normalizedValues[key] = sum > 0 ? value / sum : 0;
    }
    
    // Step 4: Assign positions to each term on the spectrum
    const termPositions: number[] = [];
    for (let i = 0; i < descriptorNames.length; i++) {
      termPositions.push(i);
    }
    
    // Step 5: Create a quadratic weighting function (higher weight at the ends)
    const midpoint = (descriptorNames.length - 1) / 2;
    const weights: number[] = termPositions.map(pos => 4 * Math.pow(pos - midpoint, 2));
    
    // Step 6: Apply the quadratic weighting function to the term levels
    const weightedData: { [key: string]: number } = {};
    descriptorNames.forEach((name, index) => {
      weightedData[name] = normalizedValues[name] * weights[index];
    });
    
    // Step 7: Calculate the weighted average position
    let weightedSum = 0;
    let weightTotal = 0;
    
    descriptorNames.forEach((name, index) => {
      weightedSum += weightedData[name] * termPositions[index];
      weightTotal += weightedData[name];
    });
    
    const weightedPosition = weightTotal > 0 ? weightedSum / weightTotal : 0;
    
    // Step 8: Normalize the calculated position to fit within the range 0 to 48
    const minPosition = 0;
    const maxPosition = descriptorNames.length - 1;
    const normalizedPosition = ((weightedPosition - minPosition) / (maxPosition - minPosition)) * 48;
    
    return Number(normalizedPosition.toFixed(2));
  }

  async calculateCategoryDistribution(){
    let descriptors = await this.panelsService.getResponseDescriptors(this.responseDetails);
    let descriptorObject: any = {};
    //create an object with the descriptor option_id as the key and the descriptor as the value
    descriptors.forEach((descriptor: any) => {
      descriptorObject[descriptor.descriptor.option_id] = descriptor.descriptor;
    })
    // console.log('descriptors', descriptors);
    // console.log('descriptorObject', descriptorObject);
    // console.log('this.statisticsService.calculatedStats', this.statisticsService.calculatedStats);
    //create an object with the category as the key and the sum and top note as the value
    let categoryShrinkageSums: any = {};
    //loop through the calculated stats and add the shrinkage estimate to the category sum
    //also calculate some other stuff in the same pass for efficiency
    let sum = Object.keys(this.statisticsService.calculatedStats).filter((key: any) => {
      //Only return flavor keys
      return descriptorObject[key]
    }).reduce((acc: any, key: any) => {
      // console.log('key', key);
      // console.log('descriptorObject[key]', descriptorObject[key]);
      let category = descriptorObject[key].type;
      if(categoryShrinkageSums[category]){
        //increment the category sum
        categoryShrinkageSums[category]['sum'] += this.statisticsService.calculatedStats[key].shrinkageEstimate;
        //check if the current descriptor is the new top note
        if(this.statisticsService.calculatedStats[key].shrinkageEstimate > categoryShrinkageSums[category]['topValue']){
          categoryShrinkageSums[category]['topNote'] = descriptorObject[key].name;
          categoryShrinkageSums[category]['topValue'] = this.statisticsService.calculatedStats[key].shrinkageEstimate;
          categoryShrinkageSums[category]['topColor'] = descriptorObject[key].color;
        }
      } else {
        //initialize the category sum
        categoryShrinkageSums[category] = {
          "sum": this.statisticsService.calculatedStats[key].shrinkageEstimate,
          "topNote": descriptorObject[key].name,
          "topValue": this.statisticsService.calculatedStats[key].shrinkageEstimate,
          "color": descriptorObject[key].color
        };
      }
      //Increment the total sum
      return acc + this.statisticsService.calculatedStats[key].shrinkageEstimate;
    }, 0);
    // console.log('sum', sum);
    let payload: any = []
    //Format for the existing chart
    Object.keys(categoryShrinkageSums).forEach((category: any) => {
      categoryShrinkageSums[category]['percentage'] = ((categoryShrinkageSums[category]['sum'] / sum) * 100).toFixed(2);
      payload.push({
        category: category,
        percentage: categoryShrinkageSums[category]['percentage'],
        topNote: categoryShrinkageSums[category]['topNote'],
        color: categoryShrinkageSums[category]['color']
      })
    });
    payload.sort((a: any, b: any) => {
      return b.percentage - a.percentage;
    });
    let maxPercentage = payload[0].percentage;
    //For marketing's sake, make these out of 3
    this.normalizedAromaCategories = payload.map((item: any) => {
      return {
        category: item.category,
        percentage: Math.round((item.percentage / maxPercentage) * 3),
        topNote: item.topNote,
        color: item.color
      }
    })
    this.aromaCategories = payload;
    this.topThreeCategories = payload.slice(0, 3);
    if(this.chart){
      this.updateChartData();
    }
    // console.log('payload', payload);
    
  }

  async getAromaCategoriesByCount() {
    let descriptors = await this.panelsService.getResponseDescriptors(this.responseDetails);
    // console.log('descriptors', descriptors);
    // console.log("calculated stats", this.statisticsService.calculatedStats);
    let totalCount = descriptors.reduce((acc: any, descriptor: any) => {
      return acc + descriptor.count;
    }, 0);
    // console.log('totalCount', totalCount);
    
    // let averages = await this.panelsService.getResponseDescriptorAverages(descriptors, this.responseDetails);
    // averages.sort((a:any,b:any) => {
    //   return b.avg - a.avg;
    // });

    let categories: any = {};

    descriptors.forEach((descriptor: any) => {
      if(!categories[descriptor.descriptor.type]) {
        categories[descriptor.descriptor.type] = {
          count: 0,
          descriptors: []
        };
      }
      categories[descriptor.descriptor.type].count += descriptor.count;
      categories[descriptor.descriptor.type].descriptors.push(descriptor);
    });

    // console.log('categories', categories);

    let percentagesArray: any[] = Object.keys(categories).map((category: any) => {
    //   console.log('category', category);
      return {
        category: category,
        percentage: categories[category].count / totalCount * 100,
        topNote: this.getTopNote(categories[category].descriptors),
        color: categories[category].descriptors[0].descriptor.color
      };
    })
    .sort((a: any, b: any) => {
      return b.percentage - a.percentage;
    });

    // console.log('percentagesArray', percentagesArray);
    

    this.aromaCategories = percentagesArray;
    this.topThreeCategories = percentagesArray.slice(0, 3);
    
    if (this.chart) {
      this.updateChartData();
    }
  }

  createChart() {
    const ctx = this.chartCanvas.nativeElement.getContext('2d');
    if (!ctx) return;

    // console.log('this.aromaCategories', this.aromaCategories);
    
    const colors = this.aromaCategories.map(cat => {
      // Add opacity (0.7) to the color
      const color = cat.color;
      return color.startsWith('#') 
        ? `${color}B3` // Hex with 70% opacity (B3 in hex is ~70%)
        : color.startsWith('rgb') 
          ? color.replace('rgb', 'rgba').replace(')', ', 0.7)')
          : `${color}B3`;
    });
    
    this.chart = new Chart(ctx, {
      type: 'doughnut',
      data: {
        labels: this.aromaCategories.map(cat => {
          return cat.category;
        }),
        datasets: [{
          data: this.aromaCategories.map(cat => cat.percentage),
          backgroundColor: colors,
          rotation:-90,
          borderWidth: 0
        }]
      },
      options: {
        responsive: true,
        cutout: '65%',
        plugins: {
          legend: {
            display: false
          },
          tooltip: {
            backgroundColor: '#000000'
          }
        }
      }
    });
  }

  updateChartData() {
    if (!this.chart) return;
    
    const colors = this.aromaCategories.map(cat => {
      // Add opacity (0.75) to the color
      const color = cat.color;
      return color.startsWith('#') 
        ? `${color}BF` // Hex with 75% opacity (BF in hex is ~75%)
        : color.startsWith('rgb') 
          ? color.replace('rgb', 'rgba').replace(')', ', 0.75)')
          : `${color}BF`;
    });
    
    this.chart.data.labels = this.aromaCategories.map(cat => cat.category.charAt(0).toUpperCase() + cat.category.slice(1));
    this.chart.data.datasets[0].data = this.aromaCategories.map(cat => cat.percentage);
    this.chart.data.datasets[0].backgroundColor = colors;
    this.chart.update();
  }

  getColorForIndex(index: number): string {
    return this.aromaCategories[index]?.color || '#000';
  }
}
