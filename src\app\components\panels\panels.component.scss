@import '../../../_colors';

:host{
    display: block;
    width: 100%;
    min-height: var(--standard-height);
    height: auto;
    padding: var(--standard-padding);
    box-sizing: border-box;

    --router-link-color: #{$darkGray};
    --router-link-active-color: #000;
    --router-link-hover-color: #{$blue};

    .table-container{
        margin-top: 20px;
    }

    .popover-container{
        position: fixed;  // Change to fixed positioning
        top: calc(var(--standard-padding) + 70px);;  // Align with header
        right: calc(var(--standard-padding) + 30px);;
        height: fit-content;
        z-index: 999999;  // Ensure it's above other content

        popover {
            a{
                text-decoration: none;
                color: var(--router-link-color);
                font-size: var(--router-link-size);
                transition: color 0.2s ease;
                cursor: pointer;

                &:hover{
                    color: var(--router-link-hover-color);
                }
            }
            position: absolute;
            top: 45px;  // Adjust this value to position below the button
            right: 0;
        }
    }
}