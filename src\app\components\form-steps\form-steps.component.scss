@import '../../../_colors';

:host{
    --step-color: #{$mediumGray};
    --step-active-color: #{$purple};
    --step-height: 8px;
    --step-width: 33px;
    --step-border-radius: 10px;
    --step-gap: 10px;

    --step-text-size: 0.75rem;
    --step-text-color: #{$mediumDarkGray};
    --step-text-weight: 700;
    --step-text-margin: 10px;

    display: inline-flex;
    margin-left: auto;
    align-items: center;

    .text{
        font-size: var(--step-text-size);
        color: var(--step-text-color);
        font-weight: var(--step-text-weight);
        margin-right: var(--step-text-margin);
    }

    .steps{
        display: flex;
        gap: var(--step-gap);

        .step{
            width: var(--step-width);
            height: var(--step-height);
            background-color: var(--step-color);
            border-radius: var(--step-border-radius);

            &.active{
                background-color: var(--step-active-color);
            }
        }
    }
}