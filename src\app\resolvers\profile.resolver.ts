import { inject } from '@angular/core';
import { ResolveFn } from '@angular/router';
import { ProfileService } from '../services/profile.service';
import { ApiService } from '../services/api.service';
import { firstValueFrom } from 'rxjs';
import { AuthService } from '../services/auth.service';
import { Router } from '@angular/router';
import { LoadingService } from '../services/loading.service';
import { finalize } from 'rxjs/operators';

export const profileResolver: ResolveFn<boolean> = (route, state) => {
  const profileService = inject(ProfileService);
  const authService = inject(AuthService);
  const router = inject(Router);
  const apiService = inject(ApiService);
  const loadingService = inject(LoadingService);

  loadingService.setLoading('profile', true);
  
  return firstValueFrom(apiService.getProfileData().pipe(
    finalize(() => loadingService.setLoading('profile', false))
  )).then((res: any) => {
    profileService.authUser = authService.auth.currentUser;
    profileService.profileData = res;
    console.log(profileService.profileData);
    return true;
  });
};
