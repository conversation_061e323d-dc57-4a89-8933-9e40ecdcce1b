import { Component, Input, inject } from '@angular/core';
import { CommonModule } from '@angular/common';
import { PercentageBarComponent } from '../percentage-bar/percentage-bar.component';
import { ColorService } from '../../services/color.service';
import { GraphTooltipComponent, GraphTooltipConfig } from '@lamb-sensory/lamb-component-library';
import { ConstantsService } from '../../services/constants.service';

@Component({
  selector: 'response-data-overall-block',
  standalone: true,
  imports: [PercentageBarComponent, GraphTooltipComponent, CommonModule],
  templateUrl: './response-data-overall-block.component.html',
  styleUrl: './response-data-overall-block.component.scss'
})
export class ResponseDataOverallBlockComponent {

  @Input() metric: any;
  @Input() responseDetails: any;
  @Input() colorSteps: { color: string, step: number }[] = [{color: '#C67474', step: 0}, {color: '#C7C586', step: 0.5}, {color: '#81C682', step: 1}];;

  colorService = inject(ColorService);
  constantsService = inject(ConstantsService);

  barPieces: any[] = [];

  tooltipConfig: GraphTooltipConfig = {};

  async ngOnInit(){
    // console.log('this.metric', this.metric);
    
    await this.getResponseData();
    this.tooltipConfig = {
      style: {
        insetLeft: Math.round(this.barPieces[0].value) + '%',
        borderRadius: '8px'
      },
      mainText: {
        content: `${Math.round(this.barPieces[0].value)}`,
      },
    }
  }

  get optionName(){
    // console.log('this.metric', this.metric);
    if(typeof this.metric === 'string'){
      return this.constantsService.optionsObject[this.metric].name;
    } else if(this.metric.hasOwnProperty('name')) {
      return this.metric.name;
    } else if (this.metric.hasOwnProperty('label')) {
      return this.metric.label;
    } else {
      return 'error getting option name';
    }
  }

  async getResponseData(){
    let optionId: string;
    if(typeof this.metric === 'string'){
      optionId = this.metric;
    } else {
      optionId = this.metric.id;
    }

    if(this.responseDetails.hasOwnProperty(optionId + '_count')){
      let avg = this.responseDetails[optionId + '_sum'] / this.responseDetails[optionId + '_count'];
      this.barPieces = this.getBarPieces(avg);
    }
  }

  getBarPieces(avg: number){
    return [
      {label: this.metric.name, value: avg, color: this.getColor(avg)},
    ];
  }

  h2r(hex: any) {
    let newhex = this.colorService.h2r(hex);
    return newhex
  }

  r2h(rgb: any) {
    return this.colorService.r2h(rgb);
  }

  _interpolateColor(color1: any, color2: any, factor: any = 0.5) {
    return this.colorService._interpolateColor(color1,color2,factor);
  };



  getColor(avg: number){
    let icol;
    let val = Math.abs(avg/100);
    let ratio;
    for (let i = 0; i < this.colorSteps.length - 1; i++) {
      if (val >= this.colorSteps[i].step && val <= this.colorSteps[i + 1].step) {
        ratio = (val - this.colorSteps[i].step) / (this.colorSteps[i + 1].step - this.colorSteps[i].step);
        icol = this._interpolateColor(this.h2r(this.colorSteps[i].color), this.h2r(this.colorSteps[i + 1].color), ratio);
        break;
      }
    }
    let active_color;
    active_color = this.r2h(icol);
    if(val === 1){
      console.log('this.colorSteps', this.colorSteps);
      
      active_color = this.colorSteps[2].color;
    }
    return active_color;
  }
}
