import { inject, Injectable } from '@angular/core';
import { ApiService } from './api.service';
import { Group } from '../interfaces/group.interface';
import {
  FormBuilder,
  FormControl,
  FormGroup,
  Validators,
  FormArray,
} from '@angular/forms';
import { BehaviorSubject, catchError, map, Observable, of, Subject, switchMap } from 'rxjs';
import { PaginationParams } from '../interfaces/paginationParams';
import { ToastService } from './toast.service';

@Injectable({
  providedIn: 'root',
})
export class GroupsService {
  apiService = inject(ApiService);
  form = inject(FormBuilder);
  toastService = inject(ToastService);
  private pageSubject = new BehaviorSubject<number>(1);
  private limitSubject = new BehaviorSubject<number>(10);


  public groups$ = this.createGroupsStream();

  page: number = 1;
  limit: number = 3;

  formStep: number = 1;
  totalSteps: number = 1;
  formSuperHeader: string = 'Creating group';
  formHeader: string = 'Create a Group';
  nextStepFlavorTextArr: string[] = [
    'All done?',
    // Add more flavor text for additional steps if needed
  ];
  hasError: boolean = false;
  errorMessage: string = '';

  editing: boolean = false;
  editingGroup: any = {};

  scrollResetSubject = new Subject<void>();

  constructor() {
  }

  private createGroupsStream() {
    return this.pageSubject.pipe(
      switchMap((page) =>
        this.getUserGroups({
          page,
          limit: this.limitSubject.getValue(),
        }, true).pipe(catchError((error) => {
          console.error('Error fetching groups:', error);
          return of({ groups: [], total: 0 });
        })
      ))
    );
  }

  public setPage(page: number) {
    this.page = page;
    this.pageSubject.next(page);
  }

  public setLimit(limit: number) {
    this.limitSubject.next(limit);
    this.setPage(1);
  }

  step1Form: FormGroup = this.form.group({
    name: new FormControl<string>('', [
      Validators.required,
      Validators.minLength(3),
      Validators.maxLength(50),
    ]),
    description: new FormControl<string>('', [
      Validators.required,
      Validators.minLength(3),
      Validators.maxLength(200),
    ]),
    panels: new FormArray([]),
    users: new FormArray([]),
    samples: new FormArray([]),
  });

  clearForm() {
    this.step1Form.reset();
    const usersArray = this.step1Form.get('users') as FormArray;
    usersArray.clear();
    const panelsArray = this.step1Form.get('panels') as FormArray;
    panelsArray.clear();
    const samplesArray = this.step1Form.get('samples') as FormArray;
    samplesArray.clear();
  }

  groups: any[] = [];
  groupsObject: { [key: string]: any } = {};

  getUserGroups(params: PaginationParams, includeReferences: boolean = false):Observable<any> {
    return this.apiService.getGroups(params, includeReferences).pipe(
      map((response: any) => {
        console.log(response);
        this.groups = response['groups'] || [];
        this.groupsObject = (response['groups'] || []).reduce(
          (acc: { [key: string]: any }, group: any) => {
            acc[group.id] = group;
            return acc;
          },
          {}
        );
        return response;
      }),
      catchError(error => {
        console.error('Error in getUserGroups:', error);
        return of({ groups: [], total: 0 });
      })
    );
  }

  private selectedGroupSubject = new BehaviorSubject<any>(null);
  selectedGroup$ = this.selectedGroupSubject.asObservable();
  setSelectedGroup(group: any) {
    this.selectedGroupSubject.next(group);
  }

  getGroupById(id: any, includeReferences: boolean = false): Observable<any> {
    return this.apiService.getGroupById(id, includeReferences).pipe(
      catchError((error) => {
        console.error('Error fetching group:', error);
        return of(null);
      })
    );
  }

  createGroup(body: Group) {
    return this.apiService.createGroup(body);
  }

  deleteGroup(id: string) {
    return this.apiService.deletePanel(id)
  }

  setFormStep(step: number) {
    this.formStep = step;
  }

  async fillFormFromGroupObject(group: any) {
    console.log(group);
    this.step1Form.patchValue({
      name: group.name,
      description: group.description,
    });
    
    // Clear existing arrays
    const usersArray = this.step1Form.get('users') as FormArray;
    usersArray.clear();
    const panelsArray = this.step1Form.get('panels') as FormArray;
    panelsArray.clear();
    const samplesArray = this.step1Form.get('samples') as FormArray;
    samplesArray.clear();
    
    // If the group has references, store them in the form arrays
    // This provides data to be used when determining selected status
    if (group.panels && group.panels.length) {
      group.panels.forEach((panel: any) => {
        panelsArray.push(new FormControl(panel));
      });
    }
    
    if (group.samples && group.samples.length) {
      group.samples.forEach((sample: any) => {
        samplesArray.push(new FormControl(sample));
      });
    }
    
    if (group.users && group.users.length) {
      group.users.forEach((user: any) => {
        usersArray.push(new FormControl(user));
      });
    }

    return;
  }

  formErrors: { [key: string]: { [key: string]: boolean } } = {
    step1: {
      name: false,
      description: false,
      users: false,
      panels: false,
      samples: false,
    },
  };

  validateCurrentStep(): boolean {
    const form = this.step1Form;
    let isValid = form.valid;

    // Update individual field errors
    Object.keys(this.formErrors['step1']).forEach((fieldName) => {
      const control = form.get(fieldName);
      if (control) {
        this.formErrors['step1'][
          fieldName as keyof typeof this.formErrors.step1
        ] = control.invalid && (control.dirty || control.touched);
      }
    });

    return isValid;
  }

  showError(): void {
    this.hasError = true;
    this.errorMessage =
      'Please complete all required fields before proceeding.';
  }

  getErrorMessage(fieldName: string): string {
    const control = this.step1Form.get(fieldName);
    if (!control || !control.errors) return '';

    const errors = control.errors;

    if (errors['required']) {
      return `${
        fieldName.charAt(0).toUpperCase() + fieldName.slice(1)
      } is required`;
    }
    if (errors['minlength']) {
      return `${
        fieldName.charAt(0).toUpperCase() + fieldName.slice(1)
      } must be at least ${errors['minlength'].requiredLength} characters`;
    }
    if (errors['maxlength']) {
      return `${
        fieldName.charAt(0).toUpperCase() + fieldName.slice(1)
      } cannot exceed ${errors['maxlength'].requiredLength} characters`;
    }

    return '';
  }

  async save() {

    let dataForAPI = { ...this.step1Form.value };
    dataForAPI.panels = dataForAPI.panels.map((panel: unknown) => {
      if(typeof panel === 'object' && panel !== null && 'id' in panel) {
        return panel.id;
      }
      if(typeof panel === 'string') {
        return panel;
      }
      return null;
    });
    dataForAPI.samples = dataForAPI.samples.map((sample: unknown) => {
      if(typeof sample === 'object' && sample !== null && 'id' in sample) {
        return sample.id;
      }
      if(typeof sample === 'string') {
        return sample;
      }
      return null;
    });
    dataForAPI.users = dataForAPI.users.map((user: unknown) => {
      if(typeof user === 'object' && user !== null && 'id' in user) {
        return user.id;
      }
      if(typeof user === 'string') {
        return user;
      }
      return null;
    });

    if (this.editing) {
      await this.apiService.updateGroup(this.editingGroup.id, dataForAPI).then((response) => {
        response.subscribe((data) => {
          console.log(data);
          this.toastService.goodToast('Group updated successfully');
        });
      });
    } else {
      await this.apiService.createGroup(dataForAPI).then((response) => {
        response.subscribe((data) => {
          console.log(data);
          this.toastService.goodToast('Group created successfully');
        });
      });
    }
  }

}
