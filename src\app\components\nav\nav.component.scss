@import '../../../_colors';

:host{
    --logo-icon-fill: black;

    --router-link-color: #{$darkGray};
    --router-link-active-color: #000;
    --router-link-hover-color: #{$blue};
    --router-link-weight: 400;
    --router-link-active-weight: 700;
    --router-link-size: .9rem;
    --router-link-spacing: 20px;

    --border-color: #{$mediumLightGray};

    --user-icon-bg: linear-gradient(152.4deg, #FEC33C 13.49%, #5E1268 87.07%);;
    --user-icon-text: white;
    --user-icon-size: 35px;
    --user-icon-text-size: .9rem;
    --user-icon-text-weight: 500;

    // --popover-item-inset: calc(100% + 5px) 0 auto auto;
    // --popover-width: fit-content;
    // --popover-background: #fff;
    // --popover-border-radius: 12px;
    // --popover-padding: 15px 20px;
    // --popover-gap: 10px;
    // --popover-box-shadow: 0px 4px 14px 0px rgba(0, 0, 0, 0.25);

    display: flex;
    width: 100%;
    height: var(--nav-height);
    align-items: center;
    border-bottom: 1px solid var(--border-color);
    padding: 0 var(--standard-padding);
    box-sizing: border-box;
    position: relative;
    z-index: 999;

    .logo-container{
        position: relative;
        top: 3px;

        svg{

            path{
                fill: var(--logo-icon-fill);
            }
        }
    }

    .nav-links-container{
        display: flex;
        gap: var(--router-link-spacing);
        flex-basis: fit-content;
        margin-left: 30px;

        a{
            text-decoration: none;
            color: var(--router-link-color);
            font-weight: var(--router-link-weight);
            font-size: var(--router-link-size);
            transition: color 0.2s ease;

            &.active{
                color: var(--router-link-active-color);
                font-weight: var(--router-link-active-weight);
            }

            &:hover{
                color: var(--router-link-hover-color);
            }
        }
    }

    .user-container{
        margin-left: auto;
        position: relative;

        .user-icon{
            display: flex;
            align-items: center;
            justify-content: center;
            background: var(--user-icon-bg);
            border-radius: 50%;
            width: var(--user-icon-size);
            height: var(--user-icon-size);
            color: var(--user-icon-text);
            font-size: var(--user-icon-text-size);
            font-weight: var(--user-icon-text-weight);
            cursor: pointer;
            user-select: none;
        }

        popover{

            a{
                text-decoration: none;
                color: var(--router-link-color);
                font-size: var(--router-link-size);
                transition: color 0.2s ease;
                cursor: pointer;

                &:hover{
                    color: var(--router-link-hover-color);
                }
            }
        }

        // .popover-container{
        //     position: absolute;
        //     inset: var(--popover-item-inset);
        //     width: var(--popover-width);
        //     display: flex;
        //     flex-direction: column;            
        //     background-color: var(--popover-background);
        //     border-radius: var(--popover-border-radius);
        //     padding: var(--popover-padding);
        //     row-gap: var(--popover-gap);
        //     box-shadow: var(--popover-box-shadow);

        //     a{
        //         text-decoration: none;
        //         color: var(--router-link-color);
        //         font-size: var(--router-link-size);
        //         transition: color 0.2s ease;
        //         cursor: pointer;

        //         &:hover{
        //             color: var(--router-link-hover-color);
        //         }
        //     }
        // }
    }
}