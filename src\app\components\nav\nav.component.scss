@import '../../../_colors';

:host{
    --logo-icon-fill: black;

    --router-link-color: #{$darkGray};
    --router-link-active-color: #000;
    --router-link-hover-color: #{$blue};
    --router-link-weight: 400;
    --router-link-active-weight: 700;
    --router-link-size: .9rem;
    --router-link-spacing: 20px;

    --border-color: #{$mediumLightGray};

    --user-icon-bg: linear-gradient(152.4deg, #FEC33C 13.49%, #5E1268 87.07%);;
    --user-icon-text: white;
    --user-icon-size: 35px;
    --user-icon-text-size: .9rem;
    --user-icon-text-weight: 500;

    // --popover-item-inset: calc(100% + 5px) 0 auto auto;
    // --popover-width: fit-content;
    // --popover-background: #fff;
    // --popover-border-radius: 12px;
    // --popover-padding: 15px 20px;
    // --popover-gap: 10px;
    // --popover-box-shadow: 0px 4px 14px 0px rgba(0, 0, 0, 0.25);

    display: flex;
    width: 100%;
    height: var(--nav-height);
    align-items: center;
    border-bottom: 1px solid var(--border-color);
    padding: 0 var(--standard-padding);
    box-sizing: border-box;
    position: relative;
    z-index: 999;

    .logo-container{
        position: relative;
        top: 3px;

        svg{

            path{
                fill: var(--logo-icon-fill);
            }
        }
    }

    .org-selector-container {
        margin-left: 30px;
        position: relative;

        .org-dropdown {
            position: relative;
            cursor: pointer;
            user-select: none;

            .org-display {
                display: flex;
                align-items: center;
                gap: 8px;
                padding: 8px 12px;
                border: 1px solid var(--border-color);
                border-radius: 8px;
                background-color: #fff;
                transition: all 0.2s ease;
                min-width: 200px;

                &:hover {
                    border-color: var(--router-link-hover-color);
                    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
                }

                .org-icon {
                    display: flex;
                    align-items: center;
                    color: var(--router-link-color);
                }

                .org-info {
                    flex: 1;
                    display: flex;
                    flex-direction: column;
                    gap: 2px;

                    .org-name {
                        font-size: 0.875rem;
                        font-weight: 500;
                        color: var(--router-link-active-color);
                        line-height: 1.2;
                    }

                    .org-type {
                        font-size: 0.75rem;
                        color: var(--router-link-color);
                        text-transform: capitalize;
                    }
                }

                .dropdown-arrow {
                    display: flex;
                    align-items: center;
                    color: var(--router-link-color);
                    transition: transform 0.2s ease;

                    &.rotated {
                        transform: rotate(180deg);
                    }

                    svg.rotated {
                        transform: rotate(180deg);
                    }
                }
            }

            .org-dropdown-menu {
                position: absolute;
                top: calc(100% + 5px);
                left: 0;
                right: 0;
                background-color: #fff;
                border: 1px solid var(--border-color);
                border-radius: 8px;
                box-shadow: 0 4px 14px rgba(0, 0, 0, 0.15);
                z-index: 1000;
                overflow: hidden;

                .dropdown-header {
                    padding: 12px 16px;
                    font-size: 0.75rem;
                    font-weight: 600;
                    color: var(--router-link-color);
                    text-transform: uppercase;
                    letter-spacing: 0.5px;
                    background-color: #f8f9fa;
                    border-bottom: 1px solid var(--border-color);
                }

                .org-option {
                    display: flex;
                    align-items: center;
                    gap: 10px;
                    padding: 12px 16px;
                    cursor: pointer;
                    transition: background-color 0.2s ease;
                    border-bottom: 1px solid #f0f0f0;

                    &:last-child {
                        border-bottom: none;
                    }

                    &:hover {
                        background-color: #f8f9fa;
                    }

                    &.selected {
                        background-color: rgba(94, 18, 104, 0.05);

                        .org-option-name {
                            color: var(--router-link-active-color);
                            font-weight: 600;
                        }
                    }

                    .org-option-icon {
                        display: flex;
                        align-items: center;
                        color: var(--router-link-color);
                        opacity: 0.7;
                    }

                    .org-option-info {
                        flex: 1;
                        display: flex;
                        flex-direction: column;
                        gap: 2px;

                        .org-option-name {
                            font-size: 0.875rem;
                            font-weight: 500;
                            color: var(--router-link-active-color);
                            line-height: 1.2;
                        }

                        .org-option-type {
                            font-size: 0.75rem;
                            color: var(--router-link-color);
                            text-transform: capitalize;
                        }
                    }

                    .selected-indicator {
                        display: flex;
                        align-items: center;
                        color: var(--router-link-hover-color);
                    }
                }

                .no-orgs-message {
                    padding: 20px 16px;
                    text-align: center;
                    color: var(--router-link-color);
                    font-size: 0.875rem;
                    font-style: italic;
                }
            }
        }
    }

    .nav-links-container{
        display: flex;
        gap: var(--router-link-spacing);
        flex-basis: fit-content;
        margin-left: 30px;

        a{
            text-decoration: none;
            color: var(--router-link-color);
            font-weight: var(--router-link-weight);
            font-size: var(--router-link-size);
            transition: color 0.2s ease;

            &.active{
                color: var(--router-link-active-color);
                font-weight: var(--router-link-active-weight);
            }

            &:hover{
                color: var(--router-link-hover-color);
            }
        }
    }

    .user-container{
        margin-left: auto;
        position: relative;

        .user-icon{
            display: flex;
            align-items: center;
            justify-content: center;
            background: var(--user-icon-bg);
            border-radius: 50%;
            width: var(--user-icon-size);
            height: var(--user-icon-size);
            color: var(--user-icon-text);
            font-size: var(--user-icon-text-size);
            font-weight: var(--user-icon-text-weight);
            cursor: pointer;
            user-select: none;
        }

        popover{

            a{
                text-decoration: none;
                color: var(--router-link-color);
                font-size: var(--router-link-size);
                transition: color 0.2s ease;
                cursor: pointer;

                &:hover{
                    color: var(--router-link-hover-color);
                }
            }
        }

        // .popover-container{
        //     position: absolute;
        //     inset: var(--popover-item-inset);
        //     width: var(--popover-width);
        //     display: flex;
        //     flex-direction: column;
        //     background-color: var(--popover-background);
        //     border-radius: var(--popover-border-radius);
        //     padding: var(--popover-padding);
        //     row-gap: var(--popover-gap);
        //     box-shadow: var(--popover-box-shadow);

        //     a{
        //         text-decoration: none;
        //         color: var(--router-link-color);
        //         font-size: var(--router-link-size);
        //         transition: color 0.2s ease;
        //         cursor: pointer;

        //         &:hover{
        //             color: var(--router-link-hover-color);
        //         }
        //     }
        // }
    }
}