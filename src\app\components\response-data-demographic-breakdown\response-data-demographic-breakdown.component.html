<div class="demographic-repeater" *ngFor="let demographic of demographicsBars; let i = index">
  <div class="demographic-title">{{demographic.type}}</div>
  <div class="percentage-bar-container">
    <percentage-bar 
      (mouseover)="createTooltip($event, i)" 
      (mouseleave)="destroyTooltip()" 
      [pieces]="demographic.options" 
      [style.--bar-padding]="'5px'" 
      [style.--bar-height]="'40px'">
    </percentage-bar>
  </div>
  <div class="demographic-legend">
    <div class="demographic-category" *ngFor="let option of demographic.options; let i = index">
      <div class="color-dot" [style.background-color]="option.color"></div>
      <div class="demographic-category-name">{{replaceDashWithSpace(option.demographic.name)}}</div>
      <div class="demographic-percentage bold">{{option.value | number:'1.0-0'}}%</div>
    </div>
  </div>
</div>