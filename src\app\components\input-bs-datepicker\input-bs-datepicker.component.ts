import { Component, HostListener, Input, ElementRef, inject } from '@angular/core';
import { FormControl, ReactiveFormsModule } from '@angular/forms';
import { BsDatepickerConfig, BsDatepickerModule } from 'ngx-bootstrap/datepicker';

// import {
//   BrnPopoverCloseDirective,
//   BrnPopoverComponent,
//   BrnPopoverContentDirective,
//   BrnPopoverTriggerDirective,
// } from '@spartan-ng/brain/popover';
// import { HlmPopoverCloseDirective, HlmPopoverContentDirective } from '@spartan-ng/ui-popover-helm';


@Component({
  selector: 'input-bs-datepicker',
  standalone: true,
  imports: [BsDatepickerModule, ReactiveFormsModule],
  templateUrl: './input-bs-datepicker.component.html',
  styleUrl: './input-bs-datepicker.component.scss'
})
export class InputBsDatepickerComponent {

  @Input() control!: FormControl;
  // datepickerConfig: Partial<BsDatepickerConfig> = {
  //   showWeekNumbers: false,
  //   containerClass: 'theme-default',
  //   dateInputFormat: 'MM/DD/YYYY',
  //   adaptivePosition: true,
  //   isAnimated: true
  // };
  // placement: 'bottom' | 'top' = 'bottom';
  // elementRef = inject(ElementRef);

  // ngOnInit(){
    // this.getPlacement();
  // }

  // getPlacement(){
  //   const hostElement = this.elementRef.nativeElement;
  //   const hostRect = hostElement.getBoundingClientRect();
  //   const viewportHeight = window.innerHeight;
    
  //   // Try to find the datepicker element
  //   const datepickerElement = document.querySelector('bs-datepicker-container');
  //   // Use actual height if available, otherwise fallback to 300px
  //   const datepickerHeight = datepickerElement?.clientHeight || 300;
    
  //   // Calculate available space above and below
  //   const spaceAbove = hostRect.top;
  //   const spaceBelow = viewportHeight - (hostRect.top + hostRect.height);
    
  //   // If there's more space below than needed, prefer bottom placement
  //   if (spaceBelow >= datepickerHeight) {
  //     return this.placement = 'bottom';
  //   }
  //   // If there's more space above and not enough below, use top placement
  //   else if (spaceAbove >= datepickerHeight) {
  //     return this.placement = 'top';
  //   }
  //   // Default to bottom if we can't determine a better position
  //   return this.placement = 'bottom';
  // }

  // @HostListener('window:scroll')
  // @HostListener('window:resize')
  // onWindowChange() {
  //   // Force update of placement when window changes
  //   this.getPlacement();
  // }
}


