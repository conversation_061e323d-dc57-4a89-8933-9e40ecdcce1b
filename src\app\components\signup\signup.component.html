<div class="login-wrap">
  <blur-ellipse class="login"></blur-ellipse>
  <div class="logo">
    <svg
      xmlns="http://www.w3.org/2000/svg"
      width="191"
      height="36"
      viewBox="0 0 191 36"
      fill="none"
    >
      <path
        d="M132.484 21.4767C134.178 21.4767 135.569 22.9518 135.569 24.8629C135.569 26.774 134.178 28.2491 132.484 28.2491C131.562 28.2491 130.892 27.9028 130.437 27.3128V28.0695H128.806V19.091H130.437V22.413C130.892 21.823 131.562 21.4767 132.484 21.4767ZM132.181 26.6714C133.192 26.6714 133.938 25.9403 133.938 24.8629C133.938 23.7855 133.192 23.0544 132.181 23.0544C131.182 23.0544 130.437 23.7855 130.437 24.8629C130.437 25.9403 131.182 26.6714 132.181 26.6714Z"
        fill="#FEC33C"
      />
      <path
        d="M140.593 21.6563H142.337L140.037 28.0695C139.379 29.9165 138.305 30.7246 136.7 30.6348V29.0956C137.597 29.1085 138.052 28.7237 138.356 27.8643L135.764 21.6563H137.547L139.19 25.966L140.593 21.6563Z"
        fill="#FEC33C"
      />
      <path
        d="M152.038 28.0695L151.507 26.4534H147.98L147.449 28.0695H145.566L148.663 19.091H150.825L153.934 28.0695H152.038ZM148.537 24.7988H150.964L149.75 21.1304L148.537 24.7988Z"
        fill="#FEC33C"
      />
      <path
        d="M158.396 21.4767C160.09 21.4767 161.481 22.9518 161.481 24.8629C161.481 26.774 160.09 28.2491 158.396 28.2491C157.473 28.2491 156.803 27.9028 156.348 27.3128V28.0695H154.718V19.091H156.348V22.413C156.803 21.823 157.473 21.4767 158.396 21.4767ZM158.093 26.6714C159.104 26.6714 159.85 25.9403 159.85 24.8629C159.85 23.7855 159.104 23.0544 158.093 23.0544C157.094 23.0544 156.348 23.7855 156.348 24.8629C156.348 25.9403 157.094 26.6714 158.093 26.6714Z"
        fill="#FEC33C"
      />
      <path
        d="M163.999 23.4776C163.999 24.4268 167.261 23.8368 167.261 26.1969C167.261 27.5821 166.072 28.2491 164.72 28.2491C163.456 28.2491 162.52 27.7104 162.065 26.7227L163.481 25.9147C163.658 26.4406 164.088 26.7484 164.72 26.7484C165.238 26.7484 165.592 26.5688 165.592 26.1969C165.592 25.2605 162.331 25.7736 162.331 23.5161C162.331 22.2078 163.43 21.4767 164.732 21.4767C165.756 21.4767 166.641 21.9513 167.134 22.8363L165.744 23.6059C165.554 23.1955 165.2 22.9518 164.732 22.9518C164.328 22.9518 163.999 23.1313 163.999 23.4776Z"
        fill="#FEC33C"
      />
      <path
        d="M171.74 23.2468H170.311V25.9147C170.311 26.6073 170.804 26.6201 171.74 26.5688V28.0695C169.452 28.326 168.68 27.6462 168.68 25.9147V23.2468H167.581V21.6563H168.68V20.3608L170.311 19.8606V21.6563H171.74V23.2468Z"
        fill="#FEC33C"
      />
      <path
        d="M174.619 22.7594C174.947 21.8872 175.744 21.528 176.54 21.528V23.375C175.693 23.2724 174.619 23.6572 174.619 25.004V28.0695H172.988V21.6563H174.619V22.7594Z"
        fill="#FEC33C"
      />
      <path
        d="M182.063 21.6563H183.694V28.0695H182.063V27.3128C181.608 27.8899 180.925 28.2491 180.003 28.2491C178.321 28.2491 176.931 26.774 176.931 24.8629C176.931 22.9518 178.321 21.4767 180.003 21.4767C180.925 21.4767 181.608 21.8359 182.063 22.413V21.6563ZM180.306 26.6714C181.317 26.6714 182.063 25.9403 182.063 24.8629C182.063 23.7855 181.317 23.0544 180.306 23.0544C179.307 23.0544 178.561 23.7855 178.561 24.8629C178.561 25.9403 179.307 26.6714 180.306 26.6714Z"
        fill="#FEC33C"
      />
      <path
        d="M191 28.0695H189.116L187.751 26.1199L186.373 28.0695H184.49L186.803 24.7988L184.591 21.6563H186.474L187.751 23.4648L189.028 21.6563H190.899L188.687 24.7859L191 28.0695Z"
        fill="#FEC33C"
      />
      <path
        d="M8.88726 36C7.13289 36 5.58628 35.7658 4.24742 35.2973C2.90856 34.8133 1.86209 34.134 1.10802 33.2596C0.369341 32.3852 0 31.3702 0 30.2147C0 29.1841 0.338562 28.3019 1.01569 27.568C1.69281 26.8341 2.59308 26.4671 3.71649 26.4671C4.74757 26.4671 5.59397 26.7872 6.25571 27.4275C6.93283 28.0677 7.27139 28.9343 7.27139 30.0273C7.27139 31.136 6.90975 31.9792 6.18645 32.5569C5.47855 33.1347 4.63984 33.4236 3.67032 33.4236C3.00859 33.4236 2.40071 33.283 1.8467 33.002C1.29269 32.7053 0.8541 32.3149 0.530927 31.8308C0.207754 31.3468 0.0461676 30.8081 0.0461676 30.2147H1.03877C1.03877 31.2453 1.36964 32.1353 2.03137 32.8848C2.69311 33.6343 3.57029 34.2121 4.66293 34.6181C5.75556 35.0085 6.94822 35.2036 8.24091 35.2036C9.05654 35.2036 9.81061 35.0319 10.5031 34.6884C11.1956 34.3448 11.7573 33.8842 12.1882 33.3064C12.6191 32.7287 12.8346 32.0729 12.8346 31.339C12.8346 30.5114 12.5961 29.7853 12.119 29.1607C11.6573 28.5361 11.0571 27.9662 10.3185 27.4509C9.59516 26.92 8.81801 26.4047 7.98699 25.905C7.15597 25.4053 6.37113 24.8744 5.63244 24.3123C4.89376 23.7502 4.28589 23.1256 3.80882 22.4385C3.34715 21.7358 3.11631 20.9161 3.11631 19.9792C3.11631 18.7768 3.48565 17.676 4.22433 16.6766C4.9784 15.6773 6.04026 14.8809 7.4099 14.2876C8.79492 13.6942 10.4262 13.3975 12.3037 13.3975C14.1658 13.3975 15.6662 13.6552 16.805 14.1705C17.9592 14.6701 18.7902 15.3025 19.298 16.0677C19.8213 16.8328 20.0829 17.5901 20.0829 18.3396C20.0829 19.417 19.7366 20.2524 19.0441 20.8458C18.367 21.4392 17.5975 21.7358 16.7357 21.7358C15.7816 21.7358 14.9198 21.4236 14.1504 20.799C13.3809 20.1588 12.9962 19.2999 12.9962 18.2225C12.9962 17.3325 13.3117 16.583 13.9426 15.974C14.5736 15.365 15.4046 15.0605 16.4357 15.0605C17.3898 15.0605 18.2285 15.365 18.9518 15.974C19.6751 16.5673 20.0367 17.3559 20.0367 18.3396H19.1595C19.1595 17.6526 18.9133 16.9967 18.4209 16.3722C17.9284 15.7319 17.2282 15.2167 16.3202 14.8263C15.4277 14.4203 14.3427 14.2173 13.0654 14.2173C12.2344 14.2173 11.4957 14.3735 10.8494 14.6858C10.2184 14.998 9.71828 15.4118 9.34893 15.9271C8.99498 16.4424 8.81801 17.0124 8.81801 17.637C8.81801 18.4489 9.05654 19.1594 9.5336 19.7684C10.0261 20.3774 10.657 20.9395 11.4265 21.4548C12.1959 21.9701 13.0116 22.4776 13.8734 22.9772C14.7352 23.4769 15.5431 24.0234 16.2972 24.6168C17.0666 25.2101 17.6976 25.8894 18.19 26.6545C18.6825 27.404 18.9287 28.3019 18.9287 29.3481C18.9287 30.691 18.4978 31.8621 17.636 32.8614C16.7896 33.8452 15.6123 34.6181 14.1042 35.1802C12.5961 35.7267 10.8571 36 8.88726 36Z"
        fill="#FEC33C"
      />
      <path
        d="M28.7397 36C27.0161 36 25.5772 35.6643 24.423 34.9928C23.2689 34.3214 22.3994 33.4157 21.8146 32.2759C21.2452 31.136 20.9605 29.879 20.9605 28.5049C20.9605 26.5374 21.3221 24.6558 22.0454 22.8601C22.7841 21.0644 23.7998 19.4639 25.0925 18.0586C26.3852 16.6532 27.8779 15.5446 29.5707 14.7326C31.2635 13.9206 33.0795 13.5146 35.0185 13.5146C37.3884 13.5146 39.2274 14.0221 40.5355 15.0371C41.859 16.0521 42.5207 17.2934 42.5207 18.7612C42.5207 19.8543 42.1976 20.8536 41.5512 21.7593C40.9203 22.6649 39.9738 23.4535 38.7119 24.1249C37.4654 24.7964 35.9265 25.3429 34.0951 25.7645C32.2792 26.1705 30.1786 26.4359 27.7933 26.5608V25.9987C28.809 25.9362 29.7708 25.6396 30.6787 25.1087C31.6021 24.5621 32.4408 23.8829 33.1949 23.0709C33.9489 22.2433 34.5953 21.3689 35.1339 20.4476C35.6879 19.5107 36.1188 18.6207 36.4266 17.7775C36.7344 16.9187 36.8883 16.2004 36.8883 15.6226C36.8883 15.2479 36.8113 14.9278 36.6574 14.6623C36.5036 14.3813 36.2189 14.2407 35.8033 14.2407C35.2032 14.2407 34.5722 14.5608 33.9105 15.201C33.2641 15.8256 32.6332 16.6688 32.0176 17.7306C31.402 18.7768 30.8249 19.948 30.2863 21.244C29.7477 22.54 29.2706 23.8673 28.8551 25.2258C28.455 26.5843 28.1395 27.8803 27.9087 29.1139C27.6779 30.3474 27.5624 31.417 27.5624 32.3227C27.5624 33.3221 27.7933 34.0169 28.255 34.4073C28.7166 34.7977 29.3245 34.9928 30.0786 34.9928C31.0943 34.9928 32.1407 34.7586 33.218 34.2902C34.2952 33.8061 35.3417 33.1191 36.3574 32.229C37.3884 31.339 38.3349 30.2772 39.1967 29.0436L39.8199 29.4652C39.0659 30.5426 38.1733 31.581 37.1422 32.5804C36.1111 33.5797 34.9031 34.3995 33.5181 35.0397C32.1484 35.6799 30.5556 36 28.7397 36Z"
        fill="#FEC33C"
      />
      <path
        d="M59.1788 36C57.7323 36 56.655 35.6487 55.9471 34.946C55.2392 34.2277 54.8853 33.2908 54.8853 32.1353C54.8853 31.7918 54.9237 31.4092 55.0007 30.9876C55.093 30.5504 55.1854 30.1679 55.2777 29.8399L57.9092 20.7287C58.2478 19.5107 58.4786 18.5348 58.6017 17.8009C58.7249 17.0514 58.7172 16.5127 58.5787 16.1848C58.4556 15.8569 58.2016 15.6929 57.8169 15.6929C57.4014 15.6929 56.8551 16.0442 56.1779 16.7469C55.5008 17.434 54.7775 18.3943 54.0081 19.6278C53.2386 20.8614 52.4922 22.3058 51.7689 23.961C51.061 25.6005 50.4532 27.3806 49.9453 29.3012H49.4606C49.8145 27.9271 50.2608 26.5062 50.7994 25.0384C51.338 23.5706 51.9536 22.1574 52.6461 20.799C53.354 19.4249 54.1235 18.1913 54.9545 17.0982C55.8009 16.0052 56.6935 15.1386 57.6322 14.4984C58.5864 13.8582 59.579 13.5381 60.61 13.5381C61.872 13.5381 62.8876 13.8425 63.6571 14.4515C64.4266 15.0605 64.9113 15.8959 65.1114 16.9577C65.3268 18.0039 65.2422 19.2062 64.8575 20.5647L61.418 32.5569C61.3718 32.6818 61.3333 32.838 61.3026 33.0254C61.2718 33.1971 61.2564 33.3455 61.2564 33.4704C61.2564 33.7202 61.3026 33.9076 61.3949 34.0325C61.4872 34.1574 61.6411 34.2199 61.8566 34.2199C62.6106 34.2199 63.434 33.7046 64.3265 32.674C65.2191 31.6435 66.1271 30.0507 67.0504 27.8959L67.7429 28.1535C66.9581 30.0586 66.1271 31.5888 65.2499 32.7443C64.3881 33.8842 63.457 34.7118 62.4567 35.2271C61.4564 35.7424 60.3638 36 59.1788 36ZM41.4736 35.5316L46.8983 14.7326H43.7589V13.9831H53.985L48.3064 35.5316H41.4736Z"
        fill="#FEC33C"
      />
      <path
        d="M74.4745 36C72.7202 36 71.1735 35.7658 69.8347 35.2973C68.4958 34.8133 67.4494 34.134 66.6953 33.2596C65.9566 32.3852 65.5873 31.3702 65.5873 30.2147C65.5873 29.1841 65.9258 28.3019 66.603 27.568C67.2801 26.8341 68.1803 26.4671 69.3038 26.4671C70.3348 26.4671 71.1812 26.7872 71.843 27.4275C72.5201 28.0677 72.8587 28.9343 72.8587 30.0273C72.8587 31.136 72.497 31.9792 71.7737 32.5569C71.0658 33.1347 70.2271 33.4236 69.2576 33.4236C68.5958 33.4236 67.988 33.283 67.434 33.002C66.88 32.7053 66.4414 32.3149 66.1182 31.8308C65.795 31.3468 65.6334 30.8081 65.6334 30.2147H66.626C66.626 31.2453 66.9569 32.1353 67.6186 32.8848C68.2804 33.6343 69.1576 34.2121 70.2502 34.6181C71.3428 35.0085 72.5355 35.2036 73.8282 35.2036C74.6438 35.2036 75.3979 35.0319 76.0904 34.6884C76.7829 34.3448 77.3446 33.8842 77.7755 33.3064C78.2064 32.7287 78.4219 32.0729 78.4219 31.339C78.4219 30.5114 78.1833 29.7853 77.7063 29.1607C77.2446 28.5361 76.6444 27.9662 75.9057 27.4509C75.1824 26.92 74.4053 26.4047 73.5743 25.905C72.7432 25.4053 71.9584 24.8744 71.2197 24.3123C70.481 23.7502 69.8732 23.1256 69.3961 22.4385C68.9344 21.7358 68.7036 20.9161 68.7036 19.9792C68.7036 18.7768 69.0729 17.676 69.8116 16.6766C70.5657 15.6773 71.6275 14.8809 72.9972 14.2876C74.3822 13.6942 76.0134 13.3975 77.8909 13.3975C79.753 13.3975 81.2535 13.6552 82.3923 14.1705C83.5464 14.6701 84.3775 15.3025 84.8853 16.0677C85.4085 16.8328 85.6702 17.5901 85.6702 18.3396C85.6702 19.417 85.3239 20.2524 84.6314 20.8458C83.9543 21.4392 83.1848 21.7358 82.323 21.7358C81.3689 21.7358 80.5071 21.4236 79.7376 20.799C78.9682 20.1588 78.5834 19.2999 78.5834 18.2225C78.5834 17.3325 78.8989 16.583 79.5299 15.974C80.1608 15.365 80.9918 15.0605 82.0229 15.0605C82.9771 15.0605 83.8158 15.365 84.539 15.974C85.2623 16.5673 85.624 17.3559 85.624 18.3396H84.7468C84.7468 17.6526 84.5006 16.9967 84.0081 16.3722C83.5157 15.7319 82.8155 15.2167 81.9075 14.8263C81.0149 14.4203 79.93 14.2173 78.6527 14.2173C77.8217 14.2173 77.083 14.3735 76.4366 14.6858C75.8057 14.998 75.3055 15.4118 74.9362 15.9271C74.5822 16.4424 74.4053 17.0124 74.4053 17.637C74.4053 18.4489 74.6438 19.1594 75.1209 19.7684C75.6133 20.3774 76.2443 20.9395 77.0137 21.4548C77.7832 21.9701 78.5988 22.4776 79.4606 22.9772C80.3224 23.4769 81.1303 24.0234 81.8844 24.6168C82.6539 25.2101 83.2848 25.8894 83.7773 26.6545C84.2697 27.404 84.516 28.3019 84.516 29.3481C84.516 30.691 84.0851 31.8621 83.2233 32.8614C82.3769 33.8452 81.1996 34.6181 79.6915 35.1802C78.1833 35.7267 76.4443 36 74.4745 36Z"
        fill="#FEC33C"
      />
      <path
        d="M94.327 36C92.6034 36 91.1645 35.6643 90.0103 34.9928C88.8561 34.3214 87.9866 33.4157 87.4018 32.2759C86.8324 31.136 86.5477 29.879 86.5477 28.5049C86.5477 26.5374 86.9094 24.6558 87.6327 22.8601C88.3714 21.0644 89.387 19.4639 90.6797 18.0586C91.9724 16.6532 93.4652 15.5446 95.158 14.7326C96.8508 13.9206 98.6667 13.5146 100.606 13.5146C102.976 13.5146 104.815 14.0221 106.123 15.0371C107.446 16.0521 108.108 17.2934 108.108 18.7612C108.108 19.8543 107.785 20.8536 107.138 21.7593C106.508 22.6649 105.561 23.4535 104.299 24.1249C103.053 24.7964 101.514 25.3429 99.6824 25.7645C97.8665 26.1705 95.7659 26.4359 93.3805 26.5608V25.9987C94.3962 25.9362 95.3581 25.6396 96.266 25.1087C97.1894 24.5621 98.0281 23.8829 98.7822 23.0709C99.5362 22.2433 100.183 21.3689 100.721 20.4476C101.275 19.5107 101.706 18.6207 102.014 17.7775C102.322 16.9187 102.476 16.2004 102.476 15.6226C102.476 15.2479 102.399 14.9278 102.245 14.6623C102.091 14.3813 101.806 14.2407 101.391 14.2407C100.79 14.2407 100.159 14.5608 99.4977 15.201C98.8514 15.8256 98.2204 16.6688 97.6049 17.7306C96.9893 18.7768 96.4122 19.948 95.8736 21.244C95.335 22.54 94.8579 23.8673 94.4424 25.2258C94.0423 26.5843 93.7268 27.8803 93.496 29.1139C93.2651 30.3474 93.1497 31.417 93.1497 32.3227C93.1497 33.3221 93.3805 34.0169 93.8422 34.4073C94.3039 34.7977 94.9118 34.9928 95.6658 34.9928C96.6815 34.9928 97.728 34.7586 98.8052 34.2902C99.8825 33.8061 100.929 33.1191 101.945 32.229C102.976 31.339 103.922 30.2772 104.784 29.0436L105.407 29.4652C104.653 30.5426 103.761 31.581 102.729 32.5804C101.698 33.5797 100.49 34.3995 99.1053 35.0397C97.7357 35.6799 96.1429 36 94.327 36Z"
        fill="#FEC33C"
      />
      <path
        d="M112.876 36C111.291 36 110.153 35.6955 109.46 35.0865C108.768 34.4776 108.421 33.6656 108.421 32.6506C108.421 32.1822 108.46 31.745 108.537 31.339C108.629 30.9174 108.706 30.5738 108.768 30.3084L112.923 14.7326H109.714V13.9831H119.894L114.792 33.0722C114.762 33.1971 114.731 33.3299 114.7 33.4704C114.685 33.6109 114.677 33.7437 114.677 33.8686C114.677 34.056 114.715 34.2121 114.792 34.337C114.885 34.4463 115.039 34.501 115.254 34.501C115.639 34.501 116.093 34.3292 116.616 33.9857C117.155 33.6265 117.755 32.9707 118.417 32.0182C119.078 31.0657 119.794 29.6916 120.563 27.8959L121.302 28.1535C120.533 29.918 119.748 31.3858 118.947 32.5569C118.147 33.7124 117.262 34.579 116.293 35.1568C115.323 35.7189 114.185 36 112.876 36ZM118.44 8.83019C117.624 8.83019 116.885 8.62719 116.224 8.22121C115.577 7.79961 115.062 7.25309 114.677 6.58165C114.308 5.91021 114.123 5.18412 114.123 4.40338C114.123 3.62264 114.308 2.90436 114.677 2.24853C115.062 1.5771 115.577 1.03839 116.224 0.632399C116.885 0.2108 117.624 0 118.44 0C119.271 0 120.009 0.2108 120.656 0.632399C121.317 1.03839 121.841 1.5771 122.225 2.24853C122.61 2.90436 122.802 3.62264 122.802 4.40338C122.802 5.18412 122.61 5.91021 122.225 6.58165C121.841 7.25309 121.317 7.79961 120.656 8.22121C120.009 8.62719 119.271 8.83019 118.44 8.83019Z"
        fill="#FEC33C"
      />
    </svg>
  </div>
  <div class="welcome">Welcome.</div>
  <div class="sub-welcome">Please enter your details to sign up</div>

  <div class="show-toggle">
    <input type="checkbox" id="show" (change)="isShow = !isShow" />
    <label for="show">For Shows</label>
  </div>

  <div *ngIf="false" class="sso-login-container">
    <gray-button (onAction)="googleLogin($event)">
      <svg
        xmlns="http://www.w3.org/2000/svg"
        width="24"
        height="24"
        viewBox="0 0 24 24"
        fill="none"
      >
        <path
          d="M22.56 12.25C22.56 11.47 22.49 10.72 22.36 10H12V14.26H17.92C17.66 15.63 16.88 16.79 15.71 17.57V20.34H19.28C21.36 18.42 22.56 15.6 22.56 12.25Z"
          fill="black"
        />
        <path
          d="M12 23C14.97 23 17.46 22.02 19.28 20.34L15.71 17.57C14.73 18.23 13.48 18.63 12 18.63C9.13999 18.63 6.70999 16.7 5.83999 14.1H2.17999V16.94C3.98999 20.53 7.69999 23 12 23Z"
          fill="black"
        />
        <path
          d="M5.84 14.09C5.62 13.43 5.49 12.73 5.49 12C5.49 11.27 5.62 10.57 5.84 9.91V7.07H2.18C1.43 8.55 1 10.22 1 12C1 13.78 1.43 15.45 2.18 16.93L5.03 14.71L5.84 14.09Z"
          fill="black"
        />
        <path
          d="M12 5.38C13.62 5.38 15.06 5.94 16.21 7.02L19.36 3.87C17.45 2.09 14.97 1 12 1C7.69999 1 3.98999 3.47 2.17999 7.07L5.83999 9.91C6.70999 7.31 9.13999 5.38 12 5.38Z"
          fill="black"
        />
      </svg>
    </gray-button>
    <!-- <gray-button (onAction)="microsoftLogin($event)">
      <svg xmlns="http://www.w3.org/2000/svg" width="23" height="23" viewBox="0 0 23 23" fill="none">
        <path d="M1 1H11V11H1V1Z" fill="black"/>
        <path d="M12 1H22V11H12V1Z" fill="black"/>
        <path d="M1 12H11V22H1V12Z" fill="black"/>
        <path d="M12 12H22V22H12V12Z" fill="black"/>
      </svg>
    </gray-button> -->

    <div class="or-container">
      <div class="or-line"></div>
      <div class="or-text">or</div>
      <div class="or-line"></div>
    </div>
  </div>

  <p class="error" *ngIf="hasError">{{ errorMessage }}</p>
  <!-- <div class="form-wrap"> -->
  <form
    [formGroup]="signupForm"
    (ngSubmit)="onSubmit()"
    class="login-form"
    [class.error]="hasError"
  >
    <div class="section-label" *ngIf="!isMobile">Personal Details</div>
    <div
      class="form-group"
      [class.error]="errors.firstNameError"
      *ngIf="!isShow"
    >
      <label for="firstName"
        >First Name
        <!-- <span class="optional">OPTIONAL</span> --></label
      >
      <input
        type="text"
        id="firstName"
        formControlName="firstName"
        class="form-control"
        placeholder="first name"
        (click)="resetError()"
      />
    </div>
    <div
      class="form-group"
      [class.error]="errors.lastNameError"
      *ngIf="!isShow"
    >
      <label for="lastName"
        >Last Name
        <!-- <span class="optional">OPTIONAL</span> --></label
      >
      <input
        type="text"
        id="lastName"
        formControlName="lastName"
        class="form-control"
        placeholder="last name"
        (click)="resetError()"
      />
    </div>
    <div class="form-group" [class.error]="errors.emailError">
      <label for="email">Email Address</label>
      <input
        type="email"
        id="email"
        formControlName="email"
        class="form-control"
        placeholder="email address"
        (click)="resetError()"
      />
    </div>
    <div class="form-group" [class.error]="errors.zipCodeError">
      <label for="zipCode">Zip Code</label>
      <input
        type="text"
        id="zipCode"
        formControlName="zipCode"
        class="form-control"
        placeholder="ZIP code"
        (click)="resetError()"
      />
    </div>

    <div class="section-label" *ngIf="!isMobile && !isShow">Password</div>
    <div
      class="form-group"
      [class.error]="errors.passwordError"
      *ngIf="!isShow"
    >
      <label for="password">Password</label>
      <input
        type="{{ showPassword ? 'text' : 'password' }}"
        id="password"
        formControlName="password"
        class="form-control"
        placeholder="password"
        (click)="resetError()"
      />
      <div class="showPassword" (click)="togglePasswordVisibility()">
        <svg
          xmlns="http://www.w3.org/2000/svg"
          width="19"
          height="14"
          viewBox="0 0 19 14"
          fill="none"
        >
          <path
            d="M9.49989 0C7.44636 0.00700001 5.44363 0.656254 3.7549 1.862C2.06694 3.06775 0.772633 4.77311 0.0438952 6.75327C-0.0146317 6.91253 -0.0146317 7.08753 0.0438952 7.24677C1.0134 9.90857 2.99663 12.0453 5.53114 13.1626C8.06652 14.2791 10.9335 14.2791 13.4689 13.1626C16.0034 12.0452 17.9866 9.90857 18.9561 7.24677C19.0146 7.08752 19.0146 6.91252 18.9561 6.75327C18.2275 4.77316 16.9331 3.0678 15.2451 1.862C13.5563 0.656254 11.5534 0.00701121 9.49989 0ZM9.49989 12.6C7.76612 12.5939 6.07226 12.0593 4.63281 11.0626C3.19254 10.066 2.07029 8.65284 1.40713 6.99994C2.29013 4.77918 3.99419 3.01251 6.14284 2.0912C8.29222 1.16982 10.7079 1.16982 12.8574 2.0912C15.0059 3.01258 16.71 4.7792 17.5931 6.99994C16.9298 8.65282 15.8076 10.0661 14.3674 11.0626C12.928 12.0593 11.234 12.5939 9.50033 12.6H9.49989Z"
            fill="black"
          />
          <path
            d="M9.49989 2.8C8.4201 2.8 7.38443 3.24276 6.62099 4.03026C5.85759 4.81775 5.42839 5.88615 5.42839 7.00006C5.42839 8.11396 5.85759 9.18231 6.62099 9.96985C7.38438 10.7573 8.42007 11.2001 9.49989 11.2001C10.5797 11.2001 11.6154 10.7573 12.3788 9.96985C13.1422 9.18236 13.5714 8.11396 13.5714 7.00006C13.5705 5.88617 13.1405 4.81867 12.3779 4.03116C11.6146 3.24453 10.5797 2.80088 9.49989 2.8ZM9.49989 9.80002C8.40229 9.80002 7.41242 9.11751 6.99252 8.0719C6.57181 7.02539 6.80422 5.82047 7.58033 5.01989C8.35645 4.21927 9.52445 3.97952 10.5389 4.41352C11.5526 4.84665 12.2142 5.86778 12.2142 7.00006C12.2125 8.54617 10.9987 9.79822 9.49989 9.80002Z"
            fill="black"
          />
        </svg>
        <svg
          class="line"
          *ngIf="showPassword"
          xmlns="http://www.w3.org/2000/svg"
          width="19"
          height="14"
          viewBox="0 0 19 14"
          fill="none"
        >
          <line
            x1="34.5821"
            y1="17.4729"
            x2="20.709"
            y2="34.5475"
            stroke="black"
            stroke-width="1.5"
          />
        </svg>
      </div>
    </div>
    <div
      class="form-group"
      [class.error]="errors.confirmPasswordError"
      *ngIf="!isShow"
    >
      <label for="confirm_password">Confirm Password</label>
      <input
        type="{{ showPassword ? 'text' : 'password' }}"
        id="confirm_password"
        formControlName="confirmPassword"
        class="form-control"
        placeholder="confirm password"
        (click)="resetError()"
      />
      <div class="showPassword" (click)="togglePasswordVisibility()">
        <svg
          xmlns="http://www.w3.org/2000/svg"
          width="19"
          height="14"
          viewBox="0 0 19 14"
          fill="none"
        >
          <path
            d="M9.49989 0C7.44636 0.00700001 5.44363 0.656254 3.7549 1.862C2.06694 3.06775 0.772633 4.77311 0.0438952 6.75327C-0.0146317 6.91253 -0.0146317 7.08753 0.0438952 7.24677C1.0134 9.90857 2.99663 12.0453 5.53114 13.1626C8.06652 14.2791 10.9335 14.2791 13.4689 13.1626C16.0034 12.0452 17.9866 9.90857 18.9561 7.24677C19.0146 7.08752 19.0146 6.91252 18.9561 6.75327C18.2275 4.77316 16.9331 3.0678 15.2451 1.862C13.5563 0.656254 11.5534 0.00701121 9.49989 0ZM9.49989 12.6C7.76612 12.5939 6.07226 12.0593 4.63281 11.0626C3.19254 10.066 2.07029 8.65284 1.40713 6.99994C2.29013 4.77918 3.99419 3.01251 6.14284 2.0912C8.29222 1.16982 10.7079 1.16982 12.8574 2.0912C15.0059 3.01258 16.71 4.7792 17.5931 6.99994C16.9298 8.65282 15.8076 10.0661 14.3674 11.0626C12.928 12.0593 11.234 12.5939 9.50033 12.6H9.49989Z"
            fill="black"
          />
          <path
            d="M9.49989 2.8C8.4201 2.8 7.38443 3.24276 6.62099 4.03026C5.85759 4.81775 5.42839 5.88615 5.42839 7.00006C5.42839 8.11396 5.85759 9.18231 6.62099 9.96985C7.38438 10.7573 8.42007 11.2001 9.49989 11.2001C10.5797 11.2001 11.6154 10.7573 12.3788 9.96985C13.1422 9.18236 13.5714 8.11396 13.5714 7.00006C13.5705 5.88617 13.1405 4.81867 12.3779 4.03116C11.6146 3.24453 10.5797 2.80088 9.49989 2.8ZM9.49989 9.80002C8.40229 9.80002 7.41242 9.11751 6.99252 8.0719C6.57181 7.02539 6.80422 5.82047 7.58033 5.01989C8.35645 4.21927 9.52445 3.97952 10.5389 4.41352C11.5526 4.84665 12.2142 5.86778 12.2142 7.00006C12.2125 8.54617 10.9987 9.79822 9.49989 9.80002Z"
            fill="black"
          />
        </svg>
        <svg
          class="line"
          *ngIf="showPassword"
          xmlns="http://www.w3.org/2000/svg"
          width="19"
          height="14"
          viewBox="0 0 19 14"
          fill="none"
        >
          <line
            x1="34.5821"
            y1="17.4729"
            x2="20.709"
            y2="34.5475"
            stroke="black"
            stroke-width="1.5"
          />
        </svg>
      </div>
    </div>

    <div class="section-label affiliate" *ngIf="!isMobile && !isShow">Affiliate</div>
    <div class="section-label new-org" *ngIf="!isMobile && !isShow">New Organization</div>
    <div
      class="form-group affiliate-group"
      [class.error]="errors.affiliateCodeError"
      *ngIf="!isShow"
    >
      <label for="affiliateCode"
        >Affiliate Code <span class="optional">OPTIONAL</span></label
      >
      <input
        type="text"
        id="affiliateCode"
        formControlName="affiliateCode"
        class="form-control"
        placeholder="affiliate code"
        (click)="resetError()"
      />
    </div>

    <div class="form-group" *ngIf="!isShow">
      <div class="form-group" [class.error]="errors.orgName">
        <label for="orgName"
          >Organization Name
          <!-- <span class="optional">OPTIONAL</span> --></label
        >
        <input
          type="text"
          id="orgName"
          formControlName="orgName"
          class="form-control"
          placeholder="Organization Name"
          (click)="resetError()"
        />
      </div>
      <div class="form-group" [class.error]="errors.website">
        <label for="website"
          >Website
          <!-- <span class="optional">OPTIONAL</span> --></label
        >
        <input
          type="text"
          id="website"
          formControlName="website"
          class="form-control"
          placeholder="website"
          (click)="resetError()"
        />
      </div>
    </div>

    <div class="section-label general" *ngIf="!isMobile">General</div>

    <div class="form-group checkbox-group">
      <!-- <div class="form-group-inner checkbox-group-inner">
          <input type="checkbox" id="isBrand" formControlName="isBrand" class="form-control" (click)="resetError()">
          <label for="isBrand">I'm signing up for a brand</label>
        </div> -->
      <div class="form-group-inner checkbox-group-inner">
        <input
          type="checkbox"
          id="marketingOptIn"
          formControlName="marketingOptIn"
          class="form-control"
          (click)="resetError()"
        />
        <label for="marketingOptIn"
          >I agree to receive marketing emails and messages about updates,
          promotions, and offers</label
        >
      </div>
    </div>

    <div class="bottom">
      <gray-button class="yellow" (onAction)="onSubmit()">Sign Up</gray-button>
    </div>
  </form>
  <!-- </div> -->
  <div class="signup-link">
    Already have an account? <a routerLink="/login">Sign in.</a>
  </div>
</div>

<div class="loading" *ngIf="authService.loading">
  <svg
    xmlns="http://www.w3.org/2000/svg"
    xmlns:xlink="http://www.w3.org/1999/xlink"
    style="
      margin: auto;
      background: none;
      display: block;
      shape-rendering: auto;
    "
    width="200px"
    height="200px"
    viewBox="0 0 100 100"
    preserveAspectRatio="xMidYMid"
  >
    <g transform="rotate(0 50 50)">
      <rect x="47" y="24" rx="3" ry="6" width="6" height="12" fill="#d7d7d7">
        <animate
          attributeName="opacity"
          values="1;0"
          keyTimes="0;1"
          dur="1s"
          begin="-0.9166666666666666s"
          repeatCount="indefinite"
        ></animate>
      </rect>
    </g>
    <g transform="rotate(30 50 50)">
      <rect x="47" y="24" rx="3" ry="6" width="6" height="12" fill="#d7d7d7">
        <animate
          attributeName="opacity"
          values="1;0"
          keyTimes="0;1"
          dur="1s"
          begin="-0.8333333333333334s"
          repeatCount="indefinite"
        ></animate>
      </rect>
    </g>
    <g transform="rotate(60 50 50)">
      <rect x="47" y="24" rx="3" ry="6" width="6" height="12" fill="#d7d7d7">
        <animate
          attributeName="opacity"
          values="1;0"
          keyTimes="0;1"
          dur="1s"
          begin="-0.75s"
          repeatCount="indefinite"
        ></animate>
      </rect>
    </g>
    <g transform="rotate(90 50 50)">
      <rect x="47" y="24" rx="3" ry="6" width="6" height="12" fill="#d7d7d7">
        <animate
          attributeName="opacity"
          values="1;0"
          keyTimes="0;1"
          dur="1s"
          begin="-0.6666666666666666s"
          repeatCount="indefinite"
        ></animate>
      </rect>
    </g>
    <g transform="rotate(120 50 50)">
      <rect x="47" y="24" rx="3" ry="6" width="6" height="12" fill="#d7d7d7">
        <animate
          attributeName="opacity"
          values="1;0"
          keyTimes="0;1"
          dur="1s"
          begin="-0.5833333333333334s"
          repeatCount="indefinite"
        ></animate>
      </rect>
    </g>
    <g transform="rotate(150 50 50)">
      <rect x="47" y="24" rx="3" ry="6" width="6" height="12" fill="#d7d7d7">
        <animate
          attributeName="opacity"
          values="1;0"
          keyTimes="0;1"
          dur="1s"
          begin="-0.5s"
          repeatCount="indefinite"
        ></animate>
      </rect>
    </g>
    <g transform="rotate(180 50 50)">
      <rect x="47" y="24" rx="3" ry="6" width="6" height="12" fill="#d7d7d7">
        <animate
          attributeName="opacity"
          values="1;0"
          keyTimes="0;1"
          dur="1s"
          begin="-0.4166666666666667s"
          repeatCount="indefinite"
        ></animate>
      </rect>
    </g>
    <g transform="rotate(210 50 50)">
      <rect x="47" y="24" rx="3" ry="6" width="6" height="12" fill="#d7d7d7">
        <animate
          attributeName="opacity"
          values="1;0"
          keyTimes="0;1"
          dur="1s"
          begin="-0.3333333333333333s"
          repeatCount="indefinite"
        ></animate>
      </rect>
    </g>
    <g transform="rotate(240 50 50)">
      <rect x="47" y="24" rx="3" ry="6" width="6" height="12" fill="#d7d7d7">
        <animate
          attributeName="opacity"
          values="1;0"
          keyTimes="0;1"
          dur="1s"
          begin="-0.25s"
          repeatCount="indefinite"
        ></animate>
      </rect>
    </g>
    <g transform="rotate(270 50 50)">
      <rect x="47" y="24" rx="3" ry="6" width="6" height="12" fill="#d7d7d7">
        <animate
          attributeName="opacity"
          values="1;0"
          keyTimes="0;1"
          dur="1s"
          begin="-0.16666666666666666s"
          repeatCount="indefinite"
        ></animate>
      </rect>
    </g>
    <g transform="rotate(300 50 50)">
      <rect x="47" y="24" rx="3" ry="6" width="6" height="12" fill="#d7d7d7">
        <animate
          attributeName="opacity"
          values="1;0"
          keyTimes="0;1"
          dur="1s"
          begin="-0.08333333333333333s"
          repeatCount="indefinite"
        ></animate>
      </rect>
    </g>
    <g transform="rotate(330 50 50)">
      <rect x="47" y="24" rx="3" ry="6" width="6" height="12" fill="#d7d7d7">
        <animate
          attributeName="opacity"
          values="1;0"
          keyTimes="0;1"
          dur="1s"
          begin="0s"
          repeatCount="indefinite"
        ></animate>
      </rect>
    </g>
  </svg>
</div>
