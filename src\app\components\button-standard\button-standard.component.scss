@import '../../../_colors.scss';

:host{
    display: flex;
    margin:0px auto;
    min-width: fit-content;
    width: fit-content;
    height: 40px;
    position: relative;
    // font-size: .9rem;
    // font-weight: 500;

    button{
        background-color: $lightestGray;
        color: $buttonTextColor;
        border-radius: 10px;
        cursor: pointer;
        transition: all 0.2s ease;
        width: 100%;
        height: 100%;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 12px;
        font-weight: 600;
        box-sizing: border-box;
        padding: 0px 10px;
        border: 1px solid $mediumLightGray;
        white-space: nowrap;
        transition: all 0.3s ease;
        position: relative;
        overflow: hidden;

        &.loading {
            cursor: not-allowed;
            opacity: 0.8;
        }

        &:disabled {
            cursor: not-allowed;
            opacity: 0.8;
        }

        .spinner {
            width: 20px;
            height: 20px;
            border: 2px solid rgba(255, 255, 255, 0.3);
            border-radius: 50%;
            border-top-color: #fff;
            animation: spin 1s ease-in-out infinite;
        }

        .hidden {
            opacity: 0;
        }

        @keyframes spin {
            to { transform: rotate(360deg); }
        }

        &:hover{
            filter: brightness(1.05);
            transform: translateY(-1px);
        }

        &:active{
            filter: brightness(.95);
            transform: translateY(0px);
        }
    }

    &.light-bg{
        button{
            &:hover{
                filter: brightness(.95);
                transform: translateY(-1px);
            }

            &:active{
                filter: brightness(.90);
                transform: translateY(0px);
            }
        }
    }

    &.yellow{
        button{
            background-color: $yellow;
            color: #000;
            border: 1px solid $yellow;
        }
    }

    &.start {
        height: 65px;
        button {
            background-color: $yellow;
            color: #000;
            border-radius: 18px;
        }
    }

    &.blue{
        button{
            background-color: $blue;
            color: #fff;
            border: 1px solid $blue;

            .spinner {
                border: 2px solid rgba(255, 255, 255, 0.3);
                border-top-color: #fff;
            }
        }
    }

    &.red{
        button{
            background-color: $red;
            color: #fff;
            border: 1px solid $red;
        }
    }

    &.disabled{
        button{
            background-color: $lightestGray;
            color: $mediumDarkGray;
        }
    }

    &.green{
        button{
            background-color: $green;
            color: #fff;
            border: 1px solid $green;
        }
    }

    &.purple{   
        button{
            background-color: #9D3CFE;
            color: #fff;
            border: 1px solid #9D3CFE;
        }
    }

    &.cyan{
        button{
            background-color: #3CE1D6;
            color: #fff;
            border: 1px solid #3CE1D6;

        }
    }

    &.no-padding{
        button{
            padding: 0;
        }
    }
}