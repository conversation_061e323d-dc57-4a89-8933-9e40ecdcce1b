import { ComponentFixture, TestBed } from '@angular/core/testing';

import { NewPanelStep4Component } from './new-panel-step-4.component';

fdescribe('NewPanelStep4Component', () => {
  let component: NewPanelStep4Component;
  let fixture: ComponentFixture<NewPanelStep4Component>;

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      imports: [NewPanelStep4Component]
    })
    .compileComponents();

    fixture = TestBed.createComponent(NewPanelStep4Component);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  it('should be private', ()=>{
    expect(component.panelistPoolControl.value()).toBe('private');
  })
});
