:host{
    display: flex;

    form{

        .form-step-title {
            margin-bottom: 0;
        }

        .review-container {
            display: flex;

            .review-half {
                width: 50%;
                margin-top: 5px;

                .review-section {
                        // border: 1px solid red;
                        margin-bottom: 20px;
                        display: grid;
                        grid-template-columns: 1fr;
                        gap: 20px;
                
                        .review-section-title {
                            display: flex;
                            align-items: center;
                            gap: 10px;
                            border-bottom: 1px solid var(--form-step-title-border-color);
                            ;
                            border-top: 1px solid var(--form-step-title-border-color);
                            ;
                            padding-bottom: 5px;
                            padding-top: 5px;
                            font-size: .75rem;
                            font-weight: 700;
                
                            button {
                                margin-left: auto;
                                text-decoration: underline;
                                cursor: pointer;
                                font-size: 0.6875rem;
                                font-weight: 500;
                            }
                        }
                
                        .review-section-row {
                            display: grid;
                            grid-template-columns: 120px 1fr;
                            gap: 20px;
                            font-size: .75rem;
                
                            .review-section-row-title {
                                font-weight: 500;
                            }
                
                            .review-section-row-value {
                                font-weight: 700;
                                padding-top: 0;
                                text-transform: capitalize;
                
                                .special-text {
                                    font-weight: 400;
                                    padding-top: 5px;
                                    display: flex;
                
                                    .special-text-content {
                                        padding-left: 5px;
                
                                        span {
                                            &:not(:last-child)::after {
                                                content: ',';
                                                margin-right: 4px;
                                            }
                                        }
                                    }
                                }
                            }
                        }
                
                        &:first-child .review-section-title {
                            border-top: none;
                        }
                    }
            }
        }


    }
}