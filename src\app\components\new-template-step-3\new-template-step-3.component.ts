import { Component, inject, signal, WritableSignal, HostListener } from '@angular/core';
import { CommonModule } from '@angular/common';
import { TemplateFormService } from '../../services/template-form.service';
import { ConstantsService } from '../../services/constants.service';
import { FormControl, FormArray, ReactiveFormsModule } from '@angular/forms';
import { InputSelectComponent } from '../input-select/input-select.component';
import { DndModule, DndDropEvent, EffectAllowed } from 'ngx-drag-drop';
import { firstValueFrom, Subscription } from 'rxjs';
import { CustomStepListPopoverComponent } from '../custom-step-list-popover/custom-step-list-popover.component';
import { DigitToWordPipe } from '../../pipes/digit-to-word.pipe';
import { QuestionsService } from '../../services/questions.service';
import { PopupService } from '../../services/popup.service';
import { QuestionAdapter } from '../../adapters/question.adapter';
import { PanelStep } from '../../interfaces/panel-step.interface';
import { fadeScaleInOut, fadeInOut } from '../../modules/animations/animations.module';


@Component({
  selector: 'new-template-step-3',
  standalone: true,
  imports: [
    InputSelectComponent,
    ReactiveFormsModule,
    CommonModule,
    DndModule,
    CustomStepListPopoverComponent,
    DigitToWordPipe,
  ],
  templateUrl: './new-template-step-3.component.html',
  styleUrl: './new-template-step-3.component.scss',
  animations: [fadeScaleInOut, fadeInOut]
})
export class NewTemplateStep3Component {

  templateFormService = inject(TemplateFormService);
  constantsService = inject(ConstantsService);
  questionsService = inject(QuestionsService);
  popupService = inject(PopupService);
  form = this.templateFormService.step2Form;
  consumptionMethodControl = this.form.get('consumptionMethod')! as FormControl;
  consumptionOptions = this.constantsService.consumption;
  questionOrderControl = this.form.get('questionOrder')! as FormControl;
  questionsControl: WritableSignal<FormArray> = signal(this.form.get('questions')! as FormArray);
  customStepListPopover = false;
  addStepPopover = false;
  questionAdapter = new QuestionAdapter();

  draggable = {
    // note that data is handled with JSON.stringify/JSON.parse
    // only set simple data or POJO's as methods will be lost
    data: "myDragData",
    effectAllowed: "all" as EffectAllowed,
    disable: false,
    handle: false
  };

  numberStrings = ['One', 'Two', 'Three', 'Four'];
  questionItems: any[] = [];
  initialQuestionOrder: any[] = [];
  subscription: Subscription = new Subscription();

  async ngOnInit(): Promise<void> {
    await this.getCustomQuestions();
    await this.checkQuestionsFormArray();
    this.initializeQuestionArrays();
    // Always using custom mode, no need for subscription
  }

  async getCustomQuestions(): Promise<void> {
    this.questionsService.customQuestions = await firstValueFrom(this.questionsService.getQuestions());
    console.log('customQuestions', this.questionsService.customQuestions);
    
    const adaptedDefaults = await this.questionAdapter.adaptMany(this.questionsService.defaultQuestions, this.questionAdapter.defaultToPanelStep);
    const adaptedCustoms = await this.questionAdapter.adaptMany(this.questionsService.customQuestions, this.questionAdapter.customToPanelStep);
    this.questionsService.adaptedQuestions = [...adaptedDefaults, ...adaptedCustoms];
    this.questionsService.adaptedQuestionsObject = this.questionsService.adaptedQuestions.reduce((acc: any, question: PanelStep) => {
      acc[question.id] = question;
      return acc;
    }, {});
    console.log('adaptedQuestionsObject', this.questionsService.adaptedQuestionsObject);
    return;
  }

  getDefaultQuestions(): string[] {
    let questions = this.constantsService.questions.map((q: any) => q.id);
    questions.sort((a: any, b: any) => {
      return this.constantsService.defaultQuestionOrder.indexOf(a) - this.constantsService.defaultQuestionOrder.indexOf(b);
    });
    return questions;
  }

  initializeQuestionArrays(): void {
    let questions = this.questionsControl().value;
    this.questionItems = questions;
    this.initialQuestionOrder = [...questions];
  }

  async checkQuestionsFormArray(): Promise<void> {
    let questions = this.questionsControl().value;
    console.log('Template questions:', questions);

    if (!questions || questions.length === 0) {
      this.buildQuestionsFormArray(this.getDefaultQuestions());
    } else {
      console.log('Using existing questions:', questions);
      this.buildQuestionsFormArray(questions);
    }
    return;
  }

  buildQuestionsFormArray(questions: string[]): void {
    console.log('Building questions form array:', questions);

    this.questionsControl().clear();
    questions.forEach(question => {
      this.questionsControl().push(new FormControl(question));
    });
    console.log('Questions control after build:', this.questionsControl().value);
  }

  ngOnDestroy(): void {
    if (this.subscription) {
      this.subscription.unsubscribe();
    }
  }

  onDrop(event: DndDropEvent) {
    if (event.dropEffect === 'move') {
      const fromIndex = this.questionItems.findIndex(item => item === event.data);

      // Get the drop target's data-index attribute
      const dropTarget = (event.event.target as HTMLElement).closest('.question-item');
      const toIndex = dropTarget ? parseInt(dropTarget.getAttribute('data-index') || '0', 10) : 0;

      if (fromIndex !== -1) {
        // Remove item from original position and insert at new position
        const [item] = this.questionItems.splice(fromIndex, 1);
        this.questionItems.splice(toIndex, 0, item);

        // Update the form control value to match the new order
        this.questionsControl().setValue(this.questionItems);
      }
    }
  }

  moveItemUp(index: number): void {
    if (index > 0) {
      // Swap current item with the one above it
      [this.questionItems[index], this.questionItems[index - 1]] =
      [this.questionItems[index - 1], this.questionItems[index]];

      // Update the form control value
      this.questionsControl().setValue(this.questionItems);
    }
  }

  moveItemDown(index: number): void {
    if (index < this.questionItems.length - 1) {
      // Swap current item with the one below it
      [this.questionItems[index], this.questionItems[index + 1]] =
      [this.questionItems[index + 1], this.questionItems[index]];

      // Update the form control value
      this.questionsControl().setValue(this.questionItems);
    }
  }

  removeQuestion(index: number): void {
    this.questionsControl().removeAt(index);
  }

  getQuestionName(questionId: string): string {
    if (this.constantsService.questionsObject && this.constantsService.questionsObject[questionId]) {
      return this.constantsService.questionsObject[questionId].name;
    }
    const question = this.constantsService.questions.find((q: any) => q.id === questionId);
    return question ? question.name : questionId;
  }

  openAddStepPopover(): void {
    this.addStepPopover = true;
  }

  closeAddStepPopover(): void {
    this.addStepPopover = false;
  }

  openCustomStepListPopover(): void {
    this.addStepPopover = false;
    setTimeout(() => {
      this.customStepListPopover = true;
    }, 100);
  }

  closeCustomStepListPopover(): void {
    this.customStepListPopover = false;
  }

  backToAddStepPopover(): void {
    this.customStepListPopover = false;
    setTimeout(() => {
      this.addStepPopover = true;
    }, 100);
  }

  @HostListener('document:click', ['$event'])
  onDocumentClick(event: MouseEvent) {
    // Check if click target is outside both popovers
    const addStepPopoverElement = document.querySelector('.add-step-popover');
    const customStepListElement = document.querySelector('custom-step-list-popover');

    if (
      (this.addStepPopover && addStepPopoverElement && !addStepPopoverElement.contains(event.target as Node)) ||
      (this.customStepListPopover && customStepListElement && !customStepListElement.contains(event.target as Node))
    ) {
      this.addStepPopover = false;
      this.customStepListPopover = false;
    }
  }

  addStep(): void {
    this.popupService.openCustomStepForm('new');
  }

  editStep(stepKey: any): void {
    this.popupService.openCustomStepForm('edit', stepKey);
  }

  onStepSelected(step: any) {
    console.log('step', step, this.questionsControl().value);
    this.questionsControl().push(new FormControl(step.id));
    this.closeCustomStepListPopover();
  }

  onViewStep(step: any) {
    console.log('step', step);
    this.popupService.openCustomStepForm('add', step.id);
  }
  
  
}