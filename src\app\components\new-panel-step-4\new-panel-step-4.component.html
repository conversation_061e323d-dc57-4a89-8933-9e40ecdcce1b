<div class="form-step-container">
<form [formGroup]="form">

    <div class="form-step-title">Set target panelists</div>

    <div class="input-container">
        <label for="panelist-pool-control">Panelist Pool</label>
        <input-radio-standard [control]="panelistPoolControl" [id]="'panelist-pool-control-public'" [value]="'public'" [name]="'panelist-pool-control'" [class.active]="panelistPoolControl.value === 'public'">Public - offer to all eligible panelists on platform</input-radio-standard>
        <input-radio-standard [control]="panelistPoolControl" [id]="'panelist-pool-control-private'" [value]="'private'" [name]="'panelist-pool-control'" [class.active]="panelistPoolControl.value === 'private'">Private - offer only to eligible panelists my organization has invited as affiliates</input-radio-standard>
    </div>

    <div class="input-container">
        <label for="restricted-control">Panelist Certification Restrictions</label>
        <input-radio-standard [control]="restrictedControl" [id]="'restricted-control-false'" [value]="false" [name]="'restricted-control'" [class.active]="restrictedControl.value === false">Offer to all eligible panelists on platform</input-radio-standard>
        <input-radio-standard [control]="restrictedControl" [id]="'restricted-control-true'" [value]="true" [name]="'restricted-control'" [class.active]="restrictedControl.value === true">Offer only to eligibile panelists who have achieved level</input-radio-standard>

        <div class="nested-input-container" *ngIf="restrictedControl.value">
            <svg xmlns="http://www.w3.org/2000/svg" width="16" height="32" viewBox="0 0 16 32" fill="none">
                <path d="M15.3536 28.3536C15.5488 28.1583 15.5488 27.8417 15.3536 27.6464L12.1716 24.4645C11.9763 24.2692 11.6597 24.2692 11.4645 24.4645C11.2692 24.6597 11.2692 24.9763 11.4645 25.1716L14.2929 28L11.4645 30.8284C11.2692 31.0237 11.2692 31.3403 11.4645 31.5355C11.6597 31.7308 11.9763 31.7308 12.1716 31.5355L15.3536 28.3536ZM0.5 0V22H1.5V0H0.5ZM7 28.5H15V27.5H7V28.5ZM0.5 22C0.5 25.5898 3.41015 28.5 7 28.5V27.5C3.96243 27.5 1.5 25.0376 1.5 22H0.5Z" fill="#C6C6C6"/>
            </svg>
            <input-radio-block [control]="restrictionLevelControl" [id]="'restriction-level-control-1'" [value]="'1'" [name]="'restriction-level-control'" [class.active]="restrictionLevelControl.value === '1'">1</input-radio-block>
            <input-radio-block [control]="restrictionLevelControl" [id]="'restriction-level-control-2'" [value]="'2'" [name]="'restriction-level-control'" [class.active]="restrictionLevelControl.value === '2'">2</input-radio-block>
            <input-radio-block [control]="restrictionLevelControl" [id]="'restriction-level-control-3'" [value]="'3'" [name]="'restriction-level-control'" [class.active]="restrictionLevelControl.value === '3'">3</input-radio-block>
        </div>
    </div>
</form>
</div>