<ng-container *ngIf="stepType === 'range-slider'">
    <svg xmlns="http://www.w3.org/2000/svg" width="12" height="13" viewBox="0 0 12 13" fill="none">
        <path d="M3.93625 1.25417C3.93794 1.14336 3.89463 1.03649 3.81701 0.957738C3.73939 0.878428 3.63308 0.833992 3.52227 0.833992H1.65594C0.743044 0.837367 0.00331189 1.57704 0 2.48994V4.35626C0 4.588 0.188432 4.77587 0.420178 4.77587C0.651924 4.77587 0.840356 4.588 0.840356 4.35626V2.48994C0.840356 2.27394 0.926416 2.06639 1.07942 1.91339C1.23242 1.7604 1.43997 1.67433 1.65596 1.67433H3.52228C3.75178 1.67096 3.93625 1.48367 3.93625 1.25417ZM11.5798 4.77024C11.3503 4.77024 11.163 4.58575 11.1596 4.35625V2.48993C11.1596 2.27394 11.0736 2.06639 10.9206 1.91339C10.7676 1.7604 10.56 1.67433 10.344 1.67433H8.47771C8.24596 1.67433 8.05809 1.4859 8.05809 1.25415C8.05809 1.02241 8.24596 0.833975 8.47771 0.833975H10.344C11.2569 0.83735 11.9967 1.57702 12 2.48992V4.35624C12 4.46705 11.9555 4.57336 11.8762 4.65098C11.7975 4.72861 11.6906 4.77193 11.5798 4.77024ZM10.344 12.834H8.47772C8.24597 12.834 8.0581 12.6455 8.0581 12.4138C8.0581 12.182 8.24597 11.9936 8.47772 11.9936H10.344C10.56 11.9936 10.7676 11.9076 10.9206 11.7546C11.0736 11.6016 11.1596 11.394 11.1596 11.178V9.31169C11.1596 9.07994 11.3481 8.89207 11.5798 8.89207C11.8116 8.89207 12 9.07994 12 9.31169V11.178C11.9966 12.0909 11.2569 12.8307 10.344 12.834ZM0.420181 8.89772C0.64968 8.89772 0.836975 9.0822 0.840359 9.3117V11.178C0.840359 11.6286 1.20542 11.9936 1.65596 11.9936H3.52228C3.75403 11.9936 3.9419 12.1821 3.9419 12.4138C3.9419 12.6456 3.75403 12.834 3.52228 12.834H1.65596C0.743061 12.8306 0.00332919 12.0909 1.73029e-05 11.178V9.31172C1.73029e-05 9.20091 0.0444527 9.0946 0.123764 9.01697C0.202512 8.93935 0.309377 8.89603 0.420181 8.89772ZM5.99999 2.07595C4.73835 2.07595 3.52788 2.57768 2.63583 3.46978C1.74374 4.36187 1.242 5.57239 1.242 6.83394C1.242 8.09548 1.74374 9.30605 2.63583 10.1981C3.52793 11.0902 4.73845 11.5919 5.99999 11.5919C7.26154 11.5919 8.4721 11.0902 9.36415 10.1981C10.2562 9.306 10.758 8.09548 10.758 6.83394C10.7563 5.57229 10.2546 4.36355 9.36247 3.47136C8.47038 2.57927 7.26168 2.07763 5.99999 2.07595ZM9.92386 6.83398C9.92386 7.87458 9.51044 8.87295 8.77471 9.60877C8.03898 10.3446 7.04058 10.7579 5.99992 10.7579C4.95927 10.7579 3.96095 10.3445 3.22513 9.60877C2.48932 8.87304 2.07598 7.87463 2.07598 6.83398C2.07598 5.79333 2.48941 4.79501 3.22513 4.05919C3.96086 3.32338 4.95927 2.91004 5.99992 2.91004C7.03939 2.91341 8.03616 3.3274 8.77125 4.06256C9.50642 4.79773 9.92049 5.79448 9.92386 6.83398Z" fill="black"/>
    </svg>
</ng-container>

<ng-container *ngIf="stepType === 'sensory-spectrum'">
    <svg xmlns="http://www.w3.org/2000/svg" width="12" height="12" viewBox="0 0 12 12" fill="none">
        <path d="M0.614058 7.37157C0.952959 7.3711 1.22765 7.09543 1.22812 6.75531V5.25562C1.22249 4.91926 0.949215 4.64972 0.614058 4.64972C0.279369 4.64972 0.00609594 4.91927 0 5.25562V6.75531C0.00046874 7.09543 0.275157 7.3711 0.614058 7.37157Z" fill="black"/>
        <path d="M3.30705 10.4998C3.64595 10.4998 3.92064 10.2241 3.92111 9.88356V2.12725C3.92111 1.78666 3.64595 1.51099 3.30705 1.51099C2.96815 1.51099 2.69299 1.78666 2.69299 2.12725V9.88356C2.69346 10.2237 2.96815 10.4998 3.30705 10.4998Z" fill="black"/>
        <path d="M5.99994 12C6.33884 11.9995 6.61353 11.7239 6.614 11.3837V0.627071C6.61681 0.461952 6.55353 0.30248 6.43823 0.1844C6.32245 0.0663236 6.16495 0 5.99996 0C5.83542 0 5.67746 0.0663296 5.56215 0.1844C5.44684 0.302477 5.38309 0.461952 5.3859 0.627071V11.3837C5.38637 11.7239 5.66104 11.9995 5.99994 12Z" fill="black"/>
        <path d="M8.69284 9.35346C9.03174 9.35299 9.30643 9.07732 9.3069 8.73721V3.27325C9.3069 2.93313 9.03174 2.65746 8.69284 2.65746C8.35394 2.65746 8.07878 2.93313 8.07878 3.27325V8.73769C8.07925 9.0778 8.35394 9.35299 8.69284 9.35346Z" fill="black"/>
        <path d="M11.3859 8.00382C11.7248 8.00335 11.9994 7.72768 11.9999 7.38756V4.62337C12.0027 4.45825 11.9394 4.29878 11.8241 4.1807C11.7084 4.06262 11.5509 3.9963 11.3859 3.9963C11.2213 3.9963 11.0634 4.06263 10.9481 4.1807C10.8327 4.29878 10.769 4.45825 10.7718 4.62337V7.38756C10.7723 7.72768 11.047 8.00335 11.3859 8.00382Z" fill="black"/>
      </svg>
</ng-container>

<ng-container *ngIf="stepType === 'free-form-text'">
    <svg xmlns="http://www.w3.org/2000/svg" width="14" height="12" viewBox="0 0 14 12" fill="none">
        <path d="M9.5 3.16688H3.16688C2.89376 3.16688 2.66688 3.39314 2.66688 3.66688C2.66688 3.94 2.89376 4.16688 3.16688 4.16688H9.5C9.77375 4.16688 10 3.94688 10 3.66688C10 3.38688 9.77375 3.16688 9.5 3.16688Z" fill="black"/>
        <path d="M3.16688 5.16688H7.5C7.77375 5.16688 8 5.38688 8 5.66688C8 5.94688 7.77375 6.16688 7.5 6.16688H3.16688C2.89376 6.16688 2.66688 5.94 2.66688 5.66688C2.66688 5.39314 2.89376 5.16688 3.16688 5.16688Z" fill="black"/>
        <path fill-rule="evenodd" clip-rule="evenodd" d="M11.8338 0H1.5C0.673744 0 0 0.67312 0 1.5V11.5C0 11.7 0.12 11.8869 0.30688 11.96C0.373755 11.9869 0.433755 12 0.5 12C0.626875 12 0.76 11.9469 0.853744 11.8531L3.37374 9.33313H11.8337C12.66 9.33313 13.3337 8.66001 13.3337 7.83313V1.5C13.3337 0.673125 12.66 0 11.8338 0ZM12.3338 7.83312C12.3338 8.10686 12.1069 8.33312 11.8338 8.33312H3.16688C3.10001 8.33312 3.04001 8.34687 2.97376 8.37312C2.91376 8.39999 2.86001 8.43312 2.81376 8.47999L1 10.2931V1.5C1 1.22688 1.22688 0.999995 1.5 0.999995H11.8338C12.1069 0.999995 12.3338 1.22688 12.3338 1.5V7.83312Z" fill="black"/>
    </svg>
</ng-container>

<ng-container *ngIf="stepType === 'select-one'">
    <svg xmlns="http://www.w3.org/2000/svg" width="13" height="12" viewBox="0 0 13 12" fill="none">
        <path d="M11.0915 8.6403V1.55365H10.1343L10.1338 8.6403C10.1338 8.90508 9.91952 9.11997 9.65545 9.11997H1.93722V10.0798L9.65545 10.0803C10.0363 10.0803 10.4014 9.9286 10.6711 9.65821C10.9402 9.38833 11.0915 9.02221 11.0915 8.6403Z" fill="#292727"/>
        <path d="M12.0428 3.4733V10.5599C12.0428 10.8253 11.8285 11.0401 11.5639 11.0401H3.84722V12H11.5639C12.3571 12 13 11.3553 13 10.5599V3.47328L12.0428 3.4733Z" fill="#292727"/>
        <path d="M9.02988 7.03989V0.959865C9.02988 0.429777 8.60128 0 8.07265 0H0.957229C0.428597 0 0 0.429777 0 0.959865V7.03989C0 7.57049 0.428597 8.00026 0.957229 8.00026H8.07265C8.60128 8.00026 9.02988 7.57049 9.02988 7.03989ZM4.57335 5.57284C4.08788 6.0571 3.30331 6.0571 2.81783 5.57284L1.77122 4.52642L2.44763 3.84815L3.49118 4.89458C3.60493 5.00202 3.78266 5.00202 3.89641 4.89458L6.58065 2.20295L7.25706 2.88019L4.57335 5.57284Z" fill="#292727"/>
    </svg>
</ng-container>

<ng-container *ngIf="stepType === 'multiple-choice'">
    <svg xmlns="http://www.w3.org/2000/svg" width="13" height="12" viewBox="0 0 13 12" fill="none">
        <path d="M11.0915 8.6403V1.55365H10.1343L10.1338 8.6403C10.1338 8.90508 9.91952 9.11997 9.65545 9.11997H1.93722V10.0798L9.65545 10.0803C10.0363 10.0803 10.4014 9.9286 10.6711 9.65821C10.9402 9.38833 11.0915 9.02221 11.0915 8.6403Z" fill="#292727"/>
        <path d="M12.0428 3.4733V10.5599C12.0428 10.8253 11.8285 11.0401 11.5639 11.0401H3.84722V12H11.5639C12.3571 12 13 11.3553 13 10.5599V3.47328L12.0428 3.4733Z" fill="#292727"/>
        <path d="M9.02988 7.03989V0.959865C9.02988 0.429777 8.60128 0 8.07265 0H0.957229C0.428597 0 0 0.429777 0 0.959865V7.03989C0 7.57049 0.428597 8.00026 0.957229 8.00026H8.07265C8.60128 8.00026 9.02988 7.57049 9.02988 7.03989ZM4.57335 5.57284C4.08788 6.0571 3.30331 6.0571 2.81783 5.57284L1.77122 4.52642L2.44763 3.84815L3.49118 4.89458C3.60493 5.00202 3.78266 5.00202 3.89641 4.89458L6.58065 2.20295L7.25706 2.88019L4.57335 5.57284Z" fill="#292727"/>
    </svg>
</ng-container>

<ng-container *ngIf="stepType === 'true-false'">
    <svg xmlns="http://www.w3.org/2000/svg" width="13" height="11" viewBox="0 0 13 11" fill="none">
        <path d="M6.67629 0.873846C6.36495 0.871524 6.06869 1.00158 5.86656 1.22918L2.92635 4.43766L1.81796 3.35888C1.62247 3.16263 1.35276 3.05 1.06977 3.0465C0.787394 3.04302 0.514658 3.14869 0.313748 3.33972C0.11222 3.53133 -0.000615329 3.79202 2.52407e-06 4.06432C0.000605857 4.33663 0.114644 4.59674 0.316173 4.78777L2.21916 6.63994C2.4249 6.84025 2.7079 6.94882 3.00053 6.94127C3.29256 6.93314 3.56891 6.80889 3.76198 6.59754L7.45041 2.57335C7.73038 2.27607 7.80277 1.84932 7.63564 1.48238C7.46851 1.11543 7.09261 0.877339 6.67629 0.873846Z" fill="#292727"/>
        <path d="M11.9341 5.49898C11.6227 5.50189 11.3289 5.63659 11.1304 5.8671L8.08992 9.30195C7.90348 9.50633 7.80996 9.77457 7.83107 10.0457C7.85158 10.3169 7.98433 10.5688 8.19912 10.7453C8.41452 10.9219 8.69389 11.0078 8.97566 10.984C9.25743 10.9608 9.51748 10.829 9.69729 10.6199L12.7384 7.18509C13.0123 6.88376 13.0775 6.45525 12.9037 6.09121C12.7299 5.72717 12.3504 5.49493 11.9341 5.49898Z" fill="#292727"/>
        <path d="M8.86225 5.49898C8.45197 5.50711 8.08329 5.74342 7.91797 6.10514C7.75205 6.46686 7.81902 6.8878 8.08994 7.18508L11.1304 10.6199C11.3108 10.8289 11.5709 10.9608 11.852 10.984C12.1338 11.0078 12.4138 10.9218 12.6286 10.7453C12.844 10.5688 12.9767 10.3168 12.9972 10.0457C13.0177 9.77456 12.9242 9.50631 12.7384 9.30193L9.69729 5.86708C9.49215 5.62845 9.18445 5.49261 8.86225 5.49898Z" fill="#292727"/>
        <path d="M11.1997 9.92969e-06C11.0664 0.000590654 10.9397 0.0574909 10.8546 0.156198L1.81125 10.2775C1.73161 10.3652 1.69179 10.4795 1.70023 10.5951C1.70868 10.7112 1.7654 10.8192 1.8565 10.8947C1.94822 10.9707 2.06769 11.0079 2.18775 10.9986C2.30842 10.9893 2.42005 10.9342 2.49788 10.8453L11.5406 0.724038C11.6589 0.595142 11.6878 0.411663 11.6142 0.25549C11.54 0.0993025 11.3777 -0.00114944 11.1997 9.92969e-06Z" fill="#292727"/>
    </svg>
</ng-container>

<ng-container *ngIf="stepType === 'demographic-breakdown'">
    <svg xmlns="http://www.w3.org/2000/svg" width="13" height="13" viewBox="0 0 13 13" fill="none">
        <path d="M9.15945 6.88567C9.9068 6.21682 10.3498 5.27321 10.3874 4.27114C10.4243 3.26906 10.0526 2.29502 9.35651 1.57335C8.66123 0.851638 7.70161 0.443115 6.69872 0.443115C5.69582 0.443115 4.73622 0.851638 4.04092 1.57335C3.34484 2.29506 2.97316 3.26911 3.01001 4.27114C3.04766 5.27321 3.49064 6.21682 4.23798 6.88567C3.20708 7.24692 2.31393 7.91899 1.68107 8.80892C1.04907 9.69966 0.708631 10.7642 0.707031 11.8559C0.707031 12.2052 0.98979 12.4879 1.33823 12.4879C1.68747 12.4879 1.97023 12.2052 1.97023 11.8559C1.97103 10.793 2.39397 9.77332 3.14613 9.02115C3.8983 8.26899 4.91718 7.84605 5.9809 7.84525H7.43234C8.4961 7.84605 9.51577 8.26899 10.2679 9.02115C11.0193 9.77332 11.4422 10.793 11.4438 11.8559C11.4438 12.2052 11.7266 12.4879 12.075 12.4879C12.4243 12.4879 12.707 12.2052 12.707 11.8559C12.7038 10.7617 12.361 9.6956 11.7258 8.80564C11.0898 7.91489 10.1934 7.24364 9.15923 6.88559L9.15945 6.88567ZM6.69872 1.73822C7.67596 1.73822 8.55628 2.32697 8.9304 3.22971C9.30447 4.13245 9.09781 5.1714 8.40653 5.86188C7.71605 6.55316 6.67712 6.75982 5.77436 6.38575C4.87162 6.01168 4.28287 5.13135 4.28287 4.15407C4.28287 2.82037 5.36426 1.73822 6.69872 1.73822Z" fill="black"/>
    </svg>
</ng-container>

<ng-container *ngIf="stepType === 'descriptors'">
    <svg xmlns="http://www.w3.org/2000/svg" width="14" height="13" viewBox="0 0 14 13" fill="none">
        <path d="M2.90346 12.3333C4.32394 12.3333 5.47489 11.1823 5.47489 9.76182V2.90468C5.47489 1.4842 4.32394 0.333252 2.90346 0.333252C1.48298 0.333252 0.332031 1.4842 0.332031 2.90468V9.76182C0.332031 11.1823 1.48298 12.3333 2.90346 12.3333ZM1.61775 2.90468C1.61775 2.19486 2.19364 1.61897 2.90346 1.61897C3.61328 1.61897 4.18917 2.19486 4.18917 2.90468V9.76182C4.18917 10.4716 3.61328 11.0475 2.90346 11.0475C2.19364 11.0475 1.61775 10.4716 1.61775 9.76182V2.90468Z" fill="black"/>
        <path d="M7.18917 2.26182H13.6177V3.54754H7.18917V2.26182Z" fill="black"/>
        <path d="M7.18917 5.69039H13.6177V6.97611H7.18917V5.69039Z" fill="black"/>
        <path d="M7.18917 9.11897H11.9035V10.4047H7.18917V9.11897Z" fill="black"/>
    </svg>
</ng-container>

<ng-container *ngIf="stepType === 'aroma-distribution'">
    <svg xmlns="http://www.w3.org/2000/svg" width="12" height="12" viewBox="0 0 12 12" fill="none">
        <path d="M0 0H5.38775V5.38775H0V0ZM6.61224 0V5.38775H12V0H6.61224ZM0 12H5.38775V6.61224H0V12ZM6.61224 12H12V6.61224H6.61224V12Z" fill="black"/>
    </svg>
</ng-container>