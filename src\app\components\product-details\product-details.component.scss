@import '../../../_colors';

:host{
    // host styles in styles.scss .full-screen-form
    position: fixed;
    z-index: 999998;
    width: 100%;
    height: 100%;
    top: 0;
    left: 0;
    background-color: #fff;
    display: block;
    padding: 15px;
    box-sizing: border-box;
    overflow-y: auto;

    a{
        text-decoration: none;
        color: unset;
    }

    @mixin border() {
        border: 1px solid $mediumLightGray;
        border-radius: 18px; 
    }
    
    button-standard{

        &.back-button{
            position: absolute;
            top: 15px;
            left: 15px;
        }

        span{
            margin-left: 10px;
        }
    }

    &>div{
        --panel-details-width: calc(100% - 30px);
        width: var(--panel-details-width);
        --panel-details-max-width: 932px;
        max-width: var(--panel-details-max-width);
        margin: 0 auto;
    }

    .panel-name-row{
        margin-top: 70px;
        margin-bottom: 30px;
        display: grid;
        grid-template-columns: 2fr 1fr;
        align-items: flex-start;


        .panel-name-grid-area{

            .panel-name{
                --panel-name-font-size: 1.625rem;
                font-size: var(--panel-name-font-size);
                font-weight: 700;
            }
        }

        .buttons-grid-area{
            display: flex;
            flex-direction: row;
            flex-wrap: wrap;
            justify-content: flex-end;
            align-items: center;
            gap: 10px;
            
            .target-panelists-button{
                margin: 0;

                svg{
                }

                span{}
            }

            .actions-button-container{
                position: relative;
                width: fit-content;
                height: fit-content;

                .actions-button{
                    margin: 0;

                    svg{
                    }

                    span{
                    }
                }

                popover{
                    --router-link-color: #{$darkGray};
                    --router-link-size: .9rem;
                    --router-link-hover-color: #{$blue};

                    .popover-item{
                        text-decoration: none;
                        color: var(--router-link-color);
                        font-size: var(--router-link-size);
                        transition: color 0.2s ease;
                        cursor: pointer;
                        width: fit-content;
                        white-space: nowrap;
            
                        &:hover{
                            color: var(--router-link-hover-color);
                        }
                    }
                }
            }

            .pds-button-container {
                position: relative;

                popover {
                    --router-link-color: #{$darkGray};
                    --router-link-size: .9rem;
                    --router-link-hover-color: #{$blue};

                    .popover-item {
                        text-decoration: none;
                        color: var(--router-link-color);
                        font-size: var(--router-link-size);
                        transition: color 0.2s ease;
                        cursor: pointer;
                        width: fit-content;
                        white-space: nowrap;
                        padding: 8px 16px;

                        &:hover {
                            color: var(--router-link-hover-color);
                        }
                    }
                }
            }

            .pds-button {
                min-width: 80px;
                display: flex;
                align-items: center;
                gap: 8px;

                svg {
                    width: 16px;
                    height: 16px;
                }
            }
        }
    }

    .product-info-container{
        display: grid;
        grid-template-columns: 150px 1fr;
        gap: 20px;
        margin-bottom: 60px;

        .product-image-container{
            width: 150px;
            height: 150px;
            object-fit: cover;
            border-radius: 18px;
            overflow: hidden;
            border: 1px solid $lightGray;
            background: $lightGray;

            img{
                width: 100%;
                height: 100%;
                
            }
        }

        .product-info-section{
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 10px;
            border: 1px solid $lightGray;
            padding: 20px;
            border-radius: 18px;
            font-size: .75rem;

            .subsection{
                display: flex;
                flex-direction: column;
                gap: 20px;

                .product-detail{
                    display: grid;
                    grid-template-columns: 120px 1fr;
                    gap: 20px;

                    .detail-label{
                        font-weight: 500;
                    }

                    .detail-value{
                        font-weight: 700;
                    }
                }
            }

            &.notes{
                grid-column: 2;

                .product-detail{
                    display: grid;
                    grid-template-columns: auto 1fr;
                    gap: 30px;
                    align-items: flex-start;

                    .detail-label{
                        font-weight: 500;
                    }

                    .detail-value{
                        font-weight: 700;
                    }
                }
            }
        }
    }

    .response-details-header{
        margin-bottom: 70px;

        .header-label{
            font-size: 1.125rem;
            font-weight: 700;
            // flex-basis: auto;
            // white-space: nowrap;
            // flex-grow: 1;
            // flex-basis: 100%;
        }

        .header-content{
            display: flex;
            flex-flow: row nowrap;
            margin-top: 8px;

            .arrow-container{
                margin-left: 10px;
                margin-top: -4px;
            }

            .info-container{
                margin-left: 10px;
                display: flex;
                flex-flow: row nowrap;
                align-items: center;
                border: 1px solid $lightGray;
                border-radius: 12px;
                height: 40px;

                .response-count{
                    // border-right: 1px solid $lightGray;
                    padding: 0px 15px;
                    font-size: .75rem;
                    font-weight: 600;
                    height: 100%;
                    display: flex;
                    align-items: center;
                }

                .privacy-status{
                    padding: 0px 15px;
                    font-size: .75rem;
                    font-weight: 600;
                    height: 100%;
                    display: flex;
                    align-items: center;
                }
            }
        }

        // .response-count{
        //     font-size: .75rem;
        //     font-weight: 500;
        //     color: $darkGray;
        //     flex-basis: auto;
        //     white-space: nowrap;
        // }

        // .border-line{
        //     background: $lightGray;
        //     height: 1px;
        //     width: 100%;
        //     flex-grow: 1;
        // }

        .filters-container{
            border: 1px solid red;
            flex-basis: auto;
        }
    }

    .response-details-section{
        max-width: var(--panel-details-max-width);
        width: 100%;
        margin-bottom: 90px;
        display: grid;
        grid-template-columns: 1fr 1fr;
        gap: 20px;
        justify-content: space-between;

        &.sensory-spectrum{
            display: block;

            .spectrum-container{
                width: calc(var(--panel-details-max-width) + 60px);
                margin-left: -30px;
                margin-bottom: 40px;
                margin-top: 20px;

                @media (max-width: 1024px) {
                    width: 100%;
                    overflow-x: auto;
                    margin-left: 0;
                }
            }

            .descriptor-container{
                max-width: var(--panel-details-max-width);
                width: 100%;
                grid-column: span 2;
                display: grid;
                grid-template-columns: repeat(auto-fit, minmax(380px, 1fr));
                gap: 40px;
                justify-items: center;
            }
        }

        &.range-slider{
            grid-template-columns: repeat(
                auto-fit, 
                minmax(140px, 1fr) 
                minmax(140px, 1fr) 
                minmax(140px, 1fr)
                );
            gap: 20px;

            .section-header{
                grid-column: 1 / -1;
            }
        }

        .section-header{
            grid-column: span 2;
            display: flex;
            flex-flow: row nowrap;
            align-items: center;
            justify-content: flex-start;
            gap: 10px;
            color: #292727;
            font-family: Inter;
            font-size: 0.8125rem;
            font-style: normal;
            font-weight: 600;
            line-height: normal;
            max-width: var(--panel-details-max-width);

            .step-type-label{
                font-size: 0.625rem;
                font-weight: 400;
                text-transform: uppercase;
            }

            .line{
                flex-grow: 1;
                background: $mediumLightGray;
                height: 1px;
            }
        }

        response-data-demographic-breakdown{
            grid-column: span 2;
        }

        response-data-aroma-spectrum{
            width: calc(var(--panel-details-max-width) + 60px);
        }

        response-data-descriptors{
            max-width: calc(var(--panel-details-max-width) / 2);
            height: fit-content;
        }

        response-data-aroma-category-distribution{
            max-width: calc(var(--panel-details-max-width) / 2);
            height: fit-content;
        }

        response-data-multiple-choice{
            grid-column: span 2;
        }

        response-data-true-false{
            grid-column: span 2;
        }

        response-data-comments{
            grid-column: span 2;
        }
    }

}