import { Injectable, inject } from '@angular/core';
import { PanelsService } from './panels.service';
import { ProductsService } from './products.service';
import { GroupsService } from './groups.service';
import { ProjectsService } from './projects.service';
import { TemplateFormService } from './template-form.service';
import { QuestionsService } from './questions.service';

@Injectable({
  providedIn: 'root'
})
export class PopupService {

  popups: { [key: string]: boolean } = {
    newPanel: false,
    newProduct: false,
    newGroup: false,
    newProject: false,
    newTemplate: false,
    customStep: false,
    // panelDetails: false,
    // productDetails: false
  }

  panelsService = inject(PanelsService);
  productsService = inject(ProductsService);
  groupService = inject(GroupsService);
  projectsService = inject(ProjectsService);
  templateFormService = inject(TemplateFormService);
  questionsService = inject(QuestionsService);

  constructor() { }

  addScrollLock(target?: any){
    if(target){
      target.classList.add('no-scroll');
    }else{
      document.body.classList.add('no-scroll');
    }
  }

  removeScrollLock(target?: any){
    if(target){
      target.classList.remove('no-scroll');
    }else{
      document.body.classList.remove('no-scroll');
    }
  }

  get isAnyPopupOpen(){
    return Object.values(this.popups).some(value => value);
  }

  get needPopupOverlay(){
    return false;
  }

  openCustomStepForm(event?: any, key?: any){
    this.popups['customStep'] = true;
    if(event && event==='edit'){
      this.questionsService.editing = true;
      this.questionsService.stepKey = key;
    }

    if(event && event==='add'){
      this.questionsService.editing = true;
      this.questionsService.addingExistingStep = true;
      this.questionsService.stepKey = key;
    }
  }

  closeCustomStepForm(event?: any){
    this.popups['customStep'] = false;
  }

  openNewProject(event?: any){
    this.projectsService.clearForm();
    this.projectsService.editing = false;
    // this.projectsService.setFormStep(1);
    this.popups['newProject'] = true;
    this.addScrollLock();
  }

  closeNewProject(event?: any){
    this.popups['newProject'] = false;
    // the condition here is to prevent the scroll lock from being removed when the user is on the response details page behind the edit/new popup form
    if (!this.isAnyPopupOpen) {
      this.removeScrollLock();
    }
  }

  async openNewPanel(event?: any, template?:any){
    console.log(event);
    this.panelsService.clearForm();
    if(template){
      this.panelsService.template = template;
      await this.panelsService.fillFormFromPanelObject(template);
    }

    this.panelsService.editing = false;
    this.panelsService.setFormStep(1);
    this.popups['newPanel'] = true;
    this.addScrollLock();
  }
  openNewGroup(event?: any){
    console.log(event);
    this.groupService.clearForm();
    this.groupService.editing = false;
    this.groupService.setFormStep(1);
    this.popups['newGroup'] = true;
    this.addScrollLock();
  }

  // async openPanelDetails(panel: any){
  //   this.panelsService.editingPanel = panel;
  //   let responseDetails = await this.panelsService.getPanelInsights(panel.id);
  //   this.panelsService.responseDetails = responseDetails;
  //   this.popups['panelDetails'] = true;
  //   this.addScrollLock();
  // }

  async editPanel(panel: any, step: number = 6){
    this.panelsService.editing = true;
    this.panelsService.editingPanel = panel;
    await this.panelsService.fillFormFromPanelObject(panel);
    this.panelsService.setFormStep(step);
    this.popups['newPanel'] = true;
    this.addScrollLock();
  }
  async editGroup(group: any, step: number = 1){
    this.groupService.editing = true;
    this.groupService.editingGroup = group;
    this.groupService.formHeader = 'Editing Group';
    await this.groupService.fillFormFromGroupObject(group);
    this.groupService.setFormStep(step);
    this.popups['newGroup'] = true;
    this.addScrollLock();
  }

  // async openProductDetails(product: any){
  //   this.productsService.editingProduct = product;
  //   let responseDetails = await this.productsService.getProduct(product.id);
  //   this.productsService.responseDetails = responseDetails;
  //   this.popups['productDetails'] = true;
  //   this.addScrollLock();
  // }

  async editProduct(product: any, step: number = 3){
    this.productsService.editing = true;
    this.productsService.editingProduct = product;
    await this.productsService.fillFormFromProductObject(product);
    this.productsService.setFormStep(step);
    this.popups['newProduct'] = true;
    this.addScrollLock();
  }

  closeNewPanel(event?: any){
    this.popups['newPanel'] = false;
    // the condition here is to prevent the scroll lock from being removed when the user is on the response details page behind the edit/new popup form
    if (!this.isAnyPopupOpen) {
      this.removeScrollLock();
    }
  }

  openNewProduct(event?: any){
    this.productsService.clearForm();
    this.productsService.editing = false;
    this.productsService.setFormStep(1);
    this.popups['newProduct'] = true;
    this.addScrollLock();
  }

  closeNewProduct(event?: any){
    this.popups['newProduct'] = false;
    // the condition here is to prevent the scroll lock from being removed when the user is on the response details page behind the edit/new popup form
    if (!this.isAnyPopupOpen) {
      this.removeScrollLock();
    }
  }

  closeNewGroup(event?: any){
    this.popups['newGroup'] = false;
    // the condition here is to prevent the scroll lock from being removed when the user is on the response details page behind the edit/new popup form
    if (!this.isAnyPopupOpen) {
      this.removeScrollLock();
    }
  }

  async openNewTemplate(event?: any, template?: any) {
    console.log('Opening new template form');
    this.templateFormService.clearForm();

    if (template) {
      this.templateFormService.editingTemplate = template;
      await this.templateFormService.fillFormFromTemplateObject(template);
    }

    this.templateFormService.editing = false;
    this.templateFormService.setFormStep(1);
    this.popups['newTemplate'] = true;
    this.addScrollLock();
  }

  closeNewTemplate(event?: any) {
    this.popups['newTemplate'] = false;
    if (!this.isAnyPopupOpen) {
      this.removeScrollLock();
    }
  }

  async editTemplate(template: any, step: number = 5) {
    this.templateFormService.editing = true;
    this.templateFormService.editingTemplate = template;
    await this.templateFormService.fillFormFromTemplateObject(template);
    this.templateFormService.setFormStep(step);
    this.popups['newTemplate'] = true;
    this.addScrollLock();
    return this.templateFormService.updatedTemplate;
  }

  closeAllPopups(event?: any){
    Object.keys(this.popups).forEach((key) => this.popups[key] = false);
    this.removeScrollLock();
  }
}
