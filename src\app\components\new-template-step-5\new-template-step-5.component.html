<div class="form-step-container">
<form [formGroup]="form">
    <div class="form-step-title">Configure template budget</div>

    <div class="input-container">
        <label>Pay per template completion</label>

        <div class="nested-input-container" *ngIf="customPayControl.value === false" [@fadeShrink]>
            <input-radio-block *ngFor="let option of templatePayOptions; let i = index" [control]="payControl" [id]="'pay-control-' + i" [value]="option.value" [name]="'pay-control'" [class.active]="payControl.value === option.value" [type]="'radio'">{{option.label}}</input-radio-block>
            <input-radio-block class="custom" [control]="customPayControl" [id]="'pay-control-custom'" [name]="'pay-control-custom'" [class.active]="customPayControl.value === true" [type]="'checkbox'">Custom</input-radio-block>
        </div>

        <div class="nested-input-container custom" *ngIf="customPayControl.value === true" [@fadeShrink]>
            <svg (click)="removeCustomPay()" xmlns="http://www.w3.org/2000/svg" width="10" height="10" viewBox="0 0 10 10" fill="none">
            </svg>
            <span>Custom Pay</span>
            <span class="currency">$</span>
            <input-text [control]="payControl" [id]="'custom-pay-control'" [placeholder]="'Enter custom pay amount'" [inputMode]="'decimal'"></input-text>
        </div>
    
            </div>

            <div class="input-container" *ngIf="payControl.value === 'custom'">
                <label>Custom pay</label>
            </div>

    <div class="input-container">
        <label>Maximum allowed spend</label>
        <div class="nested-input-container">
            <span class="currency">$</span>
            <input-text [control]="maxSpendControl" [id]="'max-spend-control'" [placeholder]="'Enter max spend amount'" [inputMode]="'decimal'"></input-text>
        </div>
    </div>

    <div class="spend-preview-container">
        <div class="spend-preview-header">With this budget configuration, you will be capped at {{maxResponses}} responses.</div>
        <div class="spend-preview-body">
            <div class="spend-preview-body-item">
                <div class="spend-preview-body-item-label">Total Spend</div>
                <div class="spend-preview-body-item-value">${{maxSpendControl.value || 0}}</div>
            </div>

            <div class="spend-preview-body-item">
                <svg width="10" height="12" viewBox="0 0 10 12" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path d="M9.97159 4.97585V7.38352H0.0284092V4.97585H9.97159ZM5 11.8438C4.52178 11.8438 4.11458 11.6757 3.77841 11.3395C3.44224 11.0033 3.27415 10.5961 3.27415 10.1179C3.27415 9.65388 3.44224 9.25616 3.77841 8.92472C4.11458 8.59328 4.52178 8.42756 5 8.42756C5.46402 8.42756 5.86174 8.59328 6.19318 8.92472C6.52462 9.25616 6.69034 9.65388 6.69034 10.1179C6.69034 10.5961 6.52462 11.0033 6.19318 11.3395C5.86174 11.6757 5.46402 11.8438 5 11.8438ZM5 3.93892C4.68277 3.93892 4.39394 3.8608 4.13352 3.70455C3.87311 3.5483 3.66477 3.33996 3.50852 3.07955C3.35227 2.81913 3.27415 2.5303 3.27415 2.21307C3.27415 1.74432 3.44224 1.34659 3.77841 1.01989C4.11932 0.688447 4.52652 0.522727 5 0.522727C5.46875 0.522727 5.86648 0.688447 6.19318 1.01989C6.52462 1.34659 6.69034 1.74432 6.69034 2.21307C6.69034 2.69129 6.52462 3.09848 6.19318 3.43466C5.86648 3.77083 5.46875 3.93892 5 3.93892Z" fill="black"/>
                </svg>
            </div>

            <div class="spend-preview-body-item">
                <div class="spend-preview-body-item-label">Pay Per Template</div>
                <div class="spend-preview-body-item-value">${{payControl.value || 0}}</div>
            </div>

            <div class="spend-preview-body-item">
                <svg width="10" height="8" viewBox="0 0 10 8" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path d="M0.0923295 3.08239V0.561079H9.90057V3.08239H0.0923295ZM0.0923295 7.7983V5.27699H9.90057V7.7983H0.0923295Z" fill="black"/>
                </svg>                
            </div>

            <div class="spend-preview-body-item">
                <div class="spend-preview-body-item-label">Max. Responses</div>
                <div class="spend-preview-body-item-value">{{maxResponses}}</div>
            </div>
        </div>
    </div>
</form>
</div>
