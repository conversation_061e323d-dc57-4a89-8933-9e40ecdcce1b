<div class="sub-section-header">
    <step-type-icon [stepType]="'descriptors'"></step-type-icon>
    <span>Descriptors</span>
    <div class="line"></div>
    <div class="toggle-tabs">
        <button 
          class="tab-button" 
          [class.active]="activeTab === 'top'"
          (click)="setActiveTab('top')">
          Top Descriptors
        </button>
        
        <button 
          class="tab-button" 
          [class.active]="activeTab === 'all'"
          (click)="setActiveTab('all')">
          All Descriptors
        </button>
    </div>
</div>

<response-data-top-descriptors *ngIf="activeTab==='top'" [responseDetails]="responseDetails"></response-data-top-descriptors>
<lamb-table class="descriptors-table" *ngIf="activeTab==='all'" [styles]="tableStyles" [headers]="headerData" [data]="bodyData" [clickableRows]="false" (rowClick)="onRowClick($event)"></lamb-table>

