import { CanActivateFn, Router } from '@angular/router';
import { inject } from '@angular/core';
import { Auth, onAuthStateChanged } from '@angular/fire/auth';
import { Observable, of } from 'rxjs';
import { catchError, take } from 'rxjs/operators';
import { ApiService } from '../services/api.service';
// import { RedirectService } from '../services/redirect.service';

export const redirectLoggedInGuard: CanActivateFn = (route, state) => {
  console.log(route);

  const auth = inject(Auth);
  const router = inject(Router);
  const apiService = inject(ApiService);
  // const redirectService = inject(RedirectService);
  // const token = route.queryParams['token'];

  // console.log('token', token);

  return new Observable<boolean>(subscriber => {
    // apiService.checkSession().pipe(
    //   catchError(() => of(false))
    // ).subscribe(hasValidSession => {
    //   if (hasValidSession) {
    //     // Valid session exists, allow access
    //     console.log('has valid session');
    //     router.navigate(['/']);
        subscriber.next(true);
        subscriber.complete();
    //     return;
    //   }
    //   console.log('no valid session');
    //   onAuthStateChanged(auth, (user) => {
    //     if (!user) {
    //       console.log('onAuthStateChanged', route, state);
    //       console.log('hello, no user');
    //       subscriber.next(true);
    //     } else {
    //       console.log('hello, has user');
    //       router.navigate(['/']); // or your login route
    //       subscriber.next(false);
    //     }
    //     subscriber.complete();
    //   });
    // });
  });
};
