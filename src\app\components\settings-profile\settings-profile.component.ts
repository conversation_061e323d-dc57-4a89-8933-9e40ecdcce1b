import { Component, inject } from '@angular/core';
import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>} from '@angular/common';
import { RouterLink } from '@angular/router';
import { ButtonStandardComponent } from '../button-standard/button-standard.component';
import { FormBuilder, FormGroup, ReactiveFormsModule, FormControl } from '@angular/forms';
import { ProfileService } from '../../services/profile.service';
import { InputTextComponent } from '../input-text/input-text.component';
import { InputRadioBlockComponent } from '../input-radio-block/input-radio-block.component';
import { UsersTableComponent } from '../users-table/users-table.component';
// import { ApiService } from '../../services/api.service';
// import { ToastService } from '../../services/toast.service';
// import { firstValueFrom } from 'rxjs';

@Component({
  selector: 'settings-profile',
  standalone: true,
  imports: [RouterLink, ButtonStandardComponent, ReactiveFormsModule, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, InputTextComponent, InputRadioBlockComponent, UsersTableComponent],
  templateUrl: './settings-profile.component.html',
  styleUrl: './settings-profile.component.scss'
})
export class SettingsProfileComponent {

  saveButtonText: string = 'No Changes';
  saveState = 0;
  profileService = inject(ProfileService);
  fb = inject(FormBuilder);
  profileForm: FormGroup = this.fb.group({
    first_name: new FormControl(this.profileService.profileData?.first_name),
    last_name: new FormControl(this.profileService.profileData?.last_name),
    org_name: new FormControl(this.profileService.profileData?.org_name),
    org_type: new FormControl(this.profileService.profileData?.org_type),
  });

  firstNameControl = this.profileForm.get('first_name')! as FormControl;
  lastNameControl = this.profileForm.get('last_name')! as FormControl;
  orgNameControl = this.profileForm.get('org_name')! as FormControl;
  orgTypeControl = this.profileForm.get('org_type')! as FormControl;

  // apiService = inject(ApiService);
  
  // demographics: any[] = [];
  // sortedDemographics: any = {};
  initialFormValues: any;
  // toastService = inject(ToastService);
  async ngOnInit(){
  //   await this.getDemographics();
  //   this.sortDemographics();
  //   for(let key in this.profileService.profileData.demographics){
  //     this.profileForm.get(key)?.setValue(this.profileService.profileData.demographics[key]);
  //   }
    this.initialFormValues = this.profileForm.value;
    this.profileForm.valueChanges.subscribe(currentValues => {
      if (JSON.stringify(currentValues) !== JSON.stringify(this.initialFormValues)) {
        this.saveState = 1;
        this.saveButtonText = 'Save Changes';
      } else {
        this.saveState = 0;
        this.saveButtonText = 'No Changes';
      }
    });

  }

  // async getDemographics(){
  //   const res: any = await this.apiService.getDemographics();
  //   this.demographics = res;
  // }

  // sortDemographics(){
  //   this.demographics.forEach((item: any) => {
  //     let type = item.type;
  //     if(!this.sortedDemographics[type]){
  //       this.sortedDemographics[type] = [];
  //     }
  //     this.sortedDemographics[type].push(item);
  //   });
  //   Object.keys(this.sortedDemographics).forEach((key: string) => {
  //     let arr = this.sortedDemographics[key];
  //     arr.sort((a: any, b: any) => {
  //       return a.priority - b.priority;
  //     });
  //   });
  //   console.log(this.sortedDemographics);
  // }


  async saveProfile(){
    console.log('saveProfile');
    
  //   try{
  //     let payload: any = {"demographics": {}};
  //     for(let key in this.profileForm.value){
  //       if(key !== 'first_name' && key !== 'last_name'){
  //         console.log(key, this.profileForm.get(key)?.value);
  //         payload.demographics[key] = this.profileForm.get(key)?.value;
  //       } else {
  //         payload[key] = this.profileForm.get(key)?.value;
  //       }
  //     }
  //     const res: any = await firstValueFrom(this.profileService.updateProfile(payload));
  //     this.saveState = 2;
  //     this.saveButtonText = 'Saved!';
  //     this.toastService.goodToast('Profile updated successfully!');
  //     setTimeout(() => {
  //       this.saveState = 0;
  //       this.saveButtonText = 'No Changes';
  //     }, 2000);
  //   } catch (error: any) {
  //     this.toastService.burntToast('An error occurred during profile update');
  //   }
  }
}
