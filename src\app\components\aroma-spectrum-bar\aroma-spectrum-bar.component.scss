@import '../../../_colors.scss';

:host{
    display: block;
    position: relative;

    --bar-width: 7px;    
    --color: #d8d8d8;
    --initial: transparent;
    --text-color: #000000;
    
    .initial-bar{
        height: 100%;
        width: 100%;
        position: absolute;
        bottom: 0;
        left: 0;
        background-color: var(--initial);
    }

    .fill-bar{
        height: 50%;
        position: absolute;
        width: 100%;
        bottom: 0;
        left: 0;
        background-color: var(--color);

        &:hover{
            lamb-graph-tooltip{
                opacity: 1;
                z-index: 1000000;
                transition: opacity 0.3s ease, z-index 0s 0s;
            }
        }

        lamb-graph-tooltip{
            opacity: 0;
            z-index: -1;
            transition: opacity 0.3s ease, z-index 0s .3s;
            top: auto;
            bottom: calc(100% + 8px);
            left: 4px;
            transform: translateX(-50%);
        }
    }
}

.descriptor-tooltip {
  max-width: 200px;
  padding: 8px;
  
  .descriptor-info {
    h4 {
      margin: 0 0 4px 0;
      font-weight: 600;
    }
    
    p {
      margin: 0 0 8px 0;
      font-size: 0.9em;
    }
    
    .intensity {
      font-size: 0.8em;
      color: #666;
    }
  }
}
