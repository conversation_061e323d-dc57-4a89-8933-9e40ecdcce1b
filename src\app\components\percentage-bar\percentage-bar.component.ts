import { Component, Input } from '@angular/core';
import { CommonModule } from '@angular/common';

@Component({
  selector: 'percentage-bar',
  standalone: true,
  imports: [CommonModule],
  templateUrl: './percentage-bar.component.html',
  styleUrl: './percentage-bar.component.scss'
})
export class PercentageBarComponent {
  @Input() pieces: any[] = [];

  ngOnInit(){
    // console.log(this.pieces);
  }
}
