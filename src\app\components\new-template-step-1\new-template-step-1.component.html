<div class="form-step-container">
    <form [formGroup]="form">
      <div class="form-step-title">Start by entering template details below</div>

      <div class="input-container" [class.error-shake]="templateFormService.formErrors['step1']['experiment_type']">
        <label for="experiment-type">Experiment Type</label>
        <input-select [control]="experimentTypeControl" [id]="'experiment-type'" [options]="experimentTypeOptions" [placeholder]="'Select experiment type'"></input-select>
      </div>

      <div class="input-container" [class.error-shake]="templateFormService.formErrors['step1']['name']">
        <label for="template-name">Template Name</label>
        <input-text [control]="nameControl" [id]="'template-name'" [placeholder]="'template name'"></input-text>
      </div>

      <div class="input-container textarea-container" [class.error-shake]="templateFormService.formErrors['step1']['description']">
        <label for="template-description">Template Description</label>
        <input-text-area [control]="descriptionControl" [id]="'template-description'" [placeholder]="'template description'"></input-text-area>
      </div>

      <div class="input-container radio-container">
        <label>Start</label>
        <input-radio-block [control]="startOptionControl" [id]="'start-option-control-now'" [value]="'now'" [name]="'start-option-control'" [class.active]="startOptionControl.value === 'now'">Immediately</input-radio-block>
        <input-radio-block [control]="startOptionControl" [id]="'start-option-control-custom'" [value]="'custom'" [name]="'start-option-control'" [class.active]="startOptionControl.value === 'custom'">Custom Start Date</input-radio-block>
      </div>

      <div class="input-container date-picker-container" *ngIf="startOptionControl.value === 'custom'" [class.error-shake]="templateFormService.formErrors['step1']['startDate']">
        <svg xmlns="http://www.w3.org/2000/svg" width="18" height="34" viewBox="0 0 18 34" fill="none">
          <path d="M17.3536 30.3536C17.5488 30.1583 17.5488 29.8417 17.3536 29.6464L14.1716 26.4645C13.9763 26.2692 13.6597 26.2692 13.4645 26.4645C13.2692 26.6597 13.2692 26.9763 13.4645 27.1716L16.2929 30L13.4645 32.8284C13.2692 33.0237 13.2692 33.3403 13.4645 33.5355C13.6597 33.7308 13.9763 33.7308 14.1716 33.5355L17.3536 30.3536ZM0.5 0V20H1.5V0H0.5ZM11 30.5H17V29.5H11V30.5ZM0.5 20C0.5 25.799 5.20101 30.5 11 30.5V29.5C5.75329 29.5 1.5 25.2467 1.5 20H0.5Z" fill="#9D9D9D"/>
        </svg>
        <input-bs-datepicker [control]="startDateControl"></input-bs-datepicker>
      </div>

      <div class="input-container radio-container">
        <label>End</label>
        <input-radio-block [control]="endOptionControl" [id]="'end-option-control-never'" [value]="'never'" [name]="'end-option-control'" [class.active]="endOptionControl.value === 'never'">No End Date</input-radio-block>
        <input-radio-block [control]="endOptionControl" [id]="'end-option-control-custom'" [value]="'custom'" [name]="'end-option-control'" [class.active]="endOptionControl.value === 'custom'">Custom End Date</input-radio-block>
      </div>

      <div class="input-container date-picker-container" *ngIf="endOptionControl.value === 'custom'" [class.error-shake]="templateFormService.formErrors['step1']['endDate']">
        <svg xmlns="http://www.w3.org/2000/svg" width="18" height="34" viewBox="0 0 18 34" fill="none">
          <path d="M17.3536 30.3536C17.5488 30.1583 17.5488 29.8417 17.3536 29.6464L14.1716 26.4645C13.9763 26.2692 13.6597 26.2692 13.4645 26.4645C13.2692 26.6597 13.2692 26.9763 13.4645 27.1716L16.2929 30L13.4645 32.8284C13.2692 33.0237 13.2692 33.3403 13.4645 33.5355C13.6597 33.7308 13.9763 33.7308 14.1716 33.5355L17.3536 30.3536ZM0.5 0V20H1.5V0H0.5ZM11 30.5H17V29.5H11V30.5ZM0.5 20C0.5 25.799 5.20101 30.5 11 30.5V29.5C5.75329 29.5 1.5 25.2467 1.5 20H0.5Z" fill="#9D9D9D"/>
        </svg>
        <input-bs-datepicker [control]="endDateControl"></input-bs-datepicker>
      </div>
    </form>
  </div>