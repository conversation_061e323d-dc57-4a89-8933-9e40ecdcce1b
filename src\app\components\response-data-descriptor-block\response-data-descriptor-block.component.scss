@import '../../../_colors';

:host{
    --descriptor-block-bg: #{$lightestGray};
    --descriptor-block-border: #{$mediumGray};
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 30px 18px 40px;
    background-color: var(--descriptor-block-bg);
    border-radius: 18px;
    // border: 2px solid var(--descriptor-block-border);

    .color-bar{
        width: 25px;
        height: 7px;
        border-radius: 4px;
        background-color: var(--descriptor-block-border);
        margin-bottom: 15px;
        opacity: 0.75;
    }

    .descriptor-name{
        font-size: 0.875rem;
        font-weight: 700;
        margin-bottom: 18px;
        text-transform: capitalize;
        text-align: center;
    }

    .descriptor-avg{
        margin-bottom: 10px;
        font-size: .75rem;
        font-weight: 500;
        line-height: 1;
        display: flex;
        align-items: baseline;
        text-align: center;


        span{
            font-size: 1.125rem;
            font-weight: 700;

        }
    }

    .sub-text{
        font-size: 0.75rem;
        font-weight: 500;
        text-align: center;
    }
}