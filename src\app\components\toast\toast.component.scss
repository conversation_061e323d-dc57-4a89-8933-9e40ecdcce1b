@import "../../../styles.scss";

:host{
  z-index: 9999999;
  display: flex;
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 45px;
  box-sizing: border-box;
  justify-content: center;
  transform: translateY(30px);

  .toast-inner {
    background-color: $green;
    display: flex;
    align-items: center;
    justify-content: center;
    min-width: 50px;
    max-width: 300px;
    width: fit-content;
    padding: 0 20px;
    border-radius: 12px;
    color: #fff;
    font-weight: 700;
    font-size: .9rem;
    text-align: center;

    &.burnt {
      background-color: $red;
    }
  }
}