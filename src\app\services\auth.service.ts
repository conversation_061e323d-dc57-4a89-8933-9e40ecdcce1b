import { Injectable, inject } from '@angular/core';
import { signInWithPopup, Auth, GoogleAuthProvider, sendEmailVerification, sendPasswordResetEmail, signOut, signInWithEmailAndPassword, UserCredential} from '@angular/fire/auth';
import { Router } from '@angular/router';
import { ApiService } from './api.service';

@Injectable({
  providedIn: 'root'
})
export class AuthService {

  loading = false;

  auth = inject(Auth);
  router = inject(Router);
  apiService = inject(ApiService);

  constructor() { }

  async googleSignIn() {
    try {
      const provider = new GoogleAuthProvider();
      const result = await signInWithPopup(this.auth,provider);
      return result;
    } catch (error) {
      console.error('Error during sign in:', error);
      throw error;
    }
  }

  async sendVerificationEmail(): Promise<void> {
    return await sendEmailVerification(this.auth.currentUser!);
  }
  
  async sendPasswordResetEmail(): Promise<void> {
    return await sendPasswordResetEmail(this.auth, this.auth.currentUser!.email!);
  }

  async login(email: string, password: string): Promise<UserCredential> {
    return await signInWithEmailAndPassword(this.auth, email, password);
  }

  logout(): Promise<void> {
    // First, call your backend to clear the HttpOnly session cookie
    return this.apiService.logout()
    .then(() => {
      // Then proceed with Firebase logout
      return signOut(this.auth);
    })
    .then(() => {
      this.router.navigate(['login']);
    })
    .catch((error: any) => {
      // Handle any errors during the sign out process
      console.error('Error during logout:', error);
    });
  }
}
