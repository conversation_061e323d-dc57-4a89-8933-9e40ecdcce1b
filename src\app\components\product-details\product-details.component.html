<button-standard class="back-button" (onAction)="goBack()">
    <svg xmlns="http://www.w3.org/2000/svg" width="16" height="13" viewBox="0 0 16 13" fill="none">
        <path d="M15.2134 5.54389H2.66023L6.93207 1.33322C7.24094 1.03018 7.24248 0.535256 6.93668 0.230712C6.63088 -0.0753749 6.13144 -0.0768952 5.82413 0.226144L0.198231 5.75837C0.161351 5.7934 0.129081 5.83299 0.0998831 5.87563V5.89847C0.0752961 5.9335 0.056854 5.97157 0.0399538 6.01116V6.03857C0.0399538 6.07816 0.0122934 6.11623 0.00461006 6.15582C-0.00153669 6.2076 -0.00153669 6.25938 0.00461006 6.31115V6.35074C0.00307347 6.38882 0.00307347 6.42537 0.00461006 6.46343C0.00461006 6.49846 0.0245871 6.53044 0.0322704 6.56089C0.035344 6.57764 0.035344 6.5944 0.0322704 6.61115L0.0829826 6.70404L0.106032 6.74363H0.10757C0.13523 6.78322 0.165964 6.82129 0.198234 6.85784L5.79935 12.4283C6.10976 12.7085 6.58766 12.6978 6.88424 12.4039C7.18082 12.11 7.19465 11.638 6.91344 11.3288L2.64907 7.10294H15.2132C15.6481 7.10294 16 6.75421 16 6.32325C16 5.8923 15.6482 5.54389 15.2134 5.54389Z" fill="black"/>
    </svg>
    <span>{{backButtonText}}</span>
</button-standard>

<div class="panel-name-row">
    <div class="panel-name-grid-area">
        <div class="panel-name">{{ product.name }}</div>
    </div>
    <div class="buttons-grid-area">
        <button-standard class="target-panelists-button" (onAction)="showAssociatedPanels()"><svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 16 16" fill="none">
            <path d="M11.1942 8C11.1942 9.76448 9.76439 11.1942 8 11.1942C6.23561 11.1942 4.80584 9.76439 4.80584 8C4.80584 6.23561 6.23561 4.80584 8 4.80584C9.76439 4.80584 11.1942 6.23561 11.1942 8Z" fill="black"/>
            <path d="M14.0898 7.4029C13.8215 4.50723 11.4926 2.20877 8.59709 1.91023V0H7.4029V1.91023C4.50723 2.17845 2.20877 4.50735 1.91023 7.4029H0V8.59709H1.91023C2.17845 11.4928 4.50735 13.7912 7.4029 14.0898V16H8.59709V14.0898C11.4928 13.8215 13.7912 11.4926 14.0898 8.59709H16V7.4029H14.0898ZM8 12.926C5.28274 12.926 3.07396 10.7173 3.07396 8C3.07396 5.28274 5.28274 3.07396 8 3.07396C10.7173 3.07396 12.926 5.28274 12.926 8C12.926 10.7173 10.7173 12.926 8 12.926Z" fill="black"/>
          </svg><span>Associated Panels</span></button-standard>
        <div class="pds-button-container">
            <button-standard class="pds-button" (onAction)="togglePDSPopover()">
                <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 16 16" fill="none">
                    <path d="M13.3333 2H2.66667C2.29848 2 2 2.29848 2 2.66667V13.3333C2 13.7015 2.29848 14 2.66667 14H13.3333C13.7015 14 14 13.7015 14 13.3333V2.66667C14 2.29848 13.7015 2 13.3333 2Z" stroke="black" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
                    <path d="M5.33334 2V14" stroke="black" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
                    <path d="M10.6667 2V14" stroke="black" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
                    <path d="M2 5.33334H14" stroke="black" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
                    <path d="M2 10.6667H14" stroke="black" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
                </svg>
                <span>PDS</span>
            </button-standard>
            <popover *ngIf="showPDSPopover" [@fadeScale]>
                <div class="popover-item" (click)="fetchPDS('Basic')">Basic</div>
                <div class="popover-item" (click)="fetchPDS('Advanced')">Advanced</div>
            </popover>
        </div>
        <div class="actions-button-container">
            <button-standard class="actions-button no-padding" (onAction)="toggleActionsPopover()">
                <svg xmlns="http://www.w3.org/2000/svg" width="39" height="39" viewBox="0 0 39 39" fill="none">
                    <path d="M21.5 20.1111C21.5 21.277 20.6045 22.2222 19.5 22.2222C18.3955 22.2222 17.5 21.277 17.5 20.1111C17.5 18.9452 18.3955 18 19.5 18C20.6045 18 21.5 18.9452 21.5 20.1111Z" fill="black"/>
                    <path d="M14 20.1111C14 21.277 13.1045 22.2222 12 22.2222C10.8955 22.2222 10 21.277 10 20.1111C10 18.9452 10.8955 18 12 18C13.1045 18 14 18.9452 14 20.1111Z" fill="black"/>
                    <path d="M29 20.1111C29 21.277 28.1045 22.2222 27 22.2222C25.8955 22.2222 25 21.277 25 20.1111C25 18.9452 25.8955 18 27 18C28.1045 18 29 18.9452 29 20.1111Z" fill="black"/>
                </svg>
            </button-standard>
            <popover *ngIf="showActionsPopover" [@fadeScale]>
                <div class="popover-item" (click)="popupService.editProduct(product, 3)">Edit</div>
                <div class="popover-item" (click)="printProductLabel()">Print Product Label</div>
                <div class="popover-item" (click)="deleteProduct(product)">Delete</div>
            </popover>
        </div>
    </div>
</div>

<div class="product-info-container">
    <div class="product-image-container">
        <img [src]="product.image" alt="An image of the product.">
    </div>
    <div class="product-info-section product-details">
        <div class="subsection">
            <div class="product-detail">
                <div class="detail-label">Product Name</div>
                <div class="detail-value">"{{ product.name || 'Unknown' }}"</div>
            </div>

            <div class="product-detail">
                <div class="detail-label">Product Description</div>
                <div class="detail-value">"{{ product.description || 'Unknown' }}"</div>
            </div>

            <div class="product-detail">
                <div class="detail-label">Product Code</div>
                <div class="detail-value">{{ product.external_id || 'Unknown' }}</div>
            </div>

            <div class="product-detail">
                <div class="detail-label">Product Type</div>
                <div class="detail-value">{{ product.product_type || 'Unknown' }}</div>
            </div>

            <div class="product-detail">
                <div class="detail-label">Product Subtype</div>
                <div class="detail-value">{{ product.product_subtype || 'Unknown' }}</div>
            </div>
        </div>

        <div class="subsection">
            <div class="product-detail">
                <div class="detail-label">Packaging Container</div>
                <div class="detail-value">{{ product.packaging_option || 'Unknown' }}</div>
            </div>

            <div class="product-detail">
                <div class="detail-label">Lot Number</div>
                <div class="detail-value">{{ product.lot_number || 'Unknown' }}</div>
            </div>

            <div class="product-detail">
                <div class="detail-label">Manufacture Date</div>
                <div class="detail-value">{{ product.manufacturing_date || 'Unknown' }}</div>
            </div>

            <div class="product-detail">
                <div class="detail-label">Package Date</div>
                <div class="detail-value">{{ product.packaging_date || 'Unknown' }}</div>
            </div>

            <div class="product-detail">
                <div class="detail-label">Producer</div>
                <div class="detail-value">{{ product.producer || 'Unknown' }}</div>
            </div>
        </div>
    </div>

    <div class="product-info-section notes">
        <div class="product-detail">
            <div class="detail-label">Notes</div>
            <div class="detail-value">{{ product.notes || 'No notes provided' }}</div>
        </div>
    </div>
</div>

<div class="response-details-header">
    <div class="header-label">Collected Data Across All Panels</div>
    <div class="header-content">
        <div class="arrow-container">
            <svg xmlns="http://www.w3.org/2000/svg" width="16" height="28" viewBox="0 0 16 28" fill="none">
                <path d="M15.3536 24.3536C15.5488 24.1583 15.5488 23.8417 15.3536 23.6464L12.1716 20.4645C11.9763 20.2692 11.6597 20.2692 11.4645 20.4645C11.2692 20.6597 11.2692 20.9763 11.4645 21.1716L14.2929 24L11.4645 26.8284C11.2692 27.0237 11.2692 27.3403 11.4645 27.5355C11.6597 27.7308 11.9763 27.7308 12.1716 27.5355L15.3536 24.3536ZM0.5 0V18H1.5V0H0.5ZM7 24.5H15V23.5H7V24.5ZM0.5 18C0.5 21.5899 3.41015 24.5 7 24.5V23.5C3.96243 23.5 1.5 21.0376 1.5 18H0.5Z" fill="#C6C6C6"/>
            </svg>
        </div>
        <div class="info-container">
            <div class="response-count">{{responseDetails.count}} Responses</div>
            <!-- <div class="privacy-status">{{privacyStatus}}</div> -->
        </div>
    </div>
    <!-- <div class="response-count">{{responseDetails.count}} Responses</div> -->
    <!-- <div class="border-line"></div> -->
    <!-- <div class="filters-container">To do</div> -->
</div>

<div class="response-details-section demographic-breakdown">
    <div class="section-header">
        <step-type-icon [stepType]="'demographic-breakdown'"></step-type-icon>
        <span>Respondent Demographic Breakdown</span>
        <div class="line"></div>
    </div>
    <response-data-demographic-breakdown [responseDetails]="responseDetails"></response-data-demographic-breakdown>
</div>

<div class="response-details-section" *ngFor="let step of panelSteps" [ngClass]="step.type">
        <div class="section-header">
            <step-type-icon [stepType]="step.type"></step-type-icon>
            <span>{{step.name}}</span>
            <span class="step-type-label" *ngIf="step.type === 'multiple-choice' || step.type === 'select-one' || step.type === 'true-false'">{{getStepTypeLabel(step.type)}}</span>
            <div class="line"></div>
        </div>

        <ng-container *ngIf="step.type === 'range-slider'">
            <response-data-overall-block *ngFor="let metric of constantsService.metrics" [metric]="metric" [responseDetails]="responseDetails"></response-data-overall-block>
        </ng-container>

        <ng-container *ngIf="step.type === 'sensory-spectrum'">
            <div class="spectrum-container">
                <response-data-aroma-spectrum [responseDetails]="responseDetails"></response-data-aroma-spectrum>
            </div>
            
            <div class="descriptor-container">
                <response-data-descriptors [responseDetails]="responseDetails" [service_type]="'panel'"></response-data-descriptors>
                <response-data-aroma-category-distribution [responseDetails]="responseDetails" [csvFileName]="replaceSpacesWithUnderscores(product.name) + '-product-aroma-distribution'"></response-data-aroma-category-distribution>
            </div>
        </ng-container>

        <ng-container *ngIf="step.type === 'free-form-text'">
            <response-data-comments [step]="step" [comments]="route.snapshot.data['comments']"></response-data-comments>
        </ng-container>

        <ng-container *ngIf="step.type === 'select-one' || step.type === 'multiple-choice'">
            <response-data-multiple-choice [responseDetails]="responseDetails" [step]="step"></response-data-multiple-choice>
        </ng-container>

        <ng-container *ngIf="step.type === 'true-false'">
            <response-data-true-false [responseDetails]="responseDetails" [step]="step"></response-data-true-false>
        </ng-container>
</div>
