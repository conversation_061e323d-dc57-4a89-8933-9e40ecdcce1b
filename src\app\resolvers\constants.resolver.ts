import { inject } from '@angular/core';
import { ResolveFn } from '@angular/router';
import { ConstantsService } from '../services/constants.service';
import { LoadingService } from '../services/loading.service';
import { firstValueFrom } from 'rxjs';
import { finalize } from 'rxjs/operators';

export const constantsResolver: ResolveFn<any> = (route, state) => {
  const constantsService = inject(ConstantsService);
  const loadingService = inject(LoadingService);

  loadingService.setLoading('constants', true);

  return constantsService.getConstants()
    .then((res: any) => {
      console.log('constants resolved', res);
      return res;
    })
    .finally(() => {
      loadingService.setLoading('constants', false);
    });
};
