@import '../../../_colors';

:host {
  display: block;
  height: 100%;
}

.sub-section-header {
  display: flex;
  flex-flow: row nowrap;
  align-items: center;
  justify-content: flex-start;
  gap: 10px;
  color: #292727;
  font-family: Inter;
  font-size: 0.8125rem;
  font-style: normal;
  font-weight: 600;
  line-height: normal;
  height: 40px;

  .line{
      flex-grow: 1;
      background: $mediumLightGray;
      height: 1px;
  }
  
  .toggle-tabs {
    margin: 0;
    height: 40px;
    border: 1px solid $mediumLightGray;
    border-radius: 12px;
    background-color: $lightGray;

    .tab-button {
      border: none;
      background: none;
      padding: 6px 12px;
      border-radius: 11px;
      cursor: pointer;
      font-size: 14px;
      font-weight: 500;
      color: #292727;
      transition: all 0.3s ease;
      height: 100%;
      display: inline-flex;
      align-items: center;
      justify-content: center;
      
      &.active {
        background-color: #007bff;
        color: white;
        box-shadow: 0px 4px 7px 0px rgba(0, 0, 0, 0.10);
      }
      
      &:hover:not(.active) {
        background-color: #e6e6e6;
      }
    }
  }
}

.descriptors-table {
  margin-top: 20px;
  width: 100%;
  max-height: 250px;
  overflow-y: auto;
  
  &::-webkit-scrollbar {
    width: 8px;
  }
  
  &::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 4px;
  }
  
  &::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 4px;
  }
  
  &::-webkit-scrollbar-thumb:hover {
    background: #a8a8a8;
  }
}
