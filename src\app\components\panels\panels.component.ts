import { Component, inject, OnInit } from '@angular/core';
import { AsyncPipe, NgIf } from '@angular/common';
import { PageHeaderComponent } from '../page-header/page-header.component';
// import { PanelsTableComponent } from '../panels-table/panels-table.component';
import { PaginationComponent } from '../pagination/pagination.component';
import { PanelsService } from '../../services/panels.service';
import { PopupService } from '../../services/popup.service';
import { ActivatedRoute, Router } from '@angular/router';
import { Observable, map, switchMap, tap, share } from 'rxjs';
import { ProfileService } from '../../services/profile.service';
import { LoadingService } from '../../services/loading.service';
import { RouterModule } from '@angular/router';
import { LambTableComponent, HeaderCell, TableCell } from '@lamb-sensory/lamb-component-library';
import { PanelInterface } from '../../interfaces/panel.interface';
import { TABLE_STYLES } from '../../config/table-styles.config';
import { ApiService } from '../../services/api.service';
import { PanelAdapter } from '../../adapters/panel.adapter';
import { PopoverComponent } from '../popover/popover.component';
@Component({
  selector: 'panels',
  standalone: true,
  imports: [PopoverComponent ,PageHeaderComponent, PaginationComponent, AsyncPipe, NgIf, RouterModule, LambTableComponent ],
  templateUrl: './panels.component.html',
  styleUrl: './panels.component.scss'
})
export class PanelsComponent implements OnInit {
  isPopoverOpen = false

  panelsService = inject(PanelsService);
  popupService = inject(PopupService);
  profileService = inject(ProfileService);
  route = inject(ActivatedRoute);
  loadingService = inject(LoadingService);
  router = inject(Router);
  apiService = inject(ApiService);

  // panels$ = this.panelsService.panels$;
  // tableConfig: any;
  tableHeaders: HeaderCell[] = [
    {
      value: 'Status',
      key: 'status'
    },
    {
      value: 'Name',
      key: 'panelName'
    },
    {
      value: 'Product Name',
      key: 'productName'
    },
    {
      value: 'Privacy',
      key: 'privacy'
    },
    {
      value: 'Scheduled End',
      key: 'scheduledEnd'
    },
    {
      value: '# Responses',
      key: 'responses'
    }
  ];

  tableStyles = TABLE_STYLES;
  // Shared source Observable
  private panelsSource$!: Observable<any>;
  panels$!: Observable<any[]>;
  // tableConfig!: TableConfig;
  panelsArray: any[] = [];
  paginationState$!: Observable<any>;

  ngOnInit() {
    this.loadingService.setLoading('panels', true);
        // Create a shared source observable
    this.panelsSource$ = this.panelsService.panels$.pipe(
      tap(() => this.loadingService.setLoading('panels', false)),
      share()
    );
    
    // Derive panels$ from the shared source
    this.panels$ = this.panelsSource$.pipe(
      switchMap(async (data) => {
        this.panelsArray = data['panels'];
        this.panelsService.panelsArray = await this.getTableData(data['panels']);
        // this.tableConfig = await this.configureTable(data['panels']);
        return data['panels'];
      })
    );
    
    // Derive paginationState$ from the same shared source
    this.paginationState$ = this.panelsSource$.pipe(
      map(response => ({
        currentPage: response.currentPage,
        totalPages: response.totalPages,
        hasMore: response.hasMore
      }))
    );
  }

  async getTableData(data: any){
    const adapter = new PanelAdapter(this.apiService);
    let adaptedData = await adapter.adapt(data);
    console.log(adaptedData);
    return adaptedData;
  }

  onPageChange(page: number) {
    this.panelsService.setPage(page);
  }

  onRowClick(event: any){
    this.router.navigate([`/panels/${event.cell.data.panelId}`]);
  }

  onNewPanelOptionClick(option: string){
    this.isPopoverOpen = false;
    if (option === 'New Panel'){
      this.popupService.openNewPanel(option);
    } else if (option === 'Use a Template'){
      this.router.navigate(['/templates']);
    }
  }
}
