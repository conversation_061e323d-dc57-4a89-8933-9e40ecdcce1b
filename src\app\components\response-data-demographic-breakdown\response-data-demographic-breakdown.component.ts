import { Component, Input, inject, ViewContainerRef, ComponentRef, ViewChild, ElementRef } from '@angular/core';
import { CommonModule } from '@angular/common';
import { ConstantsService } from '../../services/constants.service';
import { PercentageBarComponent } from '../percentage-bar/percentage-bar.component';
import { GraphTooltipComponent, GraphTooltipConfig } from '@lamb-sensory/lamb-component-library';
import { fadeInOut } from '../../modules/animations/animations.module';

@Component({
  selector: 'response-data-demographic-breakdown',
  standalone: true,
  imports: [CommonModule, 
    PercentageBarComponent,
    GraphTooltipComponent
  ],
  templateUrl: './response-data-demographic-breakdown.component.html',
  styleUrl: './response-data-demographic-breakdown.component.scss',
  animations: [fadeInOut]
})
export class ResponseDataDemographicBreakdownComponent {

  @Input() responseDetails: any;
  // @ViewChild('tooltipContainer', { read: ViewContainerRef }) tooltipContainer!: ViewContainerRef;

  
  constantsService = inject(ConstantsService);
  demographicsBars: any[] = [];  
  tooltipConfig: GraphTooltipConfig = {
    mainText: {
      content: 'test'
    },
  }
  private tooltipRef: ComponentRef<GraphTooltipComponent> | null = null;
  private viewContainerRef = inject(ViewContainerRef);

  ngOnInit(){
    this.getDemographics();
  }

  ngOnDestroy() {
    this.destroyTooltip();
  }

  destroyTooltip(){
    if (this.tooltipRef) {
      this.tooltipRef.destroy();
      this.tooltipRef = null;
    }
  }
  
  createTooltip(event: MouseEvent, index: number) {
    const target = event.target as HTMLElement;
    
    // Return early if not clicking on a percentage bar piece
    if (!target.classList.contains('percentage-bar-piece')) {
      this.destroyTooltip();
      return;
    }

    // Get all percentage bar pieces and find index of target
    const pieces = target.parentElement?.children || [];
    const pieceIndex = Array.from(pieces).indexOf(target);

    const leftInset = target.offsetLeft + (target.offsetWidth / 2);

    const tooltipConfig: GraphTooltipConfig = {
      mainText: {
        content: this.demographicsBars[index].options[pieceIndex].demographic.name
      },
      subtext: {
        content: Math.round(this.demographicsBars[index].options[pieceIndex].value) + '%',
        show: true
      },
      legend: {
        show: true,
        style: {
          background: this.demographicsBars[index].options[pieceIndex].color || '#c5c5c5'
        }
      },
      style: {
        insetLeft: leftInset + 'px',
        insetBottom: 'calc(100% + 3px)',
        insetTop: 'auto',
        borderRadius: '8px'
      }
    };

    // Destroy existing tooltip if any
    this.destroyTooltip();

    // Create new tooltip
    this.tooltipRef = this.viewContainerRef.createComponent(GraphTooltipComponent);
    this.tooltipRef.instance.config = tooltipConfig;
    this.tooltipRef.location.nativeElement.style.transform = 'translateX(-50%)';

    target.appendChild(this.tooltipRef.location.nativeElement);

    return;
  }

  getDemographics(){
    let totalCount = this.responseDetails.count;
    let sortingObj: any = {};
    this.constantsService.demographics.forEach((demographic: any) => {
      if(demographic.option_id != "xZO4lo4MQP7qKzIpJW1F" && this.responseDetails.hasOwnProperty(demographic.option_id + '_count')){
        if(!sortingObj.hasOwnProperty(demographic.type)){
          sortingObj[demographic.type] = {
            count: 0,
            options: []
          }
        }
        sortingObj[demographic.type].count += this.responseDetails[demographic.option_id + '_count'];
        sortingObj[demographic.type].options.push({demographic, count: this.responseDetails[demographic.option_id + '_count']});
      }
    });

    this.demographicsBars = Object.keys(sortingObj).map((type: string) => {
      // console.log('sortingObj[type]', sortingObj[type]);
      sortingObj[type].options.sort((a: any, b: any) => b.count - a.count);
      let unknownCount = totalCount - sortingObj[type].count;
      if(unknownCount > 0){
        sortingObj[type].options.push({demographic: {name: 'Unknown', option_id: 'unknown'}, count: unknownCount});
      }

      sortingObj[type].options.forEach((option: any) => {
        option.value = (option.count / totalCount) * 100;
        option.color = option.demographic.color;
      })

      return {
        type,
        options: sortingObj[type].options
      }
    })

    // console.log('demographicsBars', this.demographicsBars);

  }

  replaceDashWithSpace(str: string){
    return str.replace(/-/g, ' ');
  }

}
