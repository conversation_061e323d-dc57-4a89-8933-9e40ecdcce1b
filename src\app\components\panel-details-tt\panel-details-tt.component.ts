import { Component, Input } from '@angular/core';
import { TriangleTestAggregate, TriangleTestData } from '../../interfaces/triangleTest.interface';
import { CommonModule, KeyValuePipe, NgFor, NgIf } from '@angular/common';

@Component({
  selector: 'panel-details-tt',
  standalone: true,
  imports: [KeyValuePipe, CommonModule],
  templateUrl: './panel-details-tt.component.html',
  styleUrl: './panel-details-tt.component.scss'
})
export class PanelDetailsTtComponent {

  @Input() panel: any;
  @Input() triangleTestAggregates: TriangleTestAggregate | null = null;
  @Input() summerizedTTData: Record<string, TriangleTestData> = {};



    getTriangleTestProducts(id: any) {
      let pair = id.split('_');
  
      let productsNames: any[] = [];
      console.log(pair, this.panel.productsDetail[0]);
      pair.forEach((id: any) => {
        productsNames.push(
          this.panel.productsDetail?.find(
            (product: any) => product.product_id === id
          )
        );
      });
      console.log(productsNames, this.panel.productsDetail);
      return productsNames.map((prod) => prod.name);
    }

    getInitial(name: string) {
      return name[0].toLocaleUpperCase();
    }

    expandForDetails(id: any) {
      console.log(this.summerizedTTData[id]);
      this.summerizedTTData[id].expanded = !this.summerizedTTData[id].expanded;
    }
    isExpanded(id: string) {
      return this.summerizedTTData[id].expanded;
    }

      /**
   * Format p-value for display
   * @param pValue The p-value to format
   * @returns Formatted p-value string
   */
  formatPValue(pValue: number | null): string {
    if (pValue === null) return 'N/A';
    // For very small p-values, show as < 0.001
    if (pValue < 0.001) {
      return '< 0.001';
    }
    // For very large p-values, show as > 0.99
    if (pValue > 0.99) {
      return '> 0.99';
    }
    // Special handling for common threshold values
    if (pValue === 0.05) {
      return '0.050';
    }
    if (pValue === 0.01) {
      return '0.010';
    }
    // For all other values, show 3 decimal places
    return pValue.toFixed(3);
  }

  /**
   * Determine if a test result is statistically significant
   * @param pValue The p-value to check
   * @returns true if significant (p <= 0.05), false otherwise
   */
  isSignificant(pValue: number | null): boolean {
    if (pValue === null) return false;
    return pValue <= 0.05;
  }

  /**
   * Calculate the percentage of correct responses
   */
  getCorrectPercentage(correct: number, total: number): number {
    if (total === 0) return 0;
    return Math.round((correct / total) * 100);
  }

  /**
   * Calculate the percentage of incorrect responses
   */
  getIncorrectPercentage(correct: number, total: number): number {
    if (total === 0) return 0;
    const incorrect = total - correct;
    return Math.round((incorrect / total) * 100);
  }

  /**
   * Generate the CSS conic gradient for the donut chart based on correct/incorrect percentages
   */
  getDonutGradient(correct: number, total: number): string {
    if (total === 0) return 'conic-gradient(#6788e8 0% 100%)';

    const incorrectPercentage = this.getIncorrectPercentage(correct, total);

    return `conic-gradient(
      #6788e8 0% ${incorrectPercentage}%,
      #ffc55c ${incorrectPercentage}% 100%
    )`;
  }
}
