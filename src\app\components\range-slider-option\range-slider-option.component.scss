@import '../../../_colors';
:host{
    display: block;
    margin-bottom: 30px;
    box-shadow: 0px 4px 11px 0px rgba(0, 0, 0, 0.10);
    padding: 20px;
    border-radius: 12px;

    input-text,
    input-text-area{
        width: 100%;
    }

    .option-header{
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 30px;

        button.remove-option-button{
            margin-right: 10px;
            opacity: .4;
            cursor: pointer;
            transition: opacity .2s ease-in-out;

            &:hover{
                opacity: 1;
            }
        }

        .option-container-label{
            margin-right: auto;
            font-size: .75rem;
            font-weight: 700;
        }
    }

    .input-container{

        &:last-child{
            margin-bottom: 0;
        }

        .option-container{
            display: flex;
            gap: 10px;
            align-items: center;
            grid-column: 2;
            position: relative;

            // .option-label{
            //     border-right: 1px solid #E5E5E5;
            //     padding: 10px 10px 10px 0;
            //     font-size: 0.75rem;
            //     font-weight: 500;
            //     white-space: nowrap;
            //     width: 70px;
            //     text-align: right;
            // }

            input-text{
                width: calc(100% - 80px);
            }
        }
    }
}