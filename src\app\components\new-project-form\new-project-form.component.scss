@import '../../../_colors';

:host{
    // host styles in styles.scss .full-screen-form
    position: fixed;
    z-index: 999999;
    
    button-two-click{
        position: absolute;
        top: 15px;
        left: 15px;

        svg{
            margin-right: 10px;
        }
    }

    .form-header{
        font-size: var(--form-header-font-size);
        font-weight: var(--form-header-font-weight);
        color: var(--form-header-color);
        display: grid;
        grid-template-columns: 1fr;
        justify-content: space-between;
        align-items: center;
        margin: var(--form-header-margin-top) var(--form-header-margin-right) var(--form-header-margin-bottom) var(--form-header-margin-left);
        width: 100%;
        max-width: var(--form-header-max-width);
    }

    form{
        width: 800px;

        .input-container{
            display: grid;
            align-items: center;

            &.name{
                grid-template-columns: var(--input-container-label-width) 1fr;

                input-text{
                    width: 100%;
                }
            }

            &.radio-container{
                grid-template-columns: var(--input-container-label-width) 1fr 1fr;

                input-radio-block{
                    width: 100%;
                }
            }

            &.date-picker-container{
                grid-template-columns: var(--input-container-label-width) 1fr 1fr;
                margin-top: -15px;


                svg{
                    grid-column: 2;
                    justify-self: end;
                    top: -12px;
                    right: -50px;
                    position: relative;
                }

                input-bs-datepicker{
                    justify-self: left;
                    grid-column: 3;
                    right: -35px;
                    position: relative;
                    // margin-right: 10px;
                }
            }

            &.collaborators{
                grid-template-columns: var(--input-container-label-width) 1fr;

                .collaborators-container{
                    display: flex;
                    align-items: center;
                    gap: 10px;
                    position: relative;

                    .no-collaborators{
                        font-size: 0.6875rem;
                        color: #838383;
                        font-weight: 500;
                        padding: 3px 6px;
                        border-radius: 12px;
                        background-color: #F7F7F7;
                        border: 1px solid #D8D8D8;
                    }

                    .collaborator-item{
                        
                    }

                    // .button-container{

                        button{
                            cursor:pointer;
                        }

                        .collaborators-modal{
                            position: absolute;
                            width: 267px;
                            height: 287px;
                            background-color: #fff;
                            box-shadow: 0px 4px 10px 0px rgba(0, 0, 0, 0.25);
                            border-radius: 12px;
                            padding: 15px;
                            z-index: 999999;
                            overflow: auto;
                        }
                    // }
                }
            }

            &.table-input-container{
                grid-template-columns: var(--input-container-label-width) 1fr;
                align-items: flex-start;

                label.associated-panels{
                    display: grid;
                    grid-template-columns: auto 1fr;
                    align-items: flex-start;

                    span{
                        grid-column: span 2;
                        margin-bottom: 5px;
                    }

                    .arrow-container{
                        padding: 0px 5px 0px 10px;

                        svg{}
                    }

                    .selected-panels-list{
                        margin-top: 3px;

                        .selected-item{
                            font-size: .75rem;
                            font-weight: 700;
                            width: fit-content;
                            text-align: center;
                            padding: 5px 10px;
                            border-radius: 8px;
                            margin-bottom: 5px;
                        
                            &.panel{
                                background-color: #CBE5FD;
                                color: #00519C;
                            }

                            &.group{
                                background: #CEFFFC;
                                color: #10837B;
                            }
                        }
                    }
                }

                .table-outer-container{
                    border: 1px solid #D8D8D8;
                    background-color: #F7F7F7;
                    border-radius: 12px;
                    padding: 15px;
                    overflow: auto;
                    height: fit-content;

                    .table-select-container{
                        display: grid;
                        grid-template-columns: 1fr 1fr;
                        gap: 0px;
                        border-radius: 12px;
                        margin-bottom: 10px;
                        height: 50px;

                        &.panels{
                        background-color: #CBE5FD;

                            input-radio-block{
                                color: #00519C;

                                &.active{
                                    background-color: #0084FF;
                                }
                            }
                        }

                        &.groups{
                            background-color: #CEFFFC;

                            input-radio-block{
                                color: #10837B;

                                &.active{
                                    background-color: #10837B;
                                }
                            }
                        }

                        input-radio-block{
                            width: 100%;
                            height: 100%;
                            outline: none;
                            background-color: transparent;

                            &.active{
                                color: #fff;
                            }
                        }
                    }

                    .table-name-and-search-container{
                        display: grid;
                        align-items: center;
                        grid-template-columns: 1fr auto auto;
                        margin-bottom: 10px;

                        .table-name{
                            font-size: 0.875rem;
                            font-weight: 700;
                            color: #292727;
                        }
                        
                        .search-container{
                            height: 50px;
                        }

                        .filter-container{
                            height: 50px;
                        }
                        
                    }

                    .table-inner-container{
                        max-height: 500px;
                        overflow: auto;

                        lamb-table{
                            margin-bottom: 10px;
                        }

                        .load-more-btn {
                            margin-top: 1rem;
                            padding: 0.5rem 1rem;
                            color: #292727;
                            border: none;
                            border-radius: 4px;
                            cursor: pointer;
                            width: 100%;
                            text-align: center;
                          
                            &:hover {
                                text-decoration: underline;
                            }
                        }
                    }
                }
            }
        }
    }

}

.select-cell{

    input{
        width: 16px;
            height: 16px;
            background-color: #fff;
            border: 2px solid #fff;
            outline: $inputBorder;
            border-radius: 50%;
            margin-right: 10px;
            cursor: pointer;
            flex-shrink: 0;
    
            &:checked {
                background-color: $blue;
            }
    
            &:focus {
                outline-color: $blue;
            }
    }
}

.loading-spinner {
    display: flex;
    justify-content: center;
    align-items: center;
    width: 30px;
    margin: 0 auto;
    
    .spinner {
      animation: rotate 2s linear infinite;
      width: 20px;
      height: 20px;
      
        & .path {
            stroke: currentColor;
            stroke-linecap: round;
            animation: dash 1.5s ease-in-out infinite;
        }
    }
}

@keyframes rotate {
  100% {
    transform: rotate(360deg);
  }
}

@keyframes dash {
  0% {
    stroke-dasharray: 1, 150;
    stroke-dashoffset: 0;
  }
  50% {
    stroke-dasharray: 90, 150;
    stroke-dashoffset: -35;
  }
  100% {
    stroke-dasharray: 90, 150;
    stroke-dashoffset: -124;
  }
}