import { Component, OnInit, inject } from '@angular/core';
import { <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, NgIf } from '@angular/common';
import { PanelsService } from '../../services/panels.service';
import { ProductsService } from '../../services/products.service';
import { FormControl, FormGroup, ReactiveFormsModule } from '@angular/forms';
import { InputRadioBlockComponent } from '../input-radio-block/input-radio-block.component';
import { ProductBlockComponent } from '../product-block/product-block.component';
import { map, tap } from 'rxjs';
import { PaginationComponent } from '../pagination/pagination.component';
import { TriangleTestService } from '../../services/triangle-test.service';

@Component({
  selector: 'new-panel-step-2-tt',
  standalone: true,
  imports: [
    InputRadioBlockComponent,
    ReactiveFormsModule,
    NgFor,
    NgIf,
    ProductBlockComponent,
    AsyncPipe,
    PaginationComponent
  ],
  templateUrl: './new-panel-step-2-tt.component.html',
  styleUrl: './new-panel-step-2-tt.component.scss'
})
export class NewPanelStep2TtComponent implements OnInit {
  panelsService = inject(PanelsService);
  productsService = inject(ProductsService);
  triangleTestService = inject(TriangleTestService);

  form: FormGroup;
  productsControl: FormControl;
  seedControl: FormControl; // For storing the seed value
  selectedProducts: string[] = [];
  
  // Triangle test data
  triangleTests: any[] = [];
  
  // Seed for consistent triangle test generation
  seed: number;

  products$ = this.productsService.products$.pipe(
    tap(response => {
      if (response?.products) {
        this.products = response.products;
      }
    })
  );

  products: any[] = [];

  paginationState$ = this.products$.pipe(
    map(response => ({
      currentPage: response.currentPage,
      totalPages: response.totalPages,
      hasMore: response.hasMore
    }))
  );

  constructor() {
    this.form = this.panelsService.step2FormTt;
    this.productsControl = this.form.get('products') as FormControl;
    
    // Get or initialize the seed control
    if (!this.form.get('seed')) {
      // If seed control doesn't exist yet, create it and add to the form
      this.seed = this.generateRandomSeed();
      this.seedControl = new FormControl(this.seed);
      this.form.addControl('seed', this.seedControl);
    } else {
      // If seed control already exists, use its value
      this.seedControl = this.form.get('seed') as FormControl;
      this.seed = this.seedControl.value || this.generateRandomSeed();
      // Update the control with the seed if it wasn't set
      if (!this.seedControl.value) {
        this.seedControl.setValue(this.seed);
      }
    }

    // Initialize selected products from form value
    const initialValue = this.productsControl.value;
    this.selectedProducts = Array.isArray(initialValue) ? [...initialValue] : [];

    // Initialize validation state and generate tests if products are already selected
    this.updateValidationState();
    this.generateTriangleTests();
  }
  
  /**
   * Generates a random seed for triangle test generation
   */
  private generateRandomSeed(): number {
    // Generate a random number between 1 and 1,000,000
    return Math.floor(Math.random() * 1000000) + 1;
  }

  ngOnInit() {
    this.productsService.setPage(1);

    // Subscribe to value changes in the form control
    this.productsControl.valueChanges.subscribe((value: string[]) => {
      this.selectedProducts = Array.isArray(value) ? [...value] : [];
      this.updateValidationState();
      this.generateTriangleTests();
    });
  }

  /**
   * Generates triangle tests for selected products
   */
  generateTriangleTests(): void {
    if (this.selectedProducts.length >= 2) {
      // Generate triangle tests using the service with the saved seed
      this.triangleTests = this.triangleTestService.generateTriangleTests(this.selectedProducts, this.seed);
      console.log(`Generated triangle tests with seed ${this.seed}:`, this.triangleTests);
      
      // Store the triangle test data in the panels service for later use
      this.panelsService.triangleTestData = {
        tests: this.triangleTests,
        seed: this.seed
      };
    } else {
      this.triangleTests = [];
      this.panelsService.triangleTestData = null;
    }
  }

  /**
   * Toggles the selection of a product
   */
  toggleProductSelection(productId: string, event?: Event): void {
    // Prevent default if event is provided (to stop input-radio-block's default behavior)
    if (event) {
      event.preventDefault();
      event.stopPropagation();
    }

    // Check if product is already selected
    const isSelected = this.isProductSelected(productId);
    console.log(`Toggling product ${productId}, currently selected: ${isSelected}`);

    if (isSelected) {
      // Remove product from selection
      this.selectedProducts = this.selectedProducts.filter(id => id !== productId);
    } else if (this.selectedProducts.length < 3) {
      // Add product to selection if less than 3 are selected
      this.selectedProducts = [...this.selectedProducts, productId];
    } else {
      // Already have 3 products selected, don't add more
      console.log('Already have 3 products selected, not adding more');
      return;
    }

    console.log('New selection:', this.selectedProducts);

    // Update the form control with the new selection
    this.productsControl.setValue(this.selectedProducts);
    this.updateValidationState();
    this.generateTriangleTests();
  }

  /**
   * Checks if a product is currently selected
   */
  isProductSelected(productId: string): boolean {
    return this.selectedProducts.includes(productId);
  }

  /**
   * Updates the validation state based on the current selection
   */
  private updateValidationState(): void {
    const productCount = this.selectedProducts.length;
    const isValid = productCount >= 2 && productCount <= 3;
    this.panelsService.formErrors['step2']['products'] = !isValid;

    // Update error message if needed
    if (!isValid) {
      this.panelsService.hasError = true;
      if (productCount < 2) {
        this.panelsService.errorMessage = 'Please select at least two products for the triangle test';
      } else if (productCount > 3) {
        this.panelsService.errorMessage = 'Please select no more than three products for the triangle test';
      }
    } else {
      this.panelsService.hasError = false;
      this.panelsService.errorMessage = '';
    }

    console.log(`Validation state updated: ${isValid ? 'valid' : 'invalid'}, selected count: ${this.selectedProducts.length}`);
  }

  /**
   * Handles page changes in the pagination component
   */
  onPageChange(page: number): void {
    this.productsService.setPage(page);
  }

  /**
   * Gets the product name by ID
   */
  getProductName(productId: string): string {
    const product = this.productsService.productsObject[productId];
    return product ? product.name : 'Unknown Product';
  }

  /**
   * Gets the product type/matrix by ID
   */
  getProductType(productId: string): string {
    const product = this.productsService.productsObject[productId];
    if (!product) return 'Unknown';

    // For now, just return the matrix ID as a string
    // In a real implementation, you would fetch the matrix name from a service
    return product.matrix || 'Unknown';
  }

  /**
   * Gets the sample codes for a product
   * @param productId The ID of the product
   * @returns Array of sample codes assigned to this product
   */
  getSampleCodes(productId: string): string[] {
    if (this.triangleTests.length === 0) {
      return ['A', 'B']; // Fallback if no tests generated
    }

    // Find all sample codes assigned to this product from all triangle tests
    const sampleCodes = new Set<string>();
    
    this.triangleTests.forEach(test => {
      Object.entries(test.trueMapping).forEach(([code, id]) => {
        if (id === productId) {
          sampleCodes.add(code);
        }
      });
    });
    
    return Array.from(sampleCodes); // Return all unique sample codes
  }

  /**
   * Gets the sample letter for a product
   * @param productIndex The index of the product in the selected products array
   * @param letterIndex The index of the letter for this product (0 or 1 for two presentations)
   * @deprecated Use getSampleCodes instead which returns all unique codes
   */
  getSampleLetter(productIndex: number, letterIndex: number): string {
    if (this.selectedProducts.length < 2 || this.triangleTests.length === 0) {
      // Fallback to the previous implementation if no tests are generated
      const sampleLetters = ['A', 'B', 'C', 'D', 'E', 'F'];
      const baseIndex = productIndex * 2;
      const actualIndex = baseIndex + letterIndex;
      return sampleLetters[actualIndex] || 'X';
    }
    
    // Get the product ID from the selected products
    const productId = this.selectedProducts[productIndex];
    if (!productId) return 'X';
    
    // Find sample codes for this product
    const sampleCodes = this.getSampleCodes(productId);
    
    // Return the appropriate code or a fallback
    return sampleCodes[letterIndex] || 'X';
  }
}
