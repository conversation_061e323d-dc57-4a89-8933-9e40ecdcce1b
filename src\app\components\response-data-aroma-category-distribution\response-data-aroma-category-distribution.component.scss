@import '../../../_colors';

:host {
  // max-width: calc(var(--panel-details-max-width) / 2);
  display: grid;
  --graph-size: 155px;
  grid-template-columns: var(--graph-size) 1fr;
  gap: 20px;
  position: relative;

  .sub-section-header {
    display: flex;
    flex-flow: row nowrap;
    align-items: center;
    justify-content: flex-start;
    gap: 10px;
    color: #292727;
    font-family: Inter;
    font-size: 0.8125rem;
    font-style: normal;
    font-weight: 600;
    line-height: normal;
    grid-column: span 2;
    height: 40px;

    .line{
        flex-grow: 1;
        background: $mediumLightGray;
        height: 1px;
        width: auto;
        transition: width 0.2s ease-in-out;
    }

    .export-csv-button{
      // --export-csv-button-inset: -36px 0px auto auto;
      // position: absolute;
      inset: var(--export-csv-button-inset);
      cursor: pointer;
      transition: width 0.2s ease-in-out;
      display: grid;
      grid-template-columns: auto 1fr;
      overflow: hidden;
      align-items: center;
      justify-content: flex-start;
      // gap: 8px;
      height: 40px;
      width: 45px;
      background: $lightGray;
      border: 1px solid $mediumLightGray;
      border-radius: 12px;
      // width: 40px;
      // max-width: 40px;
      transition: width 0.2s ease-in-out;


      &:hover{
        width: 125px;

        .hover-text{
          // max-width:100%;
        }
      }

      .hover-text{
        font-size: 0.6875rem;
        font-weight: 700;
        display: inline-flex;
        align-items: center;
        flex-flow: row nowrap;
        gap: 5px;
        width: fit-content;
        // max-width: 0;
        overflow: hidden;
        justify-content: flex-end;
        // transition: max-width 0.2s ease-in-out;

        span{
          display: inline-flex;
          align-items: center;
          justify-content: center;
          padding: 2px 4px;
          border-radius: 4px;
          font-size: 0.4375rem;
          background-color: #fff;
          border: 1px solid #000;
        }
      }

      svg{
        width: 13px;
        height: 13px;
        margin-left: 15px;
        margin-right: 15px;
      }
    }
  }

  

  .chart-container {
    width: var(--graph-size);
    height: var(--graph-size);
    margin: 0 auto;
  }
  
  .categories-legend {
    margin-top: 10px;
    display: flex;
    flex-direction: column;
    gap: 12px;
  
    .category {
      --dot-size: 20px;
      display: grid;
      grid-template-columns: var(--dot-size) 1fr;
      align-items: center;
      column-gap: 8px;
      row-gap: 4px;
  
      .color-dot {
        width: var(--dot-size);
        height: var(--dot-size);
        border-radius: 5px;
        opacity: 0.70;
      }
  
      .category-info {
        display: flex;
        align-items: center;
        gap: 8px;
        font-size: .875rem;
  
        .category-name {
          font-weight: 500;
          text-transform: capitalize;
        }
  
        .percentage {
          font-weight: 700;
        }
      }

      .top-note{
        grid-column: 2;
        font-size: .75rem;
        font-weight: 500;

        span{
          text-transform: capitalize;
        }
      }
    }
  }
  
}