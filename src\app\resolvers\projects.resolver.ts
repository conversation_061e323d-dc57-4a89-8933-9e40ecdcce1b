import { inject } from "@angular/core";
import { LoadingService } from "../services/loading.service";
import { ProjectsService } from "../services/projects.service";
import { ResolveFn } from "@angular/router";

export const projectDetailsResolver: ResolveFn<any> = (route, state) => {
    const projectsService = inject(ProjectsService);
    const loadingService = inject(LoadingService);
    const projectId = route.params['id'];
    loadingService.setLoading('projects', true);
    // if(projectsService.projectsObject.hasOwnProperty(projectId)){
    //     return projectsService.projectsObject[projectId];
    // }

    const hydrateReferences = true;
    const hydrateFullDetails = true;
    return projectsService.getProjectById(projectId, hydrateReferences, hydrateFullDetails).then((project: {}) => {
        loadingService.setLoading('projects', false);
        console.log('project', project);
        return project;
    });
};