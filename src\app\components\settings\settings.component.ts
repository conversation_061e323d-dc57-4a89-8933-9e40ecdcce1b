import { Component, inject } from '@angular/core';
import { RouterModule } from '@angular/router';
import { ProfileService } from '../../services/profile.service';
import { NgIf } from '@angular/common';

@Component({
  selector: 'settings',
  standalone: true,
  imports: [RouterModule, NgIf],
  templateUrl: './settings.component.html',
  styleUrl: './settings.component.scss'
})
export class SettingsComponent {
  profileService = inject(ProfileService);
}
