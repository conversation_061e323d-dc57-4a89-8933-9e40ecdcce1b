import { Component, inject, HostListener } from '@angular/core';
import { NgIf } from '@angular/common';
import { FormBuilder, Validators, ReactiveFormsModule, FormGroup, FormControl } from '@angular/forms';
import { Router, RouterModule } from '@angular/router';
import { ToastService } from "../../services/toast.service";
import { AuthService } from "../../services/auth.service";
import { GrayButtonComponent } from "../gray-button/gray-button.component";
import { BlurEllipseComponent } from "../blur-ellipse/blur-ellipse.component";
import { HttpClient } from '@angular/common/http';
import { lastValueFrom } from 'rxjs';
import { environment } from '../../../environments/environment';
import { EnvironmentService } from '../../services/environment.service';

@Component({
  selector: 'app-signup',
  standalone: true,
  imports: [ReactiveFormsModule, RouterModule, NgIf, Gray<PERSON><PERSON>onComponent, BlurEllipseComponent],
  templateUrl: './signup.component.html',
  styleUrl: './signup.component.scss'
})
export class SignupComponent {

  http = inject(HttpClient);
  fb = inject(FormBuilder);
  authService = inject(AuthService);
  toastService = inject(ToastService);
  router = inject(Router);
  private environmentService = inject(EnvironmentService);
  errorMessage = "";
  hasError = false;
  loading = false;
  errors = {
    affiliateCodeError: false,
    firstNameError: false,
    lastNameError: false,
    emailError: false,
    passwordError: false,
    confirmPasswordError: false,
    zipCodeError: false,
    orgName: false,
    website: false,
  }

  isShow = false;
  isAffiliated = true;
  isNewOrg = false;

  // Helper method to get the current baseUrl
  private getBaseUrl(): string {
    return this.environmentService.getBaseUrl();
  }

  signupForm: FormGroup = this.fb.group({
    email: ['', [Validators.required, Validators.email]],
    password: [''],
    confirmPassword: ['' ],
    affiliateCode: [''],
    firstName: [''],
    // firstName: ['', Validators.required],
    lastName: [''],
    // lastName: ['', Validators.required],
    isBrand: [false],
    zipCode: ['', Validators.required],
    marketingOptIn: [false],
    orgName: ['',!this.isShow&&Validators.minLength(3)],
    website: ['']
  });

  showPassword = false;

  screenWidth: number = window.innerWidth;
  isMobile = this.screenWidth <= 768;

  @HostListener('window:resize', ['$event'])
  onResize() {
    this.screenWidth = window.innerWidth;
    this.isMobile = this.screenWidth <= 768;
  }

  ngOnInit(){
    this.authService.loading = false;
    this.screenWidth = window.innerWidth;
  }

  googleLogin(event: Event){
    console.log("googleLogin", event);
    this.authService.googleSignIn();
  }

  togglePasswordVisibility(){
    this.showPassword = !this.showPassword;
  }

  async resetError(){
    this.hasError = false;
    Object.keys(this.errors).forEach(key => {
      this.errors[key as keyof typeof this.errors] = false;
    });
    return;
  }

  private debounceTimer: any;

  onSubmit = () => {
    if (this.debounceTimer) {
      clearTimeout(this.debounceTimer);
    }
    
    this.debounceTimer = setTimeout(async () => {
      console.log('submit');
      if (this.signupForm.valid && !this.authService.loading) {
        const email = this.signupForm.get('email')?.value;
        let password = this.signupForm.get('password')?.value;
        const confirmPassword = this.signupForm.get('confirmPassword')?.value;
        const affiliateCode = this.signupForm.get('affiliateCode')?.value;
        const firstName = this.signupForm.get('firstName')?.value;
        const lastName = this.signupForm.get('lastName')?.value;
        const isBrand = true;
        const zipCode = this.signupForm.get('zipCode')?.value;
        const marketingOptIn = this.signupForm.get('marketingOptIn')?.value;
        const orgName = this.signupForm.get('orgName')?.value;
        const website = this.signupForm.get('website')?.value;

        if (password !== confirmPassword) {
          this.errorMessage = "Passwords do not match";
          this.hasError = true;
          return;
        }
        if (!this.isShow && !affiliateCode && (!orgName || !website)) {
          this.errorMessage = "Either affiliate code or organization name and website are required";
          this.hasError = true;
          return;
        }

        this.authService.loading = true;
        try {

          let userData ={
            email: email,
            first_name: firstName,
            last_name: lastName,
            affiliateCode: affiliateCode,
            password: password,
            zipCode: zipCode,
            isBrand: isBrand,
            marketingOptIn: marketingOptIn,
            orgName: orgName,
            website: website,
          }

          let userDataForShow ={
            email: email,
            first_name: "Show",
            last_name: "Show",
            affiliateCode: "",
            password: "00Show!00",
            zipCode: zipCode,
            isBrand: false,
            marketingOptIn: marketingOptIn,
            orgName: "Show",
            website: 'http://show-website.com',
          }
          // const userCredential = await this.authService.signup(email, password);
          // if (userCredential) {
            const res = await lastValueFrom(this.http.post(this.getBaseUrl() + '/auth/register', this.isShow ? userDataForShow : userData))
            // this.toastService.goodToast('Signup successful! You will be redirected to login.');
            // setTimeout(() => {
            //   this.router.navigate(['/login']);
            // }, 2000);
            this.toastService.goodToast('Signup successful! You will be signed in automatically');
            setTimeout(async () => {
              if(!password && this.isShow){
                password = "00Show!00";
              }
              await this.authService.login(email, password);
              this.router.navigate(['/']);
            }, 1000);
          // }
        } catch (error: any) {
          console.log('catching here', error);
          this.errorMessage = error.error || 'An error occurred during signup';
          this.hasError = true;
        } finally {
          this.authService.loading = false;
        }
      } else {
        this.authService.loading = false;
        this.errorMessage = "Please fill out all fields correctly";
        this.hasError = true;
        Object.keys(this.signupForm.controls).forEach(key => {
          const controlErrors = this.signupForm.get(key)?.errors;
          if (controlErrors) {
            console.log(`${key} validation errors:`, controlErrors);
            switch(key){
              case "firstName":
                this.errors.firstNameError = true;
                break;
              case "lastName":
                this.errors.lastNameError = true;
                break;    
              case "email":
                this.errors.emailError = true;
                break;
              case "password":
                this.errors.passwordError = true;
                break;
              case "confirmPassword":
                this.errors.confirmPasswordError = true;
                break;
              case "zipCode":
                this.errors.zipCodeError = true;
                break;
            }
          }
        });
      }
    }, 100);
  }; 
}
