@import "../../../_colors";

:host {
  // host styles in styles.scss .full-screen-form
  position: fixed;
  z-index: 999998;
  width: 100%;
  height: 100%;
  top: 0;
  left: 0;
  background-color: #fff;
  display: block;
  padding: 15px;
  box-sizing: border-box;
  overflow-y: auto;

  @mixin border() {
    border: 1px solid $mediumLightGray;
    border-radius: 18px;
  }

  a {
    text-decoration: none;
    color: unset;
  }

  button-standard {
    span {
      margin-left: 10px;
    }

    &.back-button {
      position: absolute;
      top: 15px;
      left: 15px;
    }
  }

  & > div {
    // border: 1px solid red;
    --panel-details-width: calc(100% - 30px);
    width: var(--panel-details-width);
    --panel-details-max-width: 932px;
    max-width: var(--panel-details-max-width);
    margin: 0 auto;
  }

  .panel-name-row {
    margin-top: 70px;
    margin-bottom: 30px;
    display: grid;
    grid-template-columns: 2fr 1fr;
    align-items: flex-start;

    .panel-name-grid-area {
      .panel-name {
        --panel-name-font-size: 1.625rem;
        font-size: var(--panel-name-font-size);
        font-weight: 700;
      }

      .panel-product-container {
        display: flex;
        align-items: center;
        justify-content: flex-start;
        flex-direction: row;
        flex-wrap: nowrap;
        --panel-product-container-font-size: 0.875rem;
        font-size: var(--panel-product-container-font-size);
        line-height: var(--panel-product-container-font-size);
        color: $darkestGray;
        margin-top: 10px;

        .arrow-container {
          position: relative;
          top: calc(var(--panel-product-container-font-size) * -0.5);
          margin-left: 5px;
          margin-right: 10px;
        }

        span {
          font-style: italic;
          font-weight: 500;
          margin-right: 10px;
        }

        .divider-block {
          width: 20px;
          height: 20px;
          border-radius: 5px;
          background-color: #d463e0;

          .tt-divider-block {
            display: flex;
            justify-content: center;
            align-items: center;
            width: 20px;
            height: 20px;

            &.name-initial {
              font-size: 14px;
              font-weight: 600;
              color: #5d1765;
            }
          }
        }

        .product-name {
          font-weight: 700;
        }
      }
    }

    & > div {
      // border: 1px solid red;
      --panel-details-width: calc(100% - 30px);
      width: var(--panel-details-width);
      --panel-details-max-width: 932px;
      max-width: var(--panel-details-max-width);
      margin: 0 auto;

      // @media (max-width: 1024px) {
      //     --panel-details-max-width: 800px;
      // }
    }

    .buttons-grid-area {
      display: flex;
      flex-direction: row;
      flex-wrap: wrap;
      justify-content: flex-end;
      align-items: center;
      gap: 10px;

      .target-panelists-button {
        margin: 0;

        svg {
        }

        span {
          .panel-product-container {
            display: flex;
            align-items: center;
            justify-content: flex-start;
            flex-direction: row;
            flex-wrap: nowrap;
            --panel-product-container-font-size: 0.875rem;
            font-size: var(--panel-product-container-font-size);
            line-height: var(--panel-product-container-font-size);
            color: $darkestGray;
            margin-top: 10px;

            .arrow-container {
              position: relative;
              top: calc(var(--panel-product-container-font-size) * -0.5);
              margin-left: 5px;
              margin-right: 10px;
            }

            span {
              font-style: italic;
              font-weight: 500;
              margin-right: 10px;
              color: #636363;
            }

            lamb-gp-icon {
              --divider-block-size: 20px;
              width: var(--divider-block-size);
              height: var(--divider-block-size);
              border-radius: 5px;
              background: $mediumLightGray;
              margin-right: 3px;
            }

            .product-name {
              font-weight: 700;
            }
          }
        }
      }

      .actions-button-container {
        position: relative;
        width: fit-content;
        height: fit-content;

        .actions-button {
          margin: 0;

            svg {
            }

            span {
            }
          }

        popover {
          --router-link-color: #{$darkGray};
          --router-link-size: 0.9rem;
          --router-link-hover-color: #{$blue};

          .popover-item {
            text-decoration: none;
            color: var(--router-link-color);
            font-size: var(--router-link-size);
            transition: color 0.2s ease;
            cursor: pointer;
            width: fit-content;
            white-space: nowrap;

            &:hover {
              color: var(--router-link-hover-color);
            }
          }
        }
      }
    }
  }

  .panel-summary-row {
    @include border();
    display: flex;
    flex-flow: row nowrap;
    margin-bottom: 60px;
    box-shadow: 0px 4px 10px 0px rgba(0, 0, 0, 0.1);

    .panel-summary-section {
      flex-grow: 1;
      padding: 12px 15px 15px;

      &:not(:last-child) {
        border-right: 1px solid $mediumLightGray;
      }

      &.budget-section {
        flex-grow: 3;

        .section-label {
          margin-bottom: 10px;
        }

        .section-content {
          font-size: 0.75rem;

          percentage-bar {
            width: 126px;
          }

          .spent {
            font-weight: 700;
            margin-left: 12px;
          }

          .remaining {
            font-weight: 500;
            margin-left: 8px;
          }
        }
      }

      .section-label {
        font-size: 0.75rem;
        font-weight: 500;
        color: $darkGray;
        margin-bottom: 12px;
        display: flex;

        span {
          margin-left: auto;
          text-decoration: underline;
          cursor: pointer;
        }
      }

        .section-content {
          font-weight: 700;
          display: flex;
          font-size: 1rem;
          align-items: center;
        }
      }
    }

  .response-details-header {
    margin-bottom: 70px;

    .header-label {
      font-size: 1.125rem;
      font-weight: 700;
      // flex-basis: auto;
      // white-space: nowrap;
      // flex-grow: 1;
      // flex-basis: 100%;
    }

    .header-content {
      display: flex;
      flex-flow: row nowrap;
      margin-top: 8px;

      .arrow-container {
        margin-left: 10px;
        margin-top: -4px;
      }

      .info-container {
        margin-left: 10px;
        display: flex;
        flex-flow: row nowrap;
        align-items: center;
        border: 1px solid $lightGray;
        border-radius: 12px;
        height: 40px;

        .response-count {
          border-right: 1px solid $lightGray;
          padding: 0px 15px;
          font-size: 0.75rem;
          font-weight: 600;
          height: 100%;
          display: flex;
          align-items: center;
        }

        .privacy-status {
          padding: 0px 15px;
          font-size: 0.75rem;
          font-weight: 600;
          height: 100%;
          display: flex;
          align-items: center;
        }
      }
    }

    // .response-count{
    //     font-size: .75rem;
    //     font-weight: 500;
    //     color: $darkGray;
    //     flex-basis: auto;
    //     white-space: nowrap;
    // }

    // .border-line{
    //     background: $lightGray;
    //     height: 1px;
    //     width: 100%;
    //     flex-grow: 1;
    // }

    .filters-container {
      border: 1px solid red;
      flex-basis: auto;
    }
  }

  .tt-response-details-header {
    display: flex;
    flex-direction: row;
    align-items: center;
    gap: 10px;
    margin: 40px 0 20px 0;

    .header-label {
      font-size: 20px;
      font-weight: 600;
      margin-left: 10px;
    }

    .border-line {
      flex: 1;
      height: 1px;
      background-color: #ddd;
      margin-left: 20px;
    }
  }

  .tt-details-header {
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    align-items: center;
    padding: 16px 20px;
    border-bottom: 1px solid #e0e0e0;
    background-color: #f8f9fa;

    .tt-product-names {
      display: flex;
      flex-direction: row;
      align-items: center;
      gap: 10px;
      flex: 1;

      .tt-divider-block {
        margin-right: 5px;
      }
    }

    .p-value {
      display: flex;
      align-items: center;
      gap: 8px;
      font-weight: 500;
      padding: 5px 10px;
      border-radius: 6px;
      background-color: #e4ebfe;
      color: #11295f;
      font-size: 13px;
      min-width: 130px;

      &.significant {
        background-color: rgba(255, 197, 92, 0.2);
        color: #905e00;

        svg path {
          fill: #905e00;
        }
      }

      .significance-badge {
        background-color: #ffc55c;
        color: #905e00;
        font-size: 12px;
        padding: 2px 8px;
        border-radius: 12px;
        font-weight: 600;
        margin-left: 5px;
      }
    }

    .expand-icon {
      cursor: pointer;
      transition: transform 0.3s ease;
      margin-left: 15px;

      &:hover {
        transform: scale(1.2);
      }
    }
  }

  .tt-divider-block {
    background-color: #d463e0;
    display: flex;
    justify-content: center;
    align-items: center;
    width: 20px;
    height: 20px;
    border-radius: 5px;
    // margin-right: 3px;

    .name-initial {
      font-size: 12px;
      font-weight: 600;
      color: #5d1765;
    }
  }

  .tt-product-names {
    display: flex;
    flex-direction: row;
    align-items: center;
    gap: 10px;
    flex-wrap: wrap;
  }

  .response-details-section {
    width: 100%;
    margin-bottom: 65px;
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 20px;

    .section-header {
      font-size: 0.875rem;
      font-weight: 700;
      margin-bottom: 20px;
      grid-column: span 2;
    }

    .section-content {
      grid-column: span 2;

      &:not(.overall) {
        border: 1px solid $lightGray;
        border-radius: 18px;
        padding: 15px;
      }

      &.overall {
        display: grid;
        grid-template-columns: repeat(
          auto-fit,
          minmax(140px, 1fr) minmax(140px, 1fr) minmax(140px, 1fr)
        );
        gap: 10px;
      }

        &.aroma-spectrum {
          // width: 110%;
          // margin-left: -5%;
          // border: 1px solid red;
        }

        &.top-descriptors {
          grid-column: span 1;
          // .tabs-container{
          //     display:flex;
          //     flex-direction: row;
          //     justify-content:space-between;
          //     align-items: end;

          //     .header-elem{
          //         display:flex;
          //     flex-direction: row;
          //     justify-content:space-between;
          //     align-items: start;
          //     }

          //     .toggle-tabs{
          //         margin: 0;

          //         .tab-button {
          //             border: none;
          //             background: none;
          //             padding: 6px 12px;
          //             border-radius: 20px;
          //             cursor: pointer;
          //             font-size: 14px;
          //             font-weight: 500;
          //             color: #666;
          //             transition: all 0.3s ease;

          //             &.active {
          //               background-color: #007bff;
          //               color: white;
          //               box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
          //             }

          //             &:hover:not(.active) {
          //               background-color: #e6e6e6;
          //             }
          //           }
          //     }

        // }
      }
      &.aroma-distribution {
        grid-column: span 1;
      }

      .descriptors-table {
        margin-top: 15px;
      }
    }

    &.demographic-breakdown,
    &.additional-comments {
      grid-template-columns: 1fr;
    }
  }

  
}
.response-details-section {
  max-width: var(--panel-details-max-width);
  width: 100%;
  margin-bottom: 90px;
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 20px;
  justify-content: space-between;

  &.sensory-spectrum {
    display: block;

    .spectrum-container {
      width: calc(var(--panel-details-max-width) + 60px);
      margin-left: -30px;
      margin-bottom: 40px;
      margin-top: 20px;

      @media (max-width: 1024px) {
        width: 100%;
        overflow-x: auto;
        margin-left: 0;
      }
    }

    .descriptor-container {
      max-width: var(--panel-details-max-width);
      width: 100%;
      grid-column: span 2;
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(380px, 1fr));
      gap: 40px;
      justify-items: center;
    }
  }

  &.range-slider {
    grid-template-columns: repeat(
      auto-fit,
      minmax(140px, 1fr) minmax(140px, 1fr) minmax(140px, 1fr)
    );
    gap: 20px;

    .section-header {
      grid-column: 1 / -1;
    }
  }

  .section-header {
    grid-column: span 2;
    display: flex;
    flex-flow: row nowrap;
    align-items: center;
    justify-content: flex-start;
    gap: 10px;
    color: #292727;
    font-family: Inter;
    font-size: 0.8125rem;
    font-style: normal;
    font-weight: 600;
    line-height: normal;
    max-width: var(--panel-details-max-width);

    .step-type-label {
      font-size: 0.625rem;
      font-weight: 400;
      text-transform: uppercase;
    }

    .line {
      flex-grow: 1;
      background: $mediumLightGray;
      height: 1px;
    }
  }

  response-data-demographic-breakdown {
    grid-column: span 2;
  }

  response-data-aroma-spectrum {
    width: calc(var(--panel-details-max-width) + 60px);
  }

  response-data-descriptors {
    max-width: calc(var(--panel-details-max-width) / 2);
    height: fit-content;
  }

  response-data-aroma-category-distribution {
    max-width: calc(var(--panel-details-max-width) / 2);
    height: fit-content;
  }

  response-data-multiple-choice {
    grid-column: span 2;
  }

  response-data-true-false {
    grid-column: span 2;
  }

  response-data-comments {
    grid-column: span 2;
  }
}
