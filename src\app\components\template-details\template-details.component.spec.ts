import { ComponentFixture, TestBed } from '@angular/core/testing';
import { TemplateDetailsComponent } from './template-details.component';
import { ActivatedRoute } from '@angular/router';
import { of } from 'rxjs';
import { NoopAnimationsModule } from '@angular/platform-browser/animations';

describe('TemplateDetailsComponent', () => {
  let component: TemplateDetailsComponent;
  let fixture: ComponentFixture<TemplateDetailsComponent>;

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      imports: [TemplateDetailsComponent, NoopAnimationsModule],
      providers: [
        {
          provide: ActivatedRoute,
          useValue: {
            data: of({
              template: {
                id: 'test-id',
                name: 'Test Template',
                experiment_type: 'Test Type',
                consumption_option: 'Test Consumption',
                created: { _seconds: **********, _nanoseconds: 0 },
                usedBy: []
              }
            })
          }
        }
      ]
    })
    .compileComponents();

    fixture = TestBed.createComponent(TemplateDetailsComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
