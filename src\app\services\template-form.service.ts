import { inject, Injectable } from '@angular/core';
import {
  FormBuilder,
  FormGroup,
  FormControl,
  Validators,
  FormArray,
} from '@angular/forms';
import {
  Subject,
  throttleTime,
  BehaviorSubject,
  firstValueFrom
} from 'rxjs';
import { ApiService } from './api.service';
import { ConstantsService } from './constants.service';
import { TemplatesService } from './templates.service';

@Injectable({
  providedIn: 'root',
})
export class TemplateFormService {
  form = inject(FormBuilder);
  apiService = inject(ApiService);
  constantsService = inject(ConstantsService);
  templatesService = inject(TemplatesService);

  updatedTemplate: any = null;

  formStep: number = 1;
  totalSteps: number = 5; // One less than panel form since we're skipping step 2
  formSuperHeader: string = 'Creating template';
  formHeader: string = 'Create a Template';

  get formHeaderText(): string {
    return this.editing ? 'Edit Template' : this.formHeader;
  }

  get formSuperHeaderText(): string {
    return this.editing ? 'Editing template' : this.formSuperHeader;
  }
  nextStepFlavorTextArr: string[] = [
    'All done?',
    'Template questions in order?',
    'Target panelists all set?',
    'Budget approved?',
    'Look good?',
  ];

  hasError: boolean = false;
  errorMessage: string = '';

  // Step 1: Basic Information (name, description, dates)
  step1Form: FormGroup = this.form.group({
    experiment_type: new FormControl<string>('normal_sensory'),
    name: new FormControl<string>('', Validators.required),
    description: new FormControl<string>('', Validators.required),
    startOption: new FormControl<string>('now'),
    start_date: new FormControl<string>(''),
    endOption: new FormControl<string>('never'),
    end_date: new FormControl<string>(''),
  });

  // Step 2: Questions (consumption method, question order)
  step2Form: FormGroup = this.form.group({
    consumptionMethod: new FormControl(''),
    questionOrder: new FormControl('custom'),
    questions: new FormArray([]),
  });

  // Step 3: Target Panelists
  step3Form: FormGroup = this.form.group({
    panelistPool: new FormControl('public'),
    restricted: new FormControl(false),
    restrictionLevel: new FormControl('1'),
  });

  // Step 4: Budget
  step4Form: FormGroup = this.form.group({
    pay: new FormControl(5),
    customPay: new FormControl(false),
    maxSpend: new FormControl(0),
  });

  clearForm() {
    this.step1Form.reset();
    this.step1Form.get('experiment_type')?.setValue('normal_sensory');
    this.step1Form.get('startOption')?.setValue('now');
    this.step1Form.get('endOption')?.setValue('never');

    const questionsArray = this.step2Form.get('questions') as FormArray;
    questionsArray.clear();
    this.step2Form.reset();
    this.step2Form.get('questionOrder')?.setValue('custom');

    this.step3Form.reset();
    this.step3Form.get('panelistPool')?.setValue('public');
    this.step3Form.get('restricted')?.setValue(false);
    this.step3Form.get('restrictionLevel')?.setValue(1);

    this.step4Form.reset();
    this.step4Form.get('pay')?.setValue(5);
    this.step4Form.get('customPay')?.setValue(false);
    this.step4Form.get('maxSpend')?.setValue(0);
  }

  checkEndDate(endDate: string) {
    if (endDate) {
      const year = new Date(endDate).getFullYear();
      if (year >= 4000) {
        return 'never';
      }
      return 'custom';
    }
    return 'never';
  }

  async fillFormFromTemplateObject(template: any) {
    this.step1Form.patchValue({
      experiment_type: template.experiment_type || 'normal_sensory',
      name: template.name,
      description: template.description,
      start_date: template.start_date,
      end_date: template.end_date,
      startOption: template.start_date ? 'custom' : 'now',
      endOption: this.checkEndDate(template.end_date),
    });

    // Get the FormArray
    const questionsArray = this.step2Form.get('questions') as FormArray;

    // Clear existing controls
    questionsArray.clear();

    // Add new FormControls for each step
    if (template['steps'] && Array.isArray(template['steps'])) {
      template['steps'].forEach((step: string) => {
        questionsArray.push(new FormControl(step));
      });
    }

    // Now patch the remaining step2Form values
    this.step2Form.patchValue({
      consumptionMethod: template['consumption_option'] || '',
      questionOrder: this.checkIfCustomQuestionOrder(template['steps'] || []),
    });

    this.step3Form.patchValue({
      panelistPool: template.isPublic ? 'public' : 'private',
      restricted: template.minimum_certification_level > 0,
      restrictionLevel: template.minimum_certification_level || 1,
    });

    this.step4Form.patchValue({
      pay: (template.value || 500) / 100,
      customPay: (template.value || 0) % 100 !== 0,
      maxSpend: (template.budget || 0) / 100,
    });

    return;
  }

  checkIfCustomQuestionOrder(steps: any) {
    if (!steps || !Array.isArray(steps) || steps.length === 0) {
      return 'default';
    }

    if (steps.length !== this.constantsService.defaultQuestionOrder.length) {
      return 'custom';
    }

    // Check if arrays have same elements in same order
    for (let i = 0; i < steps.length; i++) {
      if (steps[i] !== this.constantsService.defaultQuestionOrder[i]) {
        return 'custom';
      }
    }

    return 'default';
  }

  loading: boolean = false;
  buttonDisabled: boolean = false;

  stepSubject = new Subject<'next' | 'prev'>();
  saveSubject = new Subject<void>();
  scrollResetSubject = new Subject<void>();

  formErrors: { [key: string]: { [key: string]: boolean } } = {
    step1: {
      experiment_type: false,
      name: false,
      description: false,
      startDate: false,
      startOption: false,
      endDate: false,
      endOption: false,
    },
    step2: {
      consumptionMethod: false,
      questionOrder: false,
      questions: false,
    },
    step3: {
      panelistPool: false,
      restricted: false,
      restrictionLevel: false,
    },
    step4: {
      pay: false,
      maxSpend: false,
    },
  };

  editing: boolean = false;
  editingTemplate: any = {};

  constructor() {
    this.initializeSubscriptions();
    this.initializeConditionalValidation();
    this.initializeFormValueChangeSubscriptions();
  }

  initializeSubscriptions(): void {
    this.initializeStepSubscription();
    this.initializeSaveSubscription();
  }

  private initializeStepSubscription(): void {
    this.stepSubject.pipe(throttleTime(500)).subscribe((direction) => {
      if (direction === 'next' && this.formStep < this.totalSteps) {
        this.formStep++;
        this.scrollResetSubject.next();
      } else if (direction === 'prev' && this.formStep > 1) {
        this.formStep--;
        this.scrollResetSubject.next();
      }
    });
  }

  private initializeSaveSubscription(): void {
    this.saveSubject.pipe(throttleTime(3000)).subscribe(() => {
      if (this.formStep === this.totalSteps) {
        // Create a copy of step1Form values and modify if needed to get the correct date format
        const step1Values = { ...this.step1Form.value };
        if (step1Values.startOption === 'now') {
          step1Values.start_date = new Date().toISOString();
        } else if (step1Values.start_date) {
          step1Values.start_date = new Date(
            step1Values.start_date
          ).toISOString();
        }

        if (step1Values.endOption === 'custom' && step1Values.end_date) {
          step1Values.end_date = new Date(step1Values.end_date).toISOString();
        }

        // create a copy of step 4 values to make sure pay and maxSpend are numbers
        const step4Values = { ...this.step4Form.value };
        step4Values.pay = Number(step4Values.pay);
        step4Values.maxSpend = Number(step4Values.maxSpend);

        const payload = {
          ...step1Values,
          ...this.step2Form.value,
          ...this.step3Form.value,
          ...step4Values,
        };

        this.saveTemplate(payload);
      }
    });
  }

  formatPayload(payload: any) {
    return {
      start_date: payload.startOption === 'now' ?
        new Date().toISOString() :
        (payload.start_date ? new Date(payload.start_date).toISOString() : null),
      end_date:
        payload.endOption === 'never'
          ? new Date('4200-01-01T00:00:00Z').toISOString()
          : (payload.end_date ? new Date(payload.end_date).toISOString() : null),
      consumption_option: payload.consumptionMethod || '',
      steps: payload.questions || [],
      isPublic: payload.panelistPool === 'public',
      minimum_certification_level: payload.restricted
        ? Number(payload.restrictionLevel)
        : 0,
      value: payload.pay * 100,
      budget: payload.maxSpend * 100,
      description: payload.description,
      name: payload.name,
      experiment_type: payload.experiment_type || 'Normal Sensory',
      usedBy: payload.usedBy || [],
    };
  }

  private validateCurrentStep(): boolean {
    const form = this.getCurrentForm();

    // Only validate required fields (name and description) in step 1
    if (this.formStep === 1) {
      // Get the current values directly from the form
      const formValues = form.value;
      const nameValue = formValues.name;
      const descriptionValue = formValues.description;

      let isValid = true;

      console.log('Form values:', nameValue, descriptionValue);

      // Check if name is empty or just whitespace
      if (!nameValue || nameValue.trim() === '') {
        this.formErrors['step1']['name'] = true;
        isValid = false;
      } else {
        this.formErrors['step1']['name'] = false;
      }

      // Check if description is empty or just whitespace
      if (!descriptionValue || descriptionValue.trim() === '') {
        this.formErrors['step1']['description'] = true;
        isValid = false;
      } else {
        this.formErrors['step1']['description'] = false;
      }

      return isValid;
    }

    // For other steps, all fields are optional
    return true;
  }

  showError(): void {
    this.hasError = true;
    switch (this.formStep) {
      case 1:
        this.errorMessage = 'Please provide a name and description for the template.';
        break;
      default:
        this.errorMessage = 'Please complete all required fields before proceeding.';
        break;
    }
  }

  nextStep() {
    if (this.validateCurrentStep()) {
      this.stepSubject.next('next');
      console.log(this.step3Form.value);
    } else {
      this.showError();
    }
  }

  prevStep() {
    const currentStep = `step${this.formStep}` as keyof typeof this.formErrors;
    // Reset current step's error state
    if (currentStep !== 'step5') {
      Object.keys(this.formErrors[currentStep]).forEach((fieldName) => {
        this.formErrors[currentStep][
          fieldName as keyof (typeof this.formErrors)[typeof currentStep]
        ] = false;
      });
    }

    this.stepSubject.next('prev');
  }

  save() {
    this.saveSubject.next();
  }

  setFormStep(step: number): void {
    if (step >= 1 && step <= this.totalSteps) {
      this.formStep = step;
      this.scrollResetSubject.next();
    }
  }

  reviewStep() {
    this.formStep = 5; // Last step (review)
    this.scrollResetSubject.next();
  }

  async saveTemplate(templateData: any) {
    this.loading = true;
    const formattedTemplate = this.formatPayload(templateData);

    try {
      if (this.editing) {
        let updatedTemplate = await firstValueFrom(this.templatesService.updatePanelTemplate(this.editingTemplate.id,{
          ...formattedTemplate,
          id: this.editingTemplate.id
        }));
        this.updatedTemplate=(updatedTemplate);
      } else {
        await firstValueFrom(this.templatesService.createPanelTemplate(formattedTemplate));
      }
      this.loading = false;
    } catch (error) {
      console.error('Error saving template:', error);
      this.hasError = true;
      this.errorMessage = 'Failed to save template. Please try again.';
      this.loading = false;
    }
  }

  isFieldInvalid(formStep: number, fieldName: string): boolean {
    const stepKey = `step${formStep}` as keyof typeof this.formErrors;
    return this.formErrors[stepKey][
      fieldName as keyof (typeof this.formErrors)[typeof stepKey]
    ];
  }

  private getCurrentForm(): FormGroup {
    switch (this.formStep) {
      case 1:
        return this.step1Form;
      case 2:
        return this.step2Form;
      case 3:
        return this.step3Form;
      case 4:
        return this.step4Form;
      default:
        return this.step1Form;
    }
  }

  private initializeConditionalValidation(): void {
    // Get form controls
    const startOptionControl = this.step1Form.get('startOption');
    const startDateControl = this.step1Form.get('start_date');
    const endOptionControl = this.step1Form.get('endOption');
    const endDateControl = this.step1Form.get('end_date');

    // Subscribe to startOption changes
    startOptionControl?.valueChanges.subscribe((value) => {
      if (value === 'custom') {
        startDateControl?.setValidators(Validators.required);
      } else {
        startDateControl?.clearValidators();
      }
      startDateControl?.updateValueAndValidity();
    });

    // Subscribe to endOption changes
    endOptionControl?.valueChanges.subscribe((value) => {
      if (value === 'custom') {
        endDateControl?.setValidators(Validators.required);
      } else {
        endDateControl?.clearValidators();
      }
      endDateControl?.updateValueAndValidity();
    });
  }

  private initializeFormValueChangeSubscriptions(): void {
    // Subscribe to each form's valueChanges
    [
      this.step1Form,
      this.step2Form,
      this.step3Form,
      this.step4Form,
    ].forEach((form, index) => {
      form.valueChanges.subscribe(() => {
        const stepKey = `step${index + 1}` as keyof typeof this.formErrors;

        // Reset all error flags for the current step
        Object.keys(this.formErrors[stepKey]).forEach((fieldName) => {
          this.formErrors[stepKey][
            fieldName as keyof (typeof this.formErrors)[typeof stepKey]
          ] = false;
        });

        this.hasError = false;
        this.errorMessage = '';
      });
    });
  }
}
