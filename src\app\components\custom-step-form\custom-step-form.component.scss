:host{
    // host styles in styles.scss .full-screen-form
    position: fixed;
    z-index: 999999;
    
    button-two-click{
        position: absolute;
        top: 15px;
        left: 15px;

        svg{
            margin-right: 10px;
        }
    }

    button-standard.save-button{
        position: absolute;
        top: 15px;
        right: 15px;
        margin: 0;

        svg{
            margin-left: 10px;
        }
    }

    .form-header{
        font-size: var(--form-header-font-size);
        font-weight: var(--form-header-font-weight);
        color: var(--form-header-color);
        display: grid;
        grid-template-columns: 1fr;
        justify-content: space-between;
        align-items: center;
        margin: var(--form-header-margin-top) var(--form-header-margin-right) 50px var(--form-header-margin-left);
        width: 100%;
        max-width: var(--form-header-max-width);
        
        .super-header{
            font-size: var(--form-super-header-font-size);
            font-weight: var(--form-super-header-font-weight);
            color: var(--form-super-header-color);
            grid-column: span 2;
            line-height: var(--form-super-header-font-size);
            margin-top: calc(var(--form-super-header-font-size) * -1);
            font-style: italic;
        }
    
        .header{
            font-size: var(--form-header-font-size);
            font-weight: var(--form-header-font-weight);
            color: var(--form-header-color);
    
            &.editing{
                grid-column: span 2;
            }
        }
    }

    form{
        width: 100%;
        margin-bottom: 40px;
        max-width: 1000px;
        display: grid;
        grid-template-columns: 1fr 1fr;
        gap: 80px;
        --input-container-label-width: 100px;

        .section-label{
            font-size: 0.875rem;
            font-weight: 700;
            width: 100%;
            border-bottom: 1px solid #E5E5E5;
            padding-bottom: 5px;
            margin-bottom: 20px;
            display: flex;
            align-items: center;
            justify-content: space-between;

            input-radio-standard{
                font-size: .7rem;
                font-weight: 500;
                color: #292727;
            }
        }

        .step-details-container{

            input-text,
            input-text-area{
                width: 100%;
            }

            .input-container{

                &.step-instructions{
                    align-items: flex-start;

                    label{
                        margin-top: 12px;
                    }
                }

                &.step-type{
                    align-items: flex-start;

                    label{
                        margin-top: 12px;

                        .sub-label{
                            font-size: 0.625rem;
                            font-weight: 500;
                            color: #979797;
                        }
                    }
                }

                label{

                    .sub-label{
                        font-size: 0.5625rem;
                        font-weight: 700;
                        color: #292727;
                        margin-top: 3px;
                    }
                }
            }
        }

        .step-options-container{

            .no-config{
                display: grid;
                grid-template-columns: 1fr;
                justify-content: center;
                align-items: center;
                text-align: center;
                height: 200px;
                font-size: .75rem;
                color: #5d5d5d;
                font-weight: 500;
            }

            .aroma-spectrum{
                border: 1px solid #D8D8D8;
                box-shadow: 0px 4px 11px 0px rgba(0, 0, 0, 0.10);
                border-radius: 12px; 
                display: grid;
                grid-template-columns: 1fr;
                justify-content: center;
                padding: 20px 20px 0px 20px;
                text-align: center;
                width: 100%;

                .label{
                    font-weight: 600;
                    font-size: .75rem;
                    margin-bottom: 20px;
                }

                svg{
                    width: 100%;
                    height: auto;
                }
            }

            button.add-option-button{
                display: flex;
                cursor: pointer;
                align-items: center;
                margin-left: -1px;
                margin: 0 auto 20px;

                .plus-container{
                    background-color: #D9D9D9;
                    width: 19px;
                    height: 19px;
                    border-radius: 6px;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    margin-right: 5px;
                }

                .add-step-text{
                    font-size: .75rem;
                    font-weight: 500;
                    color: #5D5D5D;
                }
            }
        }
    }
}