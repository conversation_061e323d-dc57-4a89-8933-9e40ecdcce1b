<form [formGroup]="profileForm">
    <div class="settings-sub-page-header">
        <!-- <button-standard class="back-button">
            <svg xmlns="http://www.w3.org/2000/svg" width="19" height="15" viewBox="0 0 19 15" fill="none">
                <path d="M18.0659 6.58337H3.15902L8.23183 1.5832C8.59862 1.22334 8.60044 0.635616 8.2373 0.273971C7.87417 -0.0895077 7.28109 -0.0913131 6.91615 0.268546L0.235399 6.83807C0.191604 6.87966 0.153283 6.92668 0.118611 6.97731V7.00443C0.0894142 7.04603 0.0675141 7.09123 0.0474452 7.13825V7.1708C0.0474452 7.21782 0.0145984 7.26303 0.00547444 7.31004C-0.00182482 7.37152 -0.00182482 7.43301 0.00547444 7.49449V7.54151C0.00364975 7.58672 0.00364975 7.63012 0.00547444 7.67533C0.00547444 7.71692 0.0291972 7.7549 0.0383211 7.79106C0.041971 7.81095 0.041971 7.83084 0.0383211 7.85074L0.0985418 7.96104L0.125913 8.00806H0.127739C0.160585 8.05508 0.197082 8.10029 0.235403 8.14369L6.88672 14.7586C7.25533 15.0913 7.82285 15.0787 8.17504 14.7296C8.52723 14.3806 8.54365 13.8201 8.20971 13.453L3.14577 8.43474H18.0657C18.5821 8.43474 19 8.02063 19 7.50886C19 6.9971 18.5823 6.58337 18.0659 6.58337Z" fill="black"/>
            </svg>
        </button-standard> -->
        <div class="header-title">Profile</div>
        <button-standard class="save-button" [class.disabled]="saveState === 0" [class.green]="saveState === 1 || saveState === 2" (onAction)="saveState === 1 ? saveProfile() : null">{{saveButtonText}}</button-standard>
    </div>

    <div class="settings-section general">
        <div class="section-header">
            <span>General</span>
        </div>

        <!-- <div class="profile-image"> -->
            <!-- <img src="" alt=""> -->
            <!-- <span>{{profileService.getInitials()}}</span> -->
        <!-- </div> -->

        <div class="input-container">
            <label for="first-name" class="primary">First Name</label>
            <!-- <input type="text" id="first-name" formControlName="first_name"> -->
            <input-text [id]="'first-name'" [control]="firstNameControl"></input-text>

            <label for="last-name" class="primary">Last Name</label>
            <!-- <input type="text" id="last-name" formControlName="last_name"> -->
            <input-text [id]="'last-name'" [control]="lastNameControl"></input-text>
        </div>
    </div>

    <div class="settings-section organization">
        <div class="section-header">
            <span>Organization Details</span>
        </div>

        <div class="input-container">
            <label for="organization-name" class="primary">Organization Name</label>
            <input-text [id]="'organization-name'" [control]="orgNameControl"></input-text>
        </div>

        <div class="input-container">
            <label for="organization-type" class="primary">Organization Type</label>
            <div class="radio-container">
                <input-radio-block [id]="'organization-type-manufacturer'" [name]="'organization-type'" [value]="'manufacturer'" [control]="orgTypeControl" [class.active]="orgTypeControl.value === 'manufacturer'">Manufacturer</input-radio-block>
                <input-radio-block [id]="'organization-type-retailer'" [name]="'organization-type'" [value]="'retailer'" [control]="orgTypeControl" [class.active]="orgTypeControl.value === 'retailer'">Retailer</input-radio-block>
                <input-radio-block [id]="'organization-type-r-and-d'" [name]="'organization-type'" [value]="'r-and-d'" [control]="orgTypeControl" [class.active]="orgTypeControl.value === 'r-and-d'">R&D House</input-radio-block>
            </div>
        </div>
    </div>

    <div class="settings-section organization">
        <div class="section-header settings-sub-page-header">

                <span>Users</span>
                <button-standard>+ new user</button-standard>

        </div>
        <div class="users-table">
            <users-table/>
        </div>

    </div>
</form>