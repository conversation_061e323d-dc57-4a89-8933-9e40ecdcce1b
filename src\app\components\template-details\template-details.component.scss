:host {
  position: fixed;
  z-index: 999998;
  width: 100%;
  height: 100%;
  top: 0;
  left: 0;
  background-color: #fff;
  display: block;
  padding: 15px;
  box-sizing: border-box;
  overflow-y: auto;
}

.template-details-container {
  display: flex;
  flex-direction: column;
  width: 100%;
  min-height: 100%;
  height: auto;
  position: relative;
  padding-bottom: 80px; /* Add padding to prevent content from being hidden by footer */

  &.empty {
    justify-content: center;
    align-items: center;
  }

  .close-button-container {
    position: absolute;
    top: 8px;
    left: 8px;
    z-index: 10;

    button-two-click {
      svg {
        margin-right: 8px;
      }
    }
  }



  .template-title-container {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-top: 32px;
    margin-bottom: 32px;
    width: 65%;
    position: absolute;
    top:16px;
    left:200px;
    z-index: 10;


    h2.template-name {
      font-size: 26px;
      font-weight: 700;
      margin: 0;
      color: #000000;
    }

    .more-actions-button {
      background: none;
      border: none;
      cursor: pointer;
      padding: 4px;
      display: flex;
      align-items: center;
      justify-content: center;

      &:hover {
        background-color: rgba(0, 0, 0, 0.05);
        border-radius: 50%;
      }

      svg {
        width: 24px;
        height: 24px;
      }
    }

    popover {
      position: absolute;
      top: 100%;
      right: 0;
      margin-top: 8px;
      z-index: 10;
      background-color: #FFFFFF;
      border-radius: 4px;
      box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
      overflow: hidden;

      .popover-item {
        padding: 12px 16px;
        cursor: pointer;
        white-space: nowrap;
        transition: background-color 0.2s ease;
        font-size: 14px;

        &:hover {
          background-color: #F5F5F5;
        }

        &.delete {
          color: #FF0000;
        }
      }
    }
  }

  .template-content {
    padding: 24px 24px 100px 24px;
    width: 650px;
    margin: 50px auto;
    position: relative;
    flex: 1;


  .template-section {
    display: flex;
    align-items: center;
    margin-bottom: 16px;
    margin-top: 32px;
    border-top: 1px solid #E0E0E0;
    border-bottom: 1px solid #E0E0E0;
    padding-top: 8px;
    padding-bottom: 8px;
    position: relative;

    .section-icon {
      display: flex;
      align-items: center;
      justify-content: center;
      margin-right: 12px;
      color: #000000;

      svg {
        width: 20px;
        height: 20px;
      }
    }

    h3 {
      font-size: 16px;
      font-weight: 600;
      margin: 0;
      color: #000000;
    }
  }

  .template-info-grid {
    display: flex;
    flex-direction: column;
    gap: 20px;
    margin-bottom: 24px;

    .info-row {
      display: flex;
      flex-direction: row;

      .info-label {
        width: 200px;
        font-size: 14px;
        font-weight: 500;
        color: #000000;
      }

      .info-value {
        flex: 1;
        font-size: 14px;
        font-weight: 700;
        color: #000000;
      }
    }
  }

  .template-questions {
    margin-bottom: 32px;

    // Loading state
    .loading-container {
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      padding: 32px 0;
      gap: 16px;

      .loading-spinner {
        width: 40px;
        height: 40px;
        border: 3px solid #f3f3f3;
        border-top: 3px solid #3498db;
        border-radius: 50%;
        animation: spin 1s linear infinite;
      }

      .loading-text {
        font-size: 14px;
        color: #666666;
      }

      @keyframes spin {
        0% { transform: rotate(0deg); }
        100% { transform: rotate(360deg); }
      }
    }

    // Error state
    .error-container {
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      padding: 32px 0;
      gap: 16px;

      .error-icon {
        color: #FF4D4F;
      }

      .error-text {
        font-size: 14px;
        color: #FF4D4F;
      }
    }

    // Empty questions state
    .empty-questions {
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      padding: 32px 0;
      gap: 16px;

      .empty-icon {
        color: #CCCCCC;
      }

      .empty-text {
        font-size: 14px;
        color: #999999;
      }
    }

    .questions-grid {
      display: grid;
      grid-template-columns: 120px 1fr;
      gap: 16px 24px;
      border-bottom: 1px solid #EEEEEE;
      padding-bottom: 16px;
    }

    .question-category,
    .question-category-value,
    .question-label {
      font-size: 14px;
      font-weight: 500;
      color: #000000;
      padding: 8px 0;
    }

    .question-category-value {
      color: #333333;
    }

    .question-steps {
      display: flex;
      flex-direction: column;
      gap: 16px;
    }

    .question-step {
      display: flex;
      align-items: center;
      position: relative;

      .step-label {
        width: 80px;
        font-size: 14px;
        color: #333333;
      }

      .step-arrow {
        width: 24px;
        height: 24px;
        display: flex;
        align-items: center;
        justify-content: center;
        margin: 0 12px;
      }

      .question-box {
        flex: 1;
        background-color: #FFFFFF;
        border: 1px solid #E0E0E0;
        border-radius: 24px;
        padding: 12px 16px;
        position: relative;
        display: flex;
        align-items: center;
        min-height: 48px;
        max-width: 380px;

        .question-text {
          font-size: 14px;
          font-weight: 500;
          color: #000000;
          margin-right: 8px;
        }

        .question-type {
          font-size: 14px;
          color: #999999;
          flex: 1;
        }

        .eye-icon {
          position: absolute;
          right: 16px;
          top: 50%;
          transform: translateY(-50%);
          display: flex;
          align-items: center;
          justify-content: center;

          svg {
            width: 20px;
            height: 20px;
          }
        }
      }
    }
  }

  .empty-state {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 16px;
    text-align: center;

    .empty-icon {
      color: #999999;
      font-size: 64px;
    }

    .empty-text {
      font-size: 18px;
      font-weight: 500;
      color: #666666;
      margin-bottom: 16px;
    }
  }
}
}
