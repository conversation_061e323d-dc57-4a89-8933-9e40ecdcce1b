import { Component, inject, Input } from '@angular/core';
import { NgIf } from '@angular/common';
import { FormStepsComponent } from '../form-steps/form-steps.component';


@Component({
  selector: 'form-header',
  standalone: true,
  imports: [FormStepsComponent, NgIf],
  templateUrl: './form-header.component.html',
  styleUrl: './form-header.component.scss'
})
export class FormHeaderComponent {

  @Input() service: any = null;

}
