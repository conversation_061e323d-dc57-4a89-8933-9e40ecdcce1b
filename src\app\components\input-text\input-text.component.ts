import { Component, Input } from '@angular/core';
import { FormControl, ReactiveFormsModule } from '@angular/forms';
import { NumbersOnlyDirective } from '../../directives/numbers-only.directive';
import { DollarsAndCentsDirective } from '../../directives/dollars-and-cents.directive';

@Component({
  selector: 'input-text',
  standalone: true,
  imports: [ReactiveFormsModule, NumbersOnlyDirective, DollarsAndCentsDirective],
  templateUrl: './input-text.component.html',
  styleUrl: './input-text.component.scss'
})
export class InputTextComponent {

  @Input() control!: FormControl;
  @Input() type: string = 'text';
  @Input() id: string = '';
  @Input() placeholder: string = '';
  @Input() inputMode: string = 'text';
}
