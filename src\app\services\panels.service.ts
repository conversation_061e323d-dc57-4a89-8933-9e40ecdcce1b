import { inject, Injectable } from '@angular/core';
import {
  FormBuilder,
  FormGroup,
  FormControl,
  Validators,
  FormArray,
} from '@angular/forms';
import {
  map,
  Subject,
  throttleTime,
  BehaviorSubject,
  Observable,
  switchMap,
} from 'rxjs';
import { ApiService } from './api.service';
import { PaginationParams } from '../interfaces/paginationParams';
import { ConstantsService } from './constants.service';
import { PanelInterface } from '../interfaces/panel.interface';
import { TriangleTestService } from './triangle-test.service';

@Injectable({
  providedIn: 'root',
})
export class PanelsService {
  form = inject(FormBuilder);
  apiService = inject(ApiService);
  ttService = inject(TriangleTestService);
  constantsService = inject(ConstantsService);
  formStep: number = 1;
  totalSteps: number = 6;
  
  // Store triangle test data including seed and generated tests
  triangleTestData: {
    tests: any[];
    seed: number;
  } | null = null;
  
  get formSuperHeader() {
    return this.editing ? 'Editing panel' : 'Creating panel';
  }
  get formHeader() {
    if(this.formStep > 1) {
      return this.step1Form.get('name')?.value;
    }
    return this.editing ? 'Edit panel' : 'Create a Panel';
  }
  nextStepFlavorTextArr: string[] = [
    'All done?',
    'Collect data on "Product Name Placeholder"',
    'Panel questions in order?',
    'Target panelists all set?',
    'Budget approved?',
    'Look good?',
  ];
  panelsArray: PanelInterface[] = [];

  createPanelFromTemplate:boolean=false;
  template:any;

  page: number = 1;

  panels: any[] = [];
  panelsObject: { [key: string]: any } = {};

  hasError: boolean = false;
  errorMessage: string = '';

  step1Form: FormGroup = this.form.group({
    experiment_type: new FormControl<string>('Normal Sensory'),
    name: new FormControl<string>('', Validators.required),
    description: new FormControl<string>('', Validators.required),
    startOption: new FormControl<string>('now', Validators.required),
    start_date: new FormControl<string>(''),
    endOption: new FormControl<string>('never', Validators.required),
    end_date: new FormControl<string>(''),
  });

  step2Form: FormGroup = this.form.group({
    product: new FormControl('', Validators.required),
  });

  step2FormTt: FormGroup = this.form.group({
    products: new FormControl<string[]>([], Validators.required)
  });

  step3Form: FormGroup = this.form.group({
    consumptionMethod: new FormControl('', Validators.required),
    questionOrder: new FormControl('default', Validators.required),
    questions: new FormArray([]),
  });

  step3FormTt: FormGroup = this.form.group({
    instructions: new FormControl(''),
    consumptionMethod: new FormControl('', Validators.required),
    questions: new FormArray([]),
  });

  step4Form: FormGroup = this.form.group({
    panelistPool: new FormControl('public', Validators.required),
    restricted: new FormControl(false, Validators.required),
    restrictionLevel: new FormControl('1', Validators.required),
  });

  step5Form: FormGroup = this.form.group({
    pay: new FormControl(5, Validators.required),
    customPay: new FormControl(false, Validators.required),
    maxSpend: new FormControl(0, Validators.required),
  });

  clearForm(){
    this.step1Form.reset();
    this.step1Form.get('experiment_type')?.setValue('Normal Sensory');
    this.step1Form.get('startOption')?.setValue('now');
    this.step1Form.get('endOption')?.setValue('never');
    this.step2Form.reset();
    this.step2FormTt.reset();
    this.step2FormTt.get('products')?.setValue([]);
    const questionsArray = this.step3Form.get('questions') as FormArray;
    questionsArray.clear();
    this.step3Form.reset();
    this.step3Form.get('questionOrder')?.setValue('default');
    this.step4Form.reset();
    this.step4Form.get('panelistPool')?.setValue('public');
    this.step4Form.get('restricted')?.setValue(false);
    this.step4Form.get('restrictionLevel')?.setValue(1);
    this.step5Form.reset();
    this.step5Form.get('pay')?.setValue(5);
    this.step5Form.get('customPay')?.setValue(false);
    this.step5Form.get('maxSpend')?.setValue(0);
  }

  checkEndDate(endDate: string) {
    if (endDate) {
      const year = new Date(endDate).getFullYear();
      if (year >= 4000) {
        return 'never';
      }
      return 'custom';
    }
    return 'never';
  }

  async fillFormFromPanelObject(panel: any) {
    // console.log(panel);
    this.step1Form.patchValue({
      experiment_type: panel.experiment_type || 'normal_sensory',
      name: panel.name,
      description: panel.description,
      start_date: panel.start_date,
      end_date: panel.end_date,
      startOption: panel.start_date ? 'custom' : 'now',
      endOption: this.checkEndDate(panel.end_date),
    });

    this.step2Form.patchValue({
      product: panel.product_id,
    });

    // Get the FormArray
    const questionsArray = this.step3Form.get('questions') as FormArray;

    // Clear existing controls
    questionsArray.clear();
    if(!panel['steps']){
      //default steps
      panel['steps'] = ["a9k1bpXX4H57Bdl7XhaX",
                "wjA1uhqbIMFuZwAaCoJ8",
                "awja0ameTALoE0D4GlWD"];
    }

    // Add new FormControls for each step
    panel['steps'].forEach((step: string) => {
      questionsArray.push(new FormControl(step));
    });

    // Now patch the remaining step3Form values
    this.step3Form.patchValue({
      consumptionMethod: panel['consumption_option'],
      questionOrder: this.checkIfCustomQuestionOrder(panel['steps']),
    });

    this.step4Form.patchValue({
      panelistPool: panel.isPublic ? 'public' : 'private',
      restricted: panel.minimum_certification_level > 0,
      restrictionLevel: panel.minimum_certification_level,
    });

    this.step5Form.patchValue({
      pay: panel.value / 100,
      customPay: panel.value % 100 !== 0,
      maxSpend: panel.budget / 100,
    });


    if (panel.experiment_type === 'Triangle Test') {
      // Set the products directly as an array value
      if (panel.products && Array.isArray(panel.products)) {
        this.step2FormTt.patchValue({
          products: panel.products
        });
      } else {
        // If no products or not an array, set an empty array
        this.step2FormTt.patchValue({
          products: []
        });
      }
    } else {
      this.step2Form.patchValue({
        product: panel.product_id
      });
    }
    return;
  }

  checkIfCustomQuestionOrder(steps: any) {
    // console.log('hello', steps);

    if (steps.length !== this.constantsService.defaultQuestionOrder.length) {
      return 'custom';
    }

    // Check if arrays have same elements in same order
    for (let i = 0; i < steps.length; i++) {
      if (steps[i] !== this.constantsService.defaultQuestionOrder[i]) {
        return 'custom';
      }
    }

    return 'default';
  }

  loading: boolean = false;
  buttonDisabled: boolean = false;

  stepSubject = new Subject<'next' | 'prev'>();
  saveSubject = new Subject<void>();
  scrollResetSubject = new Subject<void>();

  validateStep2(): boolean {
    const experimentType = this.step1Form.get('experiment_type')?.value;
    const isTriangleTest = experimentType === 'Triangle Test';

    if (isTriangleTest) {
      const products = this.step2FormTt.get('products')?.value;
      const productCount = Array.isArray(products) ? products.length : 0;
      const isValid = productCount >= 2 && productCount <= 3;

      // Update error states
      this.formErrors['step2'] = {
        ...this.formErrors['step2'],
        products: !isValid,
        product: false // Reset regular product validation
      };

      this.hasError = !isValid;
      if (!isValid) {
        if (productCount < 2) {
          this.errorMessage = 'Please select at least two products for the triangle test';
        } else if (productCount > 3) {
          this.errorMessage = 'Please select no more than three products for the triangle test';
        }
      } else {
        this.errorMessage = '';
      }

      return isValid;
    }

    // Regular product validation
    const product = this.step2Form.get('product')?.value;

    // Update error states
    this.formErrors['step2'] = {
      ...this.formErrors['step2'],
      product: !product,
      products: false // Reset triangle test validation
    };

    this.hasError = !product;
    this.errorMessage = product ? '' : 'Please select a product before proceeding';

    return !!product;
  }

  formErrors: { [key: string]: { [key: string]: boolean } } = {
    step1: {
      experiment_type: false,
      name: false,
      description: false,
      startDate: false,
      startOption: false,
      endDate: false,
      endOption: false,
    },
    step2: {
      product: false,
      products: false,
    },
    step3: {
      consumptionMethod: false,
      questionOrder: false,
      questions: false,
    },
    step4: {
      panelistPool: false,
      restricted: false,
      restrictionLevel: false,
    },
    step5: {
      pay: false,
      maxSpend: false,
    },
  };

  private pageSubject = new BehaviorSubject<number>(1);
  private limitSubject = new BehaviorSubject<number>(10);

  popupSubject = new Subject<void>();

  public panels$ = this.createPanelsStream();

  editing: boolean = false;
  editingPanel: any = {};
  responseDetails: any = {};

  constructor() {
    this.initializeSubscriptions();
    this.initializeConditionalValidation();
    this.initializeFormValueChangeSubscriptions();
  }

  initializeSubscriptions(): void {
    this.initializeStepSubscription();
    this.initializeSaveSubscription();
  }

  private initializeStepSubscription(): void {
    this.stepSubject.pipe(throttleTime(500)).subscribe((direction) => {
      if (direction === 'next' && this.formStep < this.totalSteps) {
        this.formStep++;
        this.scrollResetSubject.next();
      } else if (direction === 'prev' && this.formStep > 1) {
        this.formStep--;
        this.scrollResetSubject.next();
      }
    });
  }

  private initializeSaveSubscription(): void {
    this.saveSubject.pipe(throttleTime(3000)).subscribe(() => {
      if (this.formStep === this.totalSteps) {
        // Create a copy of step1Form values and modify if needed to get the correct date format
        const step1Values = { ...this.step1Form.value };
        if (step1Values.startOption === 'now') {
          step1Values.start_date = new Date().toISOString();
        } else {
          step1Values.start_date = new Date(
            step1Values.start_date
          ).toISOString();
        }

        if (step1Values.endOption !== 'never') {
          step1Values.end_date = new Date(step1Values.end_date).toISOString();
        }

        // create a copy of step 5 values to make sure pay and maxSpend are numbers
        const step5Values = { ...this.step5Form.value };
        step5Values.pay = Number(step5Values.pay);
        step5Values.maxSpend = Number(step5Values.maxSpend);

        // Check if this is a Triangle Test
        const experimentType = this.step1Form.get('experiment_type')?.value;
        const isTriangleTest = experimentType === 'Triangle Test';
        console.log(`Saving panel with experiment type: ${experimentType}`);

        // Create the base payload
        let payload = {
          ...step1Values,
          ...this.step4Form.value,
          ...step5Values,
        };

        // Add the appropriate step2 form values based on experiment type
        if (isTriangleTest) {
          console.log('Using Triangle Test form data for save');
          payload = {
            ...payload,
            ...this.step2FormTt.value,
            ...this.step3FormTt.value,
          };
        } else {
          console.log('Using Normal Sensory form data for save');
          payload = {
            ...payload,
            ...this.step2Form.value,
          ...this.step3Form.value,
        };
        }

        console.log('New Panel Payload:', payload);
        this.savePanel(payload);
      }
    });
  }

  formatPayload(payload: any) {
    // Check if this is a Triangle Test
    const experimentType = payload.experiment_type;
    const isTriangleTest = experimentType === 'Triangle Test';
    console.log(`Formatting payload for experiment type: ${experimentType}`);

    // Create the base formatted payload
    const formattedPayload: any = {
      start_date: new Date(payload.start_date).toISOString(),
      end_date:
        payload.endOption === 'never'
          ? new Date('4200-01-01T00:00:00Z').toISOString()
          : new Date(payload.end_date).toISOString(),
      consumption_option: payload.consumptionMethod,
      steps: payload.questions,
      isPublic: payload.panelistPool === 'public',
      minimum_certification_level: payload.restricted
        ? Number(payload.restrictionLevel)
        : 0,
      value: payload.pay * 100,
      budget: payload.maxSpend * 100,
      description: payload.description,
      name: payload.name,
      experiment_type: experimentType,
      product_id: payload.product,
      product_ids: [payload.product]
    };

    // Add the appropriate product information based on experiment type
    if (isTriangleTest) {
      console.log('Formatting Triangle Test payload with products:', payload.products);
      formattedPayload.products = payload.products;
      formattedPayload.product_ids = payload.products;
      formattedPayload.ttInstructions = payload.instructions;
      
      // Use the stored triangle test data if available, or generate new ones with the seed
      if (this.triangleTestData) {
        formattedPayload.ttData = this.triangleTestData.tests;
        formattedPayload.ttSeed = this.triangleTestData.seed;
      } else if (payload.seed) {
        // Generate triangle tests with the seed from the form if no stored data
        formattedPayload.ttData = this.ttService.generateTriangleTests(payload.products, payload.seed);
        formattedPayload.ttSeed = payload.seed;
      } else {
        // Fallback in case no seed is available
        const seed = Math.floor(Math.random() * 1000000) + 1;
        formattedPayload.ttData = this.ttService.generateTriangleTests(payload.products, seed);
        formattedPayload.ttSeed = seed;
      }
      
      console.log(`Triangle test data generated with seed ${formattedPayload.ttSeed}`);
    } else {
      console.log('Formatting Normal Sensory payload with product_id:', payload.product);
      formattedPayload.product_id = payload.product;
    }

    return formattedPayload;
  }

  private validateCurrentStep(): boolean {
    const currentStep = `step${this.formStep}` as keyof typeof this.formErrors;
    console.log(`Validating step ${this.formStep}`);

    // Get the appropriate form based on the current step
    const form = this.getCurrentForm();
    console.log(`Form valid: ${form.valid}`);

    // For step 2, handle Triangle Test validation separately
    if (this.formStep === 2) {
      const experimentType = this.step1Form.get('experiment_type')?.value;
      const isTriangleTest = experimentType === 'Triangle Test';
      console.log(`Step 2 validation - Experiment type: ${experimentType}`);

      if (isTriangleTest) {
        // Triangle Test validation
        const products = this.step2FormTt.get('products')?.value;
        console.log(`Triangle Test products: ${JSON.stringify(products)}`);

        const productCount = Array.isArray(products) ? products.length : 0;
        const isValid = productCount >= 2 && productCount <= 3;
        console.log(`Triangle Test validation result: ${isValid}, product count: ${productCount}`);

        // Update error states
        this.formErrors['step2'] = {
          ...this.formErrors['step2'],
          products: !isValid,
          product: false // Reset regular product validation
        };

        // Update error message if needed
        if (!isValid) {
          if (productCount < 2) {
            this.errorMessage = 'Please select at least two products for the triangle test';
          } else if (productCount > 3) {
            this.errorMessage = 'Please select no more than three products for the triangle test';
          }
        }

        return isValid;
      } else {
        // Regular product validation
        const product = this.step2Form.get('product')?.value;
        console.log(`Regular product: ${product}`);

        const isValid = !!product;
        console.log(`Regular product validation result: ${isValid}`);

        // Update error states
        this.formErrors['step2'] = {
          ...this.formErrors['step2'],
          product: !isValid,
          products: false // Reset triangle test validation
        };

        return isValid;
      }
    }

    // Add special validation for step 5
    if (this.formStep === 5) {
      const maxSpend = form.get('maxSpend')?.value;
      const pay = form.get('pay')?.value;
      const customPay = form.get('customPay')?.value;
      const payValue = customPay ? pay : Number(pay);

      if (!maxSpend || maxSpend < payValue) {
        console.log(`Step 5 validation failed: maxSpend (${maxSpend}) < payValue (${payValue})`);
        this.formErrors['step5']['maxSpend'] = true;
        return false;
      }

      console.log('Step 5 validation passed');
      return true;
    }

    // For other steps, use standard validation
    let isValid = form.valid;
    console.log(`Standard validation result: ${isValid}`);

    // Update individual field errors
    Object.keys(this.formErrors[currentStep]).forEach((fieldName) => {
      const control = form.get(fieldName);
      if (control) {
        const fieldValid = !control.invalid;
        console.log(`Field ${fieldName} valid: ${fieldValid}`);

        this.formErrors[currentStep][
          fieldName as keyof (typeof this.formErrors)[typeof currentStep]
        ] = control.invalid;
      }
    });

    return isValid;
  }

  showError(): void {
    this.hasError = true;
    switch (this.formStep) {
      case 2:
        // Check if this is a Triangle Test
        const experimentType = this.step1Form.get('experiment_type')?.value;
        const isTriangleTest = experimentType === 'Triangle Test';

        if (isTriangleTest) {
          const products = this.step2FormTt.get('products')?.value;
          const productCount = Array.isArray(products) ? products.length : 0;

          if (productCount < 2) {
            this.errorMessage = 'Please select at least two products for the triangle test';
          } else if (productCount > 3) {
            this.errorMessage = 'Please select no more than three products for the triangle test';
          } else {
            this.errorMessage = 'Please select 2-3 products for the triangle test';
          }
        } else {
          this.errorMessage = 'Please select a product before proceeding';
        }
        break;
      case 3:
        this.errorMessage =
          'Please select a consumption method before proceeding';
        break;
      case 5:
        this.errorMessage = 'Maximum spend must be greater than pay per panel';
        break;
      default:
        this.errorMessage =
          'Please complete all required fields before proceeding.';
        break;
    }
  }

  nextStep() {
    console.log(`Validating step ${this.formStep}...`);

    // For step 2, log the experiment type and form values
    if (this.formStep === 2) {
      const experimentType = this.step1Form.get('experiment_type')?.value;
      const isTriangleTest = experimentType === 'Triangle Test';
      // console.log(`Experiment type: ${experimentType}, isTriangleTest: ${isTriangleTest}`);

      // if (isTriangleTest) {
      //   console.log('Triangle test products:', this.step2FormTt.get('products')?.value);
      // } else {
      //   console.log('Regular product:', this.step2Form.get('product')?.value);
      // }
    }

    if (this.validateCurrentStep()) {
      // console.log(`Step ${this.formStep} validation passed, moving to next step`);
      this.stepSubject.next('next');
    } else {
      // console.log(`Step ${this.formStep} validation failed, showing error`);
      this.showError();
    }
  }

  prevStep() {
    const currentStep = `step${this.formStep}` as keyof typeof this.formErrors;
    // Reset current step's error state
    if (currentStep !== 'step6') {
      Object.keys(this.formErrors[currentStep]).forEach((fieldName) => {
        this.formErrors[currentStep][
          fieldName as keyof (typeof this.formErrors)[typeof currentStep]
        ] = false;
      });
    }

    this.stepSubject.next('prev');
  }

  save() {
    this.saveSubject.next();
  }

  setFormStep(step: number): void {
    if (step >= 1 && step <= this.totalSteps) {
      // const currentStep = `step${this.formStep}` as keyof typeof this.formErrors;
      // // Reset current step's error state
      // Object.keys(this.formErrors[currentStep]).forEach(fieldName => {
      //   this.formErrors[currentStep][fieldName as keyof typeof this.formErrors[typeof currentStep]] = false;
      // });

      this.formStep = step;
      this.scrollResetSubject.next();
    }
  }

  reviewStep() {
    this.formStep = 6;
    this.scrollResetSubject.next();
  }

  filter(event: any) {
    // console.log(event);
  }

  search(event: any) {
    // console.log(event);
  }

  add(event: any) {
    // console.log(event);
  }

  getPanels(params: PaginationParams) {
    return this.apiService.getPanels(params).pipe(
      map((response: any) => {
        // console.log('response', response);
        this.panels = response.panels;
        this.panelsObject = response.panels.reduce(
          (acc: { [key: string]: any }, panel: any) => {
            acc[panel.id] = panel;
            return acc;
          },
          {}
        );
        return response;
      })
    );
  }

  async getPanelComments(id: string) {
    const comments = await this.apiService.getPanelComments(id);
    // console.log(comments);
    return comments;
  }

  getPanelInsights(id: string) {
    return this.apiService.getPanelInsights(id);
  }

  /**
   * Get triangle test aggregate data for a panel
   * @param id The panel ID
   * @returns Promise with triangle test data including chi-squared statistics
   */
  async getTriangleTestAggregates(id: string) {
    return await this.apiService.getTriangleTestAggregates(id);
  }

  getPanel(id: string) {
    return this.apiService.getPanel(id);
  }

  getResponseCount(id: string) {
    return this.apiService.getResponseCount(id, 'panels');
  }

  async savePanel(panel: any) {
    const formattedPanel = this.formatPayload(panel);
    // console.log(formattedPanel);
    if (this.editing) {
      return this.apiService.updatePanel(formattedPanel, this.editingPanel.id);
    } else {
      let new_panel: any = await this.apiService.savePanel(formattedPanel);
      if (new_panel) {
        this.panelsObject[new_panel['panel_id']] = new_panel;
        this.panels.push(new_panel);
        if(this.template){
          if(!this.template.usedBy){
            this.template.usedBy = [];
          }
          // console.log(this.template['usedBy']);
          this.apiService.updateTemplate(this.template.id, {usedBy: [...this.template.usedBy, new_panel['panel_id']]});
        }
      } else {
        //TODO: Show some error message here
      }
      this.setPage(this.page);
      return new_panel;
    }
  }

  isFieldInvalid(formStep: number, fieldName: string): boolean {
    const stepKey = `step${formStep}` as keyof typeof this.formErrors;
    return this.formErrors[stepKey][
      fieldName as keyof (typeof this.formErrors)[typeof stepKey]
    ];
  }

  private getCurrentForm(): FormGroup {
    // Check if this is a Triangle Test
    const experimentType = this.step1Form.get('experiment_type')?.value;
    const isTriangleTest = experimentType === 'Triangle Test';
    switch (this.formStep) {
      case 1:
        return this.step1Form;
      case 2:
        // Return the appropriate form based on experiment type
        return isTriangleTest ? this.step2FormTt : this.step2Form;
      case 3:
        return isTriangleTest ? this.step3FormTt : this.step3Form;
      case 4:
        return this.step4Form;
      case 5:
        return this.step5Form;
      default:
        return this.step1Form;
    }
  }

  private initializeConditionalValidation(): void {
    // Get form controls
    const startOptionControl = this.step1Form.get('startOption');
    const startDateControl = this.step1Form.get('start_date');
    const endOptionControl = this.step1Form.get('endOption');
    const endDateControl = this.step1Form.get('end_date');

    // Subscribe to startOption changes
    startOptionControl?.valueChanges.subscribe((value) => {
      if (value === 'custom') {
        startDateControl?.setValidators(Validators.required);
      } else {
        startDateControl?.clearValidators();
      }
      startDateControl?.updateValueAndValidity();
    });

    // Subscribe to endOption changes
    endOptionControl?.valueChanges.subscribe((value) => {
      if (value === 'custom') {
        endDateControl?.setValidators(Validators.required);
      } else {
        endDateControl?.clearValidators();
      }
      endDateControl?.updateValueAndValidity();
    });
  }

  private initializeFormValueChangeSubscriptions(): void {
    // Subscribe to each form's valueChanges
    [
      this.step1Form,
      this.step2Form,
      this.step3Form,
      this.step4Form,
      this.step5Form,
    ].forEach((form, index) => {
      form.valueChanges.subscribe(() => {
        const stepKey = `step${index + 1}` as keyof typeof this.formErrors;

        // Reset all error flags for the current step
        Object.keys(this.formErrors[stepKey]).forEach((fieldName) => {
          this.formErrors[stepKey][
            fieldName as keyof (typeof this.formErrors)[typeof stepKey]
          ] = false;
        });

        this.hasError = false;
        this.errorMessage = '';
      });
    });
  }

  private createPanelsStream() {
    return this.pageSubject.pipe(
      switchMap((page) =>
        this.getPanels({
          page,
          limit: this.limitSubject.getValue(),
        })
      )
    );
  }

  public setPage(page: number) {
    this.page = page;
    this.pageSubject.next(page);
  }

  public setLimit(limit: number) {
    this.limitSubject.next(limit);
    this.setPage(1);
  }

  endPanel(panel: any) {
    // console.log(panel);
  }

  deletePanel(panel: any) {
    // console.log(panel);
  }

  async getAllDescriptors() {
    let descriptors = this.constantsService.flavors;
    descriptors.sort((a: any, b: any) => {
      return a.priority - b.priority;
    });
    return descriptors;
  }

  async getResponseDescriptors(
    responseDetails: any,
    calculatedStats: any = null
  ) {
    let descriptors = this.constantsService.flavors;
    let arr: any[] = [];
    if (!calculatedStats) {
      descriptors.forEach((descriptor: any) => {
        if (responseDetails.hasOwnProperty(descriptor.option_id + '_count')) {
          arr.push({
            descriptor,
            count: responseDetails[descriptor.option_id + '_count'],
            sum: responseDetails[descriptor.option_id + '_sum'],
          });
        }
      });
    } else {
      arr = calculatedStats;
    }
    return arr.sort((a: any, b: any) => {
      return a.descriptor.priority - b.descriptor.priority;
    });
  }

  getResponseDescriptorAverages(
    responseDescriptors: any[],
    responseDetails: any
  ) {
    responseDescriptors.forEach((descriptor: any) => {
      descriptor.avg =
        descriptor.sum / descriptor.count /*  / responseDetails.count */;
    });
    return responseDescriptors;
  }
}
