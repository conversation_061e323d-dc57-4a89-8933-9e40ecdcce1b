import { Component, Input, inject, EventEmitter, Output } from '@angular/core';
import { NgIf, NgFor } from '@angular/common';
import { DigitToWordPipe } from '../../pipes/digit-to-word.pipe';
import { InputRadioStandardComponent } from '../input-radio-standard/input-radio-standard.component';
import { InputTextComponent } from '../input-text/input-text.component';
import { InputTextAreaComponent } from '../input-text-area/input-text-area.component';
import { FormControl, FormArray, FormGroup } from '@angular/forms';

@Component({
  selector: 'custom-step-question',
  standalone: true,
  imports: [
    NgIf,
    DigitToWordPipe,
    InputRadioStandardComponent,
    InputTextComponent,
    InputTextAreaComponent,
    NgFor
  ],
  templateUrl: './custom-step-question.component.html',
  styleUrl: './custom-step-question.component.scss'
})
export class CustomStepQuestionComponent {

  @Input() question: any;
  @Input() index: number = 0;
  @Input() stepType: string = '';

  @Output() removeQuestionEvent = new EventEmitter<number>();

  ngOnInit(){
    // console.log('question', this.question);

  }

  removeQuestion(){
    this.removeQuestionEvent.emit(this.index);
  }

  addOption(){
    this.question.controls['optionControlArray'].push(new FormControl(''));
  }

  removeOption(index: number){
    this.question.controls['optionControlArray'].removeAt(index);
  }
}
