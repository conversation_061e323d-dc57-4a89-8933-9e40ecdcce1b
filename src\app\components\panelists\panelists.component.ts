import { Component, inject } from '@angular/core';
import { PageHeaderComponent } from '../page-header/page-header.component';
import { PanelistsService } from '../../services/panelists.service';
import { PanelistsTableComponent } from '../panelists-table/panelists-table.component';
@Component({
  selector: 'panelists',
  standalone: true,
  imports: [PageHeaderComponent, PanelistsTableComponent],
  templateUrl: './panelists.component.html',
  styleUrl: './panelists.component.scss'
})
export class PanelistsComponent {

  panelistsService = inject(PanelistsService);
}
