import { Component, OnInit } from '@angular/core';
import { ToastService } from "../../services/toast.service";
import { CommonModule } from '@angular/common';


@Component({
  selector: 'app-toast',
  standalone: true,
  templateUrl: './toast.component.html',
  styleUrls: ['./toast.component.scss'],
  imports: [CommonModule]
})

export class ToastComponent implements OnInit {
  // message: any = "init";
  // showToast = false;
  // burnt: boolean = false;

  constructor(public toastService: ToastService) { }

  ngOnInit() {
    // this.toastService.toastState.subscribe(message => {
    //   this.message = message;
    //   this.showToast = true;
    //   setTimeout(() => {
    //     this.showToast = false;
    //     this.scrapeBurntToast();
    //   }, 20000); // Wait for 3 seconds
    // });
  }

  // scrapeBurntToast(){
    // setTimeout(() => this.toastService.burnt = false, 1000);
  // }

}
