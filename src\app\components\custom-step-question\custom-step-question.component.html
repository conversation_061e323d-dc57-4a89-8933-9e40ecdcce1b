<div class="question-header">
    <button *ngIf="index > 0" class="remove-question-button" type="button" (click)="removeQuestion()">
        <svg xmlns="http://www.w3.org/2000/svg" width="11" height="11" viewBox="0 0 11 11" fill="none">
            <path d="M5.5 4.3886L9.6587 0.229905C9.96523 -0.0766349 10.4629 -0.0766349 10.7701 0.229905C11.0766 0.537118 11.0766 1.03475 10.7701 1.3413L6.6114 5.5L10.7701 9.6587C11.0766 9.96523 11.0766 10.4629 10.7701 10.7701C10.4629 11.0766 9.96525 11.0766 9.6587 10.7701L5.5 6.6114L1.3413 10.7701C1.03477 11.0766 0.537135 11.0766 0.229905 10.7701C-0.0766349 10.4629 -0.0766349 9.96525 0.229905 9.6587L4.3886 5.5L0.229905 1.3413C-0.0766349 1.03477 -0.0766349 0.537135 0.229905 0.229905C0.537118 -0.0766349 1.03475 -0.0766349 1.3413 0.229905L5.5 4.3886Z" fill="#292727"/>
        </svg>
    </button>
    <div class="question-label">Question {{index + 1 | digitToWord: true}}</div>
    <div class="required-toggle-container">
        <input-radio-standard [control]="question.controls['required']" [id]="'required-control'" [type]="'checkbox'">Required</input-radio-standard>
    </div>
</div>

<div class="input-container">
    <label for="question-label"><span>Question</span></label>
    <input-text [id]="'question-label'" [control]="question.controls['question']" [placeholder]="'What\'s the question?'"></input-text>
</div>

<div class="input-container">
    <label for="question-instructions">
        <span>Question Instructions</span>
        <div class="sub-label">
            <span>Optional</span>
        </div>
    </label>
    <input-text-area [id]="'question-instructions'" [control]="question.controls['instructions']" [placeholder]="'instructions'"></input-text-area>
</div>

<div class="input-container slider-labels-container" *ngIf="stepType === 'range-slider'">
    <label for="question-options">
        <span>Slider Labels</span>
        <div class="sub-label">
            <span>Optional</span>
        </div>
    </label>
    <div class="option-container">
        <input-text [id]="'question-options'" [control]="question.controls['sliderLeft']" [placeholder]="'left label'"></input-text>
        <input-text [id]="'question-options'" [control]="question.controls['sliderCenter']" [placeholder]="'center label'"></input-text>
        <input-text [id]="'question-options'" [control]="question.controls['sliderRight']" [placeholder]="'right label'"></input-text>
    </div>
</div>

<div class="input-container" *ngIf="stepType === 'multiple-choice' || stepType === 'select-one'">
    <label for="question-options"><span>Options</span></label>
    <div class="option-container" *ngFor="let option of question.controls['optionControlArray'].controls; let i = index">
        <button class="remove-option-button" type="button" (click)="removeOption(i)" *ngIf="i > 0">
            <svg xmlns="http://www.w3.org/2000/svg" width="11" height="11" viewBox="0 0 11 11" fill="none">
                <path d="M5.5 4.3886L9.6587 0.229905C9.96523 -0.0766349 10.4629 -0.0766349 10.7701 0.229905C11.0766 0.537118 11.0766 1.03475 10.7701 1.3413L6.6114 5.5L10.7701 9.6587C11.0766 9.96523 11.0766 10.4629 10.7701 10.7701C10.4629 11.0766 9.96525 11.0766 9.6587 10.7701L5.5 6.6114L1.3413 10.7701C1.03477 11.0766 0.537135 11.0766 0.229905 10.7701C-0.0766349 10.4629 -0.0766349 9.96525 0.229905 9.6587L4.3886 5.5L0.229905 1.3413C-0.0766349 1.03477 -0.0766349 0.537135 0.229905 0.229905C0.537118 -0.0766349 1.03475 -0.0766349 1.3413 0.229905L5.5 4.3886Z" fill="#292727"/>
            </svg>
        </button>
        <div class="option-label">Option {{i + 1}}</div>
        <input-text [id]="'question-options'" [control]="option" [placeholder]="'option text'"></input-text>
    </div>
    <button class="add-option-button" type="button" (click)="addOption()">
        <div class="plus-container">
            <svg xmlns="http://www.w3.org/2000/svg" width="10" height="10" viewBox="0 0 10 10" fill="none">
                <path d="M4.11765 0V4.11765H0V5.88235H4.11765V10H5.88235V5.88235H10V4.11765H5.88235V0H4.11765Z" fill="#5D5D5D"/>
            </svg>
        </div>
        <div class="add-option-text">Add option</div>
    </button>
</div>