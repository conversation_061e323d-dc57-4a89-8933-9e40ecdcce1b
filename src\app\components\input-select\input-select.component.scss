@import '../../../_colors.scss';

:host{
    display: block;
    width: 265px;
    height: 40px;

    select{
        appearance: none;
        -webkit-appearance: none;
        -moz-appearance: none;
        border: none;
        outline: none;
        background: none;
        padding: 0;
        margin: 0;
        border-radius: 0;
        font-family: inherit;
        color: inherit;
        box-sizing: border-box;
        background-color: $inputBackground;
        background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='46' height='46' viewBox='0 0 46 46' fill='none'%3E%3Cpath d='M28.4216 15.7294C28.6482 15.5027 28.7846 15.1858 28.7846 14.8688C28.7846 14.5519 28.6482 14.235 28.4216 14.0083C27.9222 13.5089 27.152 13.5089 26.6987 14.0083L22.665 18.0419L18.6314 14.0083C18.132 13.5089 17.3618 13.5089 16.9085 14.0083C16.4552 14.5077 16.4091 15.2779 16.9085 15.7312L21.8046 20.6255C22.3039 21.1248 23.0742 21.1248 23.5275 20.6255L28.4216 15.7294Z' fill='%2340116F'/%3E%3C/svg%3E");
        background-repeat: no-repeat;
        background-position: right -10px top -2px;
        background-size: 60px;
        outline: $inputBorder;
        width: 100%;
        height: 100%;
        padding: 7px 10px;
        border-radius:10px;
        font-size: 13px;
        color: $appText;

        &:focus{
            outline: $inputFocusBorder;
        }

        &::placeholder{
            color: $placeholderText;
            font-size: .75rem;
            font-weight: 400;
        }

        &:has(option:disabled:checked) {
            color: $placeholderText;
        }
    }
}