import { CanActivateFn, Router } from '@angular/router';
import { inject } from '@angular/core';
import { Observable, of } from 'rxjs';
import { catchError, take } from 'rxjs/operators';
import { Auth, onAuthStateChanged } from '@angular/fire/auth';
import { ApiService } from '../services/api.service';
// import { RedirectService } from '../services/redirect.service';

export const authGuard: CanActivateFn = (route, state) => {
  const auth = inject(Auth);
  const router = inject(Router);
  const apiService = inject(ApiService);

  //TODO: Potentially add in an expiry value in a session service so we don't have to check the session every time
  return new Observable<boolean>(subscriber => {
    apiService.checkSession().pipe(
      catchError(() => of(false))
    ).subscribe(hasValidSession => {
      console.log('hello');
      
      if (hasValidSession) {
        // Valid session exists, allow access
        subscriber.next(true);
        subscriber.complete();
        return;
      }
      onAuthStateChanged(auth, async (user) => {
        if (user) {
          const session = await apiService.getSession(await user.getIdToken());
          console.log('session', session);
          console.log('hello, has user');
          subscriber.next(true);
        } else {
          console.log('hello, no user');
          router.navigate(['/login']); // or your login route
          subscriber.next(false);
        }
        subscriber.complete();
      });
    });
  });
};
