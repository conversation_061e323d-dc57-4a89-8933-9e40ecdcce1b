import { Component, inject, ElementRef, HostListener, signal, computed, effect, Signal, WritableSignal } from '@angular/core';
import { CommonModule } from '@angular/common';
import { PanelsService } from '../../services/panels.service';
import { ConstantsService } from '../../services/constants.service';
import { FormControl, FormArray, ReactiveFormsModule } from '@angular/forms';
import { InputRadioBlockComponent } from '../input-radio-block/input-radio-block.component';
import { InputSelectComponent } from '../input-select/input-select.component';
import { DndModule, DndDropEvent, EffectAllowed } from 'ngx-drag-drop';
import { firstValueFrom, Subscription } from 'rxjs';
import { PanelDetailsComponent } from "../panel-details/panel-details.component";
import { PopupService } from '../../services/popup.service';
import { DigitToWordPipe } from '../../pipes/digit-to-word.pipe';
import { LambTableComponent } from '@lamb-sensory/lamb-component-library';
import { CustomStepListPopoverComponent } from '../custom-step-list-popover/custom-step-list-popover.component';
import { fadeScaleInOut, fadeInOut } from '../../modules/animations/animations.module';
import { QuestionsService } from '../../services/questions.service';
import { QuestionAdapter } from '../../adapters/question.adapter';
import { PanelStep } from '../../interfaces/panel-step.interface';

@Component({
  selector: 'new-panel-step-3',
  standalone: true,
  imports: [
    InputRadioBlockComponent,
    InputSelectComponent,
    ReactiveFormsModule,
    CommonModule,
    DndModule,
    PanelDetailsComponent,
    DigitToWordPipe,
    LambTableComponent,
    CustomStepListPopoverComponent
  ],
  templateUrl: './new-panel-step-3.component.html',
  styleUrl: './new-panel-step-3.component.scss',
  animations: [fadeScaleInOut, fadeInOut]
})
export class NewPanelStep3Component {

  hostElement = inject(ElementRef);
  panelsService = inject(PanelsService);
  constantsService = inject(ConstantsService);
  popupService = inject(PopupService);
  questionsService = inject(QuestionsService);
  form = this.panelsService.step3Form;
  consumptionMethodControl = this.form.get('consumptionMethod')! as FormControl;
  consumptionOptions = this.constantsService.consumption;
  // questionOrderControl = this.form.get('questionOrder')! as FormControl;
  questionsControl: WritableSignal<FormArray> = signal(this.form.get('questions')! as FormArray);

  experimentType: string = 'Normal Sensory';

  draggable = {
    // note that data is handled with JSON.stringify/JSON.parse
    // only set simple data or POJO's as methods will be lost
    data: "myDragData",
    effectAllowed: "all" as EffectAllowed,
    disable: false,
    handle: false
  };

  questionItems: Signal<any[]> = computed(() => this.questionsControl().value);
  // questionItems: any[] = [];
  // questionsEffect = effect(() => {
  //   this.questionItems = this.questionsControl().value;
  // })
  initialQuestionOrder: any[] = [];
  subscription: Subscription = new Subscription();
  addStepPopover: boolean = false;
  customStepListPopover: boolean = false;
  questionAdapter = new QuestionAdapter();
  // orgQuestions: any[] = [];

  constructor() {
      console.log('questionItems', this.questionItems);
  }

  async ngOnInit(): Promise<void> {

    this.experimentType = this.panelsService.step1Form.get('experiment_type')?.value || 'Normal Sensory';
    await this.getCustomQuestions();
    await this.checkQuestionsFormArray();
    this.initializeQuestionArrays();

    console.log('QQQ: ', this.questionsControl().value);
    console.log('Experiment type in Step 3:', this.experimentType);
  }

  getQuestionName(question: string){
    // console.log('this.questionsService.adaptedQuestionsObject', this.questionsService.adaptedQuestionsObject);
    
    return this.questionsService.adaptedQuestionsObject[question].name;
  }

  async getCustomQuestions(): Promise<void> {
    this.questionsService.customQuestions = await firstValueFrom(this.questionsService.getQuestions());
    console.log('customQuestions', this.questionsService.customQuestions);
    
    const adaptedDefaults = await this.questionAdapter.adaptMany(this.questionsService.defaultQuestions, this.questionAdapter.defaultToPanelStep);
    const adaptedCustoms = await this.questionAdapter.adaptMany(this.questionsService.customQuestions, this.questionAdapter.customToPanelStep);
    this.questionsService.adaptedQuestions = [...adaptedDefaults, ...adaptedCustoms];
    this.questionsService.adaptedQuestionsObject = this.questionsService.adaptedQuestions.reduce((acc: any, question: PanelStep) => {
      acc[question.id] = question;
      return acc;
    }, {});
    console.log('adaptedQuestionsObject', this.questionsService.adaptedQuestionsObject);
    return;
  }

  openAddStepPopover(): void {
    this.addStepPopover = true;
  }

  closeAddStepPopover(): void {
    this.addStepPopover = false;
  }

  openCustomStepListPopover(): void {   
    this.addStepPopover = false;
    setTimeout(() => {
      this.customStepListPopover = true;
    }, 100);
  }

  closeCustomStepListPopover(): void {
    this.customStepListPopover = false;
  }

  backToAddStepPopover(): void {
    this.customStepListPopover = false;
    setTimeout(() => {
      this.addStepPopover = true;
    }, 100);
  }

  @HostListener('document:click', ['$event'])
  onDocumentClick(event: MouseEvent) {
    // Check if click target is outside both popovers
    const addStepPopoverElement = document.querySelector('.add-step-popover');
    const customStepListElement = document.querySelector('custom-step-list-popover');

    if (
      (this.addStepPopover && addStepPopoverElement && !addStepPopoverElement.contains(event.target as Node)) ||
      (this.customStepListPopover && customStepListElement && !customStepListElement.contains(event.target as Node))
    ) {
      this.addStepPopover = false;
      this.customStepListPopover = false;
    }
  }

  getDefaultQuestions(): string[] {
    let questions = this.constantsService.questions.map((q: any) => q.id);
    questions.sort((a: any, b: any) => {
      return this.constantsService.defaultQuestionOrder.indexOf(a) - this.constantsService.defaultQuestionOrder.indexOf(b);
    });
    return questions;
  }

  initializeQuestionArrays(): void {
    let questions = this.questionsControl().value;
    this.questionItems = questions;
    this.initialQuestionOrder = [...questions];
  }

  async checkQuestionsFormArray(): Promise<void> {
    let questions = this.questionsControl().value;
    
    if(questions.length === 0) {
      this.buildQuestionsFormArray(this.getDefaultQuestions());
    }else{
      // console.log('questions', questions);
      this.buildQuestionsFormArray(questions);
    }
    return;
  }

  buildQuestionsFormArray(questions: string[]): void {
    // console.log('hello', questions);
    
    this.questionsControl().clear();
    questions.forEach(question => {
      this.questionsControl().push(new FormControl(question));
    });
    // console.log('this.questionsControl', this.questionsControl);
    
  }

  ngOnDestroy(): void {
    this.subscription.unsubscribe();
  }

  onDrop(event: DndDropEvent) {
    if (event.dropEffect === 'move') {
      console.log('event', event);
      console.log('this.questionsControl', this.questionsControl().value);
      const fromIndex = this.questionsControl().value.findIndex((item:string[]) => item === event.data);
      
    //   // Get the drop target's data-index attribute
      const dropTarget = (event.event.target as HTMLElement).closest('.question-item');
      const toIndex = dropTarget ? parseInt(dropTarget.getAttribute('data-index') || '0', 10) : 0;

      if (fromIndex !== -1) {
        // Remove item from original position and insert at new position
        const [item] = this.questionsControl().value.splice(fromIndex, 1);
        this.questionsControl().value.splice(toIndex, 0, item);
        
        // Update the form control value to match the new order
        // this.questionsControl().setValue(this.questionItems());
      }
    }    
  }

  moveItemUp(index: number): void {
    if (index > 0) {
      // Swap current item with the one above it
      [this.questionsControl().value[index], this.questionsControl().value[index - 1]] = 
      [this.questionsControl().value[index - 1], this.questionsControl().value[index]];
      
  //     // Update the form control value
  //     this.questionsControl().setValue(this.questionItems);
    }
  }

  moveItemDown(index: number): void {
    if (index < this.questionsControl().value.length - 1) {
      // Swap current item with the one below it
      [this.questionsControl().value[index], this.questionsControl().value[index + 1]] = 
      [this.questionsControl().value[index + 1], this.questionsControl().value[index]];
      
  //     // Update the form control value
  //     this.questionsControl().setValue(this.questionItems);
    }
  }

  removeQuestion(index: number): void {
    // this.questionItems.splice(index, 1);
    this.questionsControl().removeAt(index);
    // this.questionItems = this.questionsControl().value;
  }

  addStep(): void {
    this.popupService.openCustomStepForm('new');
  }

  editStep(stepKey: any): void{
    this.popupService.openCustomStepForm('edit', stepKey);
  }

  onStepSelected(step: any) {
    console.log('step', step, this.questionsControl().value);
    this.questionsControl().push(new FormControl(step.id));
    this.closeCustomStepListPopover();
  }

  onViewStep(step: any) {
    console.log('step', step);
    this.popupService.openCustomStepForm('add', step.id);
  }
}
