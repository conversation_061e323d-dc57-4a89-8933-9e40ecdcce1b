@import '../../../styles.scss';

:host{
    // @extend %fullscreen-fixed;
    // @extend %settings-host;
    display: block;
    .settings-section{

        .section-content{

            .current-email{
                font-size: .9rem;
                font-weight: 700;
                margin-bottom: 10px;
            }
            
            .verification-status-container{
                margin-bottom: 20px;

                .status{
                    display: flex;
                    align-items: center;
                    gap: 5px;
                    font-size: .8rem;
                    font-weight: 700;
                    margin-bottom: 5px;
                }

                .resend-verification{
                    font-size: .8rem;
                    font-weight: 500;
                    margin-left: 18px;

                    span{
                        text-decoration: underline;
                        cursor: pointer;
                    }
                }
            }

            .inputs-section{

                input{
                    width: 100%;
                }
            }

            button-standard{
                width: 100%;
                margin-top: 30px;
            }

            .text{
                font-size: .9rem;
                text-align: center;
                margin-bottom: 30px;
            }
        }
    }
}