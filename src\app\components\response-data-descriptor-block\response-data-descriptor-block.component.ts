import { Component, Input, inject } from '@angular/core';
import { StatisticsService } from '../../services/statistics.service';
import { CommonModule } from '@angular/common';
@Component({
  selector: 'response-data-descriptor-block',
  standalone: true,
  imports: [CommonModule],
  templateUrl: './response-data-descriptor-block.component.html',
  styleUrl: './response-data-descriptor-block.component.scss'
})
export class ResponseDataDescriptorBlockComponent {

  @Input() descriptor: any;
  // @Input() responseDetails: any;
  statisticsService = inject(StatisticsService);

  value: number = 0;

  ngOnInit(){
    // console.log(this.descriptor);
    // console.log(this.statisticsService.calculatedStats[this.descriptor.descriptor.option_id]);
    this.value = this.statisticsService.calculatedStats[this.descriptor.descriptor.option_id].shrinkageEstimate;
  }
}
