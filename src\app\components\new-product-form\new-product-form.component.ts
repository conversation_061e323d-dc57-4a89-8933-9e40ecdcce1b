import { Component, inject, ElementRef } from '@angular/core';
import { NgIf } from '@angular/common';
import { PopupService } from '../../services/popup.service';
import { ProductsService } from '../../services/products.service';
import { FormHeaderComponent } from '../form-header/form-header.component';
import { NewProductStep1Component } from '../new-product-step-1/new-product-step-1.component';
import { NewProductStep2Component } from '../new-product-step-2/new-product-step-2.component';
import { NewProductStep3Component } from '../new-product-step-3/new-product-step-3.component';
import { FormFooterComponent } from '../form-footer/form-footer.component';
import { ButtonTwoClickComponent } from '../button-two-click/button-two-click.component';
import { Subscription } from 'rxjs';

@Component({
  selector: 'new-product-form',
  standalone: true,
  imports: [FormHeaderComponent, NewProductStep1Component, NewProductStep2Component, NewProductStep3Component, FormFooterComponent, NgIf, ButtonTwoClickComponent],
  templateUrl: './new-product-form.component.html',
  styleUrl: './new-product-form.component.scss'
})
export class NewProductFormComponent {

  popupService = inject(PopupService);
  productsService = inject(ProductsService);
  private elementRef = inject(ElementRef);

  subscription: Subscription = new Subscription();
  
  ngOnInit() {
    this.subscription = this.productsService.scrollResetSubject.subscribe(() => {
      this.elementRef.nativeElement.scrollTop = 0;
    });
  }

  ngOnDestroy() {
    // Clean up subscription to prevent memory leaks
    this.subscription.unsubscribe();
  }
close(){
    this.popupService.closeNewProduct();
  }
}
