import { Component, inject, signal, WritableSignal } from '@angular/core';
import { PanelsService } from '../../services/panels.service';
import { ReactiveFormsModule, FormControl, FormArray } from '@angular/forms';
import { InputTextAreaComponent } from '../input-text-area/input-text-area.component';
import { InputSelectComponent } from '../input-select/input-select.component';
import { ConstantsService } from '../../services/constants.service';

@Component({
  selector: 'new-panel-step-3-tt',
  standalone: true,
  imports: [
    ReactiveFormsModule,
    InputSelectComponent,
    InputTextAreaComponent,
  ],
  templateUrl: './new-panel-step-3-tt.component.html',
  styleUrl: './new-panel-step-3-tt.component.scss'
})
export class NewPanelStep3TtComponent {

  panelsService = inject(PanelsService);
  constantsService = inject(ConstantsService);

  form = this.panelsService.step3FormTt;
  consumptionMethodControl = this.form.get('consumptionMethod')! as FormControl;
  consumptionOptions = this.constantsService.consumption;
  instructionsControl = this.form.get('instructions')! as FormControl;
  questionsControl: WritableSignal<FormArray> = signal(this.form.get('questions')! as FormArray);

  async ngOnInit(): Promise<void> {
    await this.checkQuestionsFormArray();
  }

  async checkQuestionsFormArray(): Promise<void> {
    let questions = this.questionsControl().value;
    
    if(questions.length === 0) {
      this.buildQuestionsFormArray(["awja0ameTALoE0D4GlWD"]);
    }else{
      // console.log('questions', questions);
      this.buildQuestionsFormArray(questions);
    }
    return;
  }

  buildQuestionsFormArray(questions: string[]): void {
    // console.log('hello', questions);
    
    this.questionsControl().clear();
    questions.forEach(question => {
      this.questionsControl().push(new FormControl(question));
    });
    // console.log('this.questionsControl', this.questionsControl);
    
  }
}
