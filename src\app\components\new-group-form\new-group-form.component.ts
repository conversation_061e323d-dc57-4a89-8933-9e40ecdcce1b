import { Component, ElementRef, inject } from '@angular/core';
import { PopupService } from '../../services/popup.service';
import { GroupsService } from '../../services/groups.service';
import { Subscription } from 'rxjs';
import { FormHeaderComponent } from '../form-header/form-header.component';
import { ButtonTwoClickComponent } from '../button-two-click/button-two-click.component';
import { NgIf } from '@angular/common';
import { FormFooterComponent } from '../form-footer/form-footer.component';
import { NewGroupStep1Component } from '../new-group-step1/new-group-step1.component';

@Component({
  selector: 'new-group-form',
  standalone: true,
  imports: [FormHeaderComponent,FormFooterComponent, NgIf, ButtonTwoClickComponent, NewGroupStep1Component],
  templateUrl: './new-group-form.component.html',
  styleUrl: './new-group-form.component.scss'
})
export class NewGroupFormComponent {

  popupService = inject(PopupService);
  groupsService = inject(GroupsService);
  private elementRef = inject(ElementRef);

  subscription: Subscription = new Subscription();
  
  ngOnInit() {
    this.subscription = this.groupsService.scrollResetSubject.subscribe(() => {
      this.elementRef.nativeElement.scrollTop = 0;
    });
  }

  ngOnDestroy() {
    // Clean up subscription to prevent memory leaks
    this.subscription.unsubscribe();
  }

  close(){
    this.popupService.closeNewGroup();
  }
}
