import { Component, inject } from '@angular/core';
import { CommonModule } from '@angular/common';
import { PanelsService } from '../../services/panels.service';
import { ConstantsService } from '../../services/constants.service';
import { FormControl, ReactiveFormsModule } from '@angular/forms';
import { InputRadioBlockComponent } from '../input-radio-block/input-radio-block.component';
import { InputTextComponent } from '../input-text/input-text.component';
import { fadeShrink } from '../../modules/animations/animations.module';

@Component({
  selector: 'new-panel-step-5',
  standalone: true,
  imports: [CommonModule, ReactiveFormsModule, InputRadioBlockComponent, InputTextComponent],
  templateUrl: './new-panel-step-5.component.html',
  styleUrl: './new-panel-step-5.component.scss',
  animations: [fadeShrink]
})
export class NewPanelStep5Component {
  panelsService = inject(PanelsService);
  constantsService = inject(ConstantsService);
  form = this.panelsService.step5Form;
  payControl = this.form.get('pay')! as FormControl;
  maxSpendControl = this.form.get('maxSpend')! as FormControl;
  panelPayOptions = this.constantsService.panelPayOptions;
  customPayControl = this.form.get('customPay')! as FormControl;

  ngOnInit(){
    console.log(this.payControl.value);
  }

  get maxResponses() {
    return Math.floor(this.maxSpendControl.value / (this.payControl.value === 'custom' ? this.customPayControl.value : this.payControl.value));
  }

  removeCustomPay() {
    this.customPayControl.setValue(false);
    this.payControl.setValue(5);
  }
}
