<!-- <div *ngIf="message" class="toast" [class.show]="showToast" [class.hide]="!showToast">
  <div class="toast-msg" [class.burnt]="toastService.burnt">
    <svg class="perfectly-toasted" width="11" height="9" viewBox="0 0 11 9" fill="none" xmlns="http://www.w3.org/2000/svg"><path d="M9.912 0c-.275.01-.536.13-.727.337C7.162 2.445 5.617 4.207 3.74 6.2l-2-1.757a1.034 1.034 0 0 0-.772-.256c-.28.025-.538.165-.719.388a1.128 1.128 0 0 0 .13 1.55L3.126 8.54c.42.368 1.038.341 1.426-.062 2.275-2.37 3.895-4.265 6.127-6.59.313-.315.409-.799.239-1.217A1.055 1.055 0 0 0 9.912 0Z" fill="#51B449"/></svg>
    <svg class="too-crispy" width="15" height="15" viewBox="0 0 15 15" fill="none" xmlns="http://www.w3.org/2000/svg"><path fill-rule="evenodd" clip-rule="evenodd" d="M13.785 11.499 9.807 7.521l3.978-3.978a1.611 1.611 0 1 0-2.277-2.279L7.528 5.25 3.502 1.215A1.616 1.616 0 1 0 1.215 3.5l4.035 4.03-3.987 3.98a1.608 1.608 0 0 0 0 2.277 1.61 1.61 0 0 0 2.277 0l3.981-3.98 3.978 3.979a1.617 1.617 0 0 0 2.286-2.286Z" fill="#EF3535"/></svg>
    {{ message }}
  </div>
</div> -->

<!-- good toast -->

<!-- bad toast -->

<div class="toast-inner" [class.burnt]="toastService.burnt">
  {{toastService.message}}
</div>
