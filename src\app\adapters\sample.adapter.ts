import { SampleInterface } from "../interfaces/sample.interface";
import { ConstantsService } from "../services/constants.service";
import { ApiService } from "../services/api.service";

export class SampleAdapter {
    constantsService: ConstantsService;
    apiService: ApiService;

    constructor(constantsService: ConstantsService, apiService: ApiService) {
        this.constantsService = constantsService;
        this.apiService = apiService;
    }

    async adapt(data: any): Promise<SampleInterface[]> {
        return await Promise.all(data.map(async (product: any) => ({
            sampleId: product.product_id,
            name: product.name,
            sampleType: this.constantsService.matrixObject[product.product_type]?.name || 'Unknown',
            associatedPanelCount: await this.getPanelCount(product.product_id),
            associatedResponseCount: await this.getResponseCount(product.product_id),
            producer: product.producer || 'Unknown'
        })));
    }

    async getPanelCount(productId: string){
        const panelCount = await this.apiService.getPanelCount(productId) as { count: number };
        return panelCount.count;
    }

    async getResponseCount(productId: string){
        const responseCount = await this.apiService.getResponseCount(productId, "products") as { count: number };
        return responseCount.count;
    }
}