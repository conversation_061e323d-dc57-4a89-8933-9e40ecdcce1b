<div class="form-step-container">
    <div class="triangle-test-layout">

        <form [formGroup]="form">
          <div class="form-step-title">
            Select at least two products to perform a triangle test on
          </div>

          <div class="tt-container">
            <!-- Left side: Product grid with radio selection -->
            <div class="products-section">

              <ng-container *ngIf="products$ | async as productsResponse">
                <div class="product-grid">
                  <div
                    *ngFor="let product of products; let i = index"
                    class="product-item"
                  >
                    <div class="custom-radio-wrapper" [class.active]="isProductSelected(product.id)" (click)="toggleProductSelection(product.id)">
                      <div class="custom-radio">
                        <div class="radio-inner" *ngIf="isProductSelected(product.id)"></div>
                      </div>
                      <product-block [product]="product"></product-block>
                    </div>
                  </div>
                </div>

                <div *ngIf="!products.length" class="no-products">
                  No products available
                </div>
              </ng-container>

              <pagination
                [state]="paginationState$ | async"
                (pageChange)="onPageChange($event)"
              ></pagination>
            </div>

            <!-- Right side: Selected products display -->
          </div>
      </form>
      <div class="selected-products-section">
        <div class="section-header">
          <h3 class="section-title">Selected Sample Codes</h3>
          <div class="info-icon-wrapper" *ngIf="selectedProducts.length >= 2">
            <span class="info-icon">i</span>
            <div class="tooltip-content">
              Each product will be presented using different sample codes in each triangle test to minimize bias. 
              Panelists will see these codes, not the actual product names.
            </div>
          </div>
        </div>

        <ng-container *ngIf="selectedProducts.length > 0">
          <div
            *ngFor="let productId of selectedProducts; let i = index"
            class="selected-product-item"
          >
            <div class="selected-product-header">
              <span class="product-name">{{ getProductName(productId) }}</span>
              <span class="product-type">{{ getProductType(productId) }}</span>
            </div>

            <div class="sample-labels">
              <div class="sample-label-row">
                <span class="label-text">Will be presented with codes:</span>
                <div class="sample-badges">
                  <!-- Get all sample codes for this product and display them -->
                  <ng-container *ngFor="let sampleCode of getSampleCodes(productId); let last = last">
                    <span class="sample-badge">{{ sampleCode }}</span>
                    <span *ngIf="!last" class="and-text">,</span>
                  </ng-container>
                  
                  <!-- If no sample codes are found, show a message -->
                  <span *ngIf="getSampleCodes(productId).length === 0" class="no-samples">
                    Select at least 2 products to generate codes
                  </span>
                </div>
              </div>
            </div>
          </div>
          
          <!-- Display seed information for debugging -->
          <div *ngIf="selectedProducts.length >= 2" class="seed-info">
            <span class="seed-label">Test Seed:</span>
            <span class="seed-value">{{ seed }}</span>
            <div class="seed-tooltip">
              This seed ensures that the same triangle tests will be generated on both client and server.
            </div>
          </div>
        </ng-container>

        <div *ngIf="selectedProducts.length === 0" class="no-selection-message">
          Select products to see sample labeling
        </div>
      </div>
    </div>
</div>
