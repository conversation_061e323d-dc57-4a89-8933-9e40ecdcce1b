import { Component, inject, Template<PERSON>ef, ViewChild, OnInit, HostListener, ElementRef } from '@angular/core';
import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON> } from '@angular/common';
import { ReactiveFormsModule, FormControl, FormArray } from '@angular/forms';
import { ProjectsService } from '../../services/projects.service';
import { PopupService } from '../../services/popup.service';
import { ButtonTwoClickComponent } from '../button-two-click/button-two-click.component';
import { InputTextComponent } from '../input-text/input-text.component';
import { InputRadioBlockComponent } from '../input-radio-block/input-radio-block.component';
import { InputBsDatepickerComponent } from '../input-bs-datepicker/input-bs-datepicker.component';
import { InputRadioStandardComponent } from '../input-radio-standard/input-radio-standard.component';
import { LambTableComponent, <PERSON>er<PERSON><PERSON>, LambGP<PERSON>con<PERSON>omponent, Graph<PERSON>ooltipComponent } from '@lamb-sensory/lamb-component-library';
import { TABLE_STYLES } from '../../config/table-styles.config';
import { Subscription } from 'rxjs';
import { PanelsService } from '../../services/panels.service';
import { PanelInterface } from '../../interfaces/panel.interface';
import { firstValueFrom } from 'rxjs';
import { PanelAdapter } from '../../adapters/panel.adapter';
import { ApiService } from '../../services/api.service';
import { FormFooterComponent } from '../form-footer/form-footer.component';
import { fadeScaleInOut, fadeInOut } from '../../modules/animations/animations.module';
import { ProfileService } from '../../services/profile.service';

interface PanelsState {
  panels: PanelInterface[];
  totalCount: number;
  hasMore: boolean;
  lastLoadedPage: number;
  pageSize: number;
}

interface GroupsState {
  groups: any[];
  totalCount: number;
  hasMore: boolean;
  lastLoadedPage: number;
  pageSize: number;
}

@Component({
  selector: 'new-project-form',
  standalone: true,
  imports: [ReactiveFormsModule, ButtonTwoClickComponent, InputTextComponent, InputRadioBlockComponent, InputBsDatepickerComponent, NgIf, NgFor, LambTableComponent, LambGPIconComponent, InputRadioStandardComponent, FormFooterComponent, GraphTooltipComponent],
  templateUrl: './new-project-form.component.html',
  styleUrl: './new-project-form.component.scss',
  animations: [fadeScaleInOut, fadeInOut]
})
export class NewProjectFormComponent implements OnInit {

  projectsService = inject(ProjectsService);
  popupService = inject(PopupService);
  panelsService = inject(PanelsService);
  apiService = inject(ApiService);
  profileService = inject(ProfileService);

  projectNameControl = this.projectsService.form.get('projectName')! as FormControl;
  descriptionControl = this.projectsService.form.get('description')! as FormControl;
  startOptionControl = this.projectsService.form.get('startOption')! as FormControl;
  startDateControl = this.projectsService.form.get('startDate')! as FormControl;
  endOptionControl = this.projectsService.form.get('endOption')! as FormControl;
  endDateControl = this.projectsService.form.get('endDate')! as FormControl;
  privacyControl = this.projectsService.form.get('privacy')! as FormControl;
  
  tableSelectControl = new FormControl('panels');
  tableStyles = structuredClone(TABLE_STYLES);
  panelsHeaders: HeaderCell[] = [
    {
      value: 'Select',
      key: 'select',
      style: {textAlign: 'center', width: '8ch'}
    },
    {
      value: 'Status',
      key: 'status'
    },
    {
      value: 'Name',
      key: 'panelName'
    },
    {
      value: 'Sample',
      key: 'productImage'
    }
  ];
  groupsHeaders: HeaderCell[] = [
    {
      value: 'Select',
      key: 'select',
      style: {textAlign: 'center', width: '8ch'}
    },
    {
      value: 'GroupName',
      key: 'groupName'
    },
  ];
  tableHeaders: HeaderCell[] = this.panelsHeaders;
  tableData: object[] = [];
  isLoadingMore = false;
  selectedPanelsArray: any[] = this.projectsService.selectedPanelArray;
  selectedGroupsArray: any[] = this.projectsService.selectedGroupsArray;
  selectedUsersArray: any[] = this.projectsService.collaboratorsArray;
  userTableStyles = structuredClone(TABLE_STYLES);

  @ViewChild('selectCellTemplate') selectCellTemplate!: TemplateRef<any>;
  @ViewChild('productImageCellTemplate') productImageCellTemplate!: TemplateRef<any>;
  @ViewChild('selectUserCellTemplate') selectUserCellTemplate!: TemplateRef<any>;
  @ViewChild('modalButton') modalButton!: ElementRef;
  @ViewChild('collaboratorsModal') collaboratorsModal!: ElementRef;
  @ViewChild('tooltip') tooltip!: GraphTooltipComponent;

  mockGroups = [];

  panelsState: PanelsState = {
    panels: [],
    totalCount: 0,
    hasMore: true,
    lastLoadedPage: 0,
    pageSize: 15
  };

  groupsState: GroupsState = {
    groups: [],
    totalCount: 0,
    hasMore: true,
    lastLoadedPage: 0,
    pageSize: 15
  };

  // Add arrays to store data with custom cells
  panelsTableData: object[] = [];
  groupsTableData: object[] = [];

  // Getter for current state based on selected table
  get state(): PanelsState | GroupsState {
    return this.tableSelectControl.value === 'panels' ? this.panelsState : this.groupsState;
  }

  collaboratorsModalOpen: boolean = false;
  modalOffset: {x: number, y: number} = {x: 0, y: 0};
  usersHeaders: HeaderCell[] = [
    {
      value: 'Select',
      key: 'select',
      style: {textAlign: 'center', width: '8ch'}
    },
    {
      value: 'Name',
      key: 'name'
    }
  ]
  users: any[] = [];
  loadingUsers: boolean = false;
  usersSubscription: Subscription = new Subscription();

  tooltipPosition = { x: 0, y: 0 };
  activeTooltipItem: any = null;
  activeTooltipText: string = '';
  constructor() {
  
  }

  async ngOnInit() {
    this.setTableStyles();
    // Load initial panels
    await this.loadMorePanels();
    
    // Load initial groups
    this.getGroups();
    
    // Set up table select control subscription if needed
    this.tableSelectControl.valueChanges.subscribe((value) => {
      this.swapTableHeaders(value);
      this.swapTableData(value);
      this.updateTableHeaderBackground(value);
    });

    this.projectsService.form.valueChanges.subscribe((value) => {
      console.log('value', value);
      this.projectsService.hasError = false;
      this.projectsService.formErrors = {
        projectName: false,
        description: false,
        startDate: false,
        endDate: false,
      };
    })
  }

  ngOnDestroy() {
    if (this.usersSubscription) {
      this.usersSubscription.unsubscribe();
    }
  }

  openCollaboratorsModal() {
    const modalButton = this.modalButton.nativeElement;
    this.modalOffset = {x: modalButton.offsetLeft, y: modalButton.offsetTop};
    this.loadingUsers = true;
    this.getUsers();
    this.collaboratorsModalOpen = true;
  }

  getUsers() {
    this.profileService.getUsersListInMyOrg().then((users$) => {
      this.usersSubscription = users$.subscribe((usersData) => {
        const users = usersData as any[];
        this.users = users.map(user => ({
          name: `${user.first_name} ${user.last_name}`,
          email: user.email,
          role: this.formatRoles(user.roles),
          uid: user.uid
        }));
        this.users = this.insertCustomUserSelectCells(this.users);
        this.loadingUsers = false;
      });
    }).catch(error => {
      console.error('Error loading users', error);
      this.loadingUsers = false;
    });
  }

  formatRoles(roles: string[]): string {
    return roles
      .map(role => role.charAt(0).toUpperCase() + role.slice(1))
      .join(', ');
  }

  insertCustomUserSelectCells(data: any[]) {
    return data.map((user: any) => {
      user['select__template'] = this.selectUserCellTemplate;
      user['select__templateContext'] = user;
      user['select__style'] = {textAlign: 'center'};
      return user;
    });
  }

  toggleUserSelection(selectedUser: any) {
    if(this.selectedUsersArray.includes(selectedUser)) {
      this.selectedUsersArray = this.selectedUsersArray.filter(user => user !== selectedUser);
    } else {
      this.selectedUsersArray.push(selectedUser);
    }
  }

  toggleSelection(item: any) {
    if(this.tableSelectControl.value === 'panels') {
      // For panels, we need to store the raw panel object
      const existingIndex = this.selectedPanelsArray.findIndex(panel => 
        (panel.panel_id && panel.panel_id === item.panel_id) || 
        (panel.id && panel.id === item.id)
      );
      
      if(existingIndex >= 0) {
        this.selectedPanelsArray.splice(existingIndex, 1);
      } else {
        // Make sure we have the full panel data
        const rawPanel = item.raw || item;
        this.selectedPanelsArray.push(rawPanel);
      }
    } else {
      // For groups, we need to store the raw group object for API interaction
      const existingIndex = this.selectedGroupsArray.findIndex(group => 
        (group.group_id && group.group_id === item.groupId) || 
        (group.group_id && group.group_id === item.group_id) ||
        (group.id && group.id === item.id)
      );
      
      if(existingIndex >= 0) {
        this.selectedGroupsArray.splice(existingIndex, 1);
      } else {
        // Make sure we have the full group data
        const rawGroup = item.raw || item;
        this.selectedGroupsArray.push(rawGroup);
      }
    }
  }

  setTableStyles() {
    this.tableStyles.header!.style!.borderRadius = '16px';
    this.tableStyles.body!.style!.background = 'transparent';
    this.tableStyles.body!.row!.style!.borderRadius = '16px';
    this.tableStyles.body!.row!.style!.background = '#EEEEEE';
  }

  updateTableHeaderBackground(value: string | null) {
    this.tableStyles = {
      ...this.tableStyles, 
      header: {...this.tableStyles.header, 
        style: {...this.tableStyles.header!.style, 
          background: value === 'panels' ? "#CBE5FD" : "#CEFFFC", 
          color: value === 'panels' ? "#00519C" : "#10837B",
        }
      }
    }
  }
  async loadMorePanels() {
    try {
      this.isLoadingMore = true;
      const nextPage = this.panelsState.lastLoadedPage + 1;
      const response: any = await firstValueFrom(
        this.panelsService.getPanels({
          page: nextPage,
          limit: this.panelsState.pageSize
        })
      );

      // Update panels state
      this.panelsState.panels = [...this.panelsState.panels, ...response.panels];
      this.panelsState.lastLoadedPage = nextPage;
      this.panelsState.hasMore = response.hasMore;
      this.panelsState.totalCount = response.totalCount;

      // Only adapt and add custom cells for new panels
      if (this.tableSelectControl.value === 'panels') {
        const adapter = new PanelAdapter(this.apiService);
        const adaptedNewPanels = await adapter.adapt(response.panels);
        const newPanelsWithCells = this.insertCustomCells(adaptedNewPanels, 'panels');
        this.panelsTableData = [...this.panelsTableData, ...newPanelsWithCells];
        this.tableData = this.panelsTableData;
      }
    } catch (error) {
      console.error('Error loading more panels:', error);
    } finally {
      this.isLoadingMore = false;
    }
  }

  async loadMoreGroups() {
    if (!this.groupsState.hasMore || this.isLoadingMore) return;

    this.isLoadingMore = true;
    const nextPage = this.groupsState.lastLoadedPage + 1;

    try {
      const params = { 
        page: nextPage, 
        limit: this.groupsState.pageSize 
      };
      
      const response$ = await this.apiService.getGroups(params);
      const response = await firstValueFrom(response$);
      
      // Process response based on API structure
      const groups = response as any[];

      if (groups.length > 0) {
        this.groupsState.groups = [...this.groupsState.groups, ...groups];
        this.groupsState.lastLoadedPage = nextPage;
        this.groupsState.totalCount += groups.length;
        
        // Insert custom cells for the table
        this.groupsTableData = this.insertCustomCells(this.groupsState.groups.map(group => ({
          groupName: group.name,
          groupId: group.group_id,
          // Add any other properties needed for display
          description: group.description,
          raw: group // Store the original group data
        })), 'groups');
        
        // Update the displayed table data if groups tab is selected
        if (this.tableSelectControl.value === 'groups') {
          this.tableData = this.groupsTableData;
        }
      } else {
        this.groupsState.hasMore = false;
      }
    } catch (error) {
      console.error('Error loading groups', error);
    } finally {
      this.isLoadingMore = false;
    }
  }

  swapTableData(value: string | null) {
    if(value === 'panels') {
      if(this.panelsTableData.length > 0) {
        this.tableData = this.panelsTableData;
      } else {
        // Load panels if not yet loaded
        this.loadMorePanels();
      }
    } else if(value === 'groups') {
      if(this.groupsTableData.length > 0) {
        this.tableData = this.groupsTableData;
      } else {
        // Load groups if not yet loaded
        this.getGroups();
      }
    }
  }

  getGroups() {
    this.isLoadingMore = true;
    this.apiService.getGroupsAsync().then((groups$) => {
      firstValueFrom(groups$).then((response) => {
        const groups = response as any[];
        
        if (groups.length > 0) {
          this.groupsState.groups = groups;
          this.groupsState.lastLoadedPage = 1;
          this.groupsState.totalCount = groups.length;
          this.groupsState.hasMore = groups.length >= this.groupsState.pageSize;
          
          // Insert custom cells for the table
          this.groupsTableData = this.insertCustomCells(this.groupsState.groups.map(group => ({
            groupName: group.name,
            groupId: group.group_id,
            description: group.description,
            raw: group // Store the original group data
          })), 'groups');
          
          // Update table data if groups tab is selected
          if (this.tableSelectControl.value === 'groups') {
            this.tableData = this.groupsTableData;
          }
        } else {
          this.groupsState.groups = [];
          this.groupsTableData = [];
          this.tableData = [];
          this.groupsState.hasMore = false;
        }
        
        this.isLoadingMore = false;
      }).catch(error => {
        console.error('Error loading groups', error);
        this.isLoadingMore = false;
      });
    }).catch(error => {
      console.error('Error loading groups', error);
      this.isLoadingMore = false;
    });
  }

  insertCustomCells(data: object[], type: string | null) {
    const newData = data.map((item: Record<string, any>) => {
      item['select__template'] = this.selectCellTemplate;
      item['select__templateContext'] = item;
      item['select__style'] = {textAlign: 'center'}
      if(type === 'panels') {
        item['productImage__template'] = this.productImageCellTemplate;
        item['productImage__templateContext'] = item;
      }
      return item;
    });
    return newData;
  }

  swapTableHeaders(value: string | null) {
    this.tableHeaders = value === 'panels' ? this.panelsHeaders : this.groupsHeaders;
  }

  @HostListener('document:click', ['$event'])
  handleClickOutside(event: MouseEvent) {
    if (this.collaboratorsModalOpen && this.collaboratorsModal && !this.collaboratorsModal.nativeElement.contains(event.target) && !this.modalButton.nativeElement.contains(event.target)) {
      this.collaboratorsModalOpen = false;
    }
  }

  showTooltip(event: MouseEvent, item: any, type: string) {
    const element = event.target as HTMLElement;
    const rect = element.getBoundingClientRect();

    let offset = 15;
    let text = '';
    if(type==='sample'){
      offset = 10;
      text = item.productName;
    } else if(type==='user'){
      text = item.name;
    }
    
    this.tooltipPosition = {
      x: rect.left + offset,
      y: rect.top - 30 // Position above the element with more space
    };
    this.activeTooltipItem = item;
    this.activeTooltipText = text;
  }

  hideTooltip() {
    this.activeTooltipItem = null;
    this.activeTooltipText = '';
  }
}
