@import '../../../_colors';

.bar-container {
  position: relative;
  width: 100%;

  percentage-bar{
    --bar-padding: 5px;
    --bar-height: 40px;

  }
  
  .legend {
    display: flex;
    flex-wrap: wrap;
    gap: 15px;
    margin-top: 8px;
    margin-left: 3px;
    
    .legend-item {
      display: flex;
      align-items: center;
      gap: 6px;
      
      .color-indicator {
        width: 12px;
        height: 12px;
        border-radius: 3px;
      }
      
      .label {
        font-family: Inter, sans-serif;
        font-weight: 500;
        font-size: 12px;
        color: $darkestGray;
      }
      
      &.true {
        .color-indicator {
          background-color: #7CBB7D;
        }
      }
      
      &.false {
        .color-indicator {
          background-color: #C97F7F;
        }
      }
    }
  }
}
