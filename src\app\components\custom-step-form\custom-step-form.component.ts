import { Component, inject, OnInit, <PERSON><PERSON><PERSON><PERSON> } from '@angular/core';
import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON> } from '@angular/common';
import { QuestionsService } from '../../services/questions.service';
import { PopupService } from '../../services/popup.service';
import { LoadingService } from '../../services/loading.service';
import { ButtonTwoClickComponent } from '../button-two-click/button-two-click.component';
import { ButtonStandardComponent } from '../button-standard/button-standard.component';
// import { CustomStepQuestionComponent } from '../custom-step-question/custom-step-question.component';
import { InputTextComponent } from '../input-text/input-text.component';
import { FormControl, FormArray } from '@angular/forms';
import { InputTextAreaComponent } from '../input-text-area/input-text-area.component';
import { CustomStepTypeRadioComponent } from '../custom-step-type-radio/custom-step-type-radio.component';
// import { CustomStepOptionComponent } from '../custom-step-option/custom-step-option.component';
import { RangeSliderOptionComponent } from '../range-slider-option/range-slider-option.component';
import { FreeFormTextOptionComponent } from '../free-form-text-option/free-form-text-option.component';
import { MultipleChoiceOptionComponent } from '../multiple-choice-option/multiple-choice-option.component';
import { ToastService } from '../../services/toast.service';
import { InputRadioStandardComponent } from '../input-radio-standard/input-radio-standard.component';

class SliderOption {
  leftLabel?: string = '';
  rightLabel?: string = '';
  centerLabel?: string = '';
}

class SelectOption {
  label?: string = '';
}

class Question {
  required: boolean = false;
  question: string = '';
  instructions?: string = '';
  options?: SliderOption[] | SelectOption[] = [];
}

class Step {
  name: string = '';
  instructions?: string = '';
  questions: Question[] = [];
  stepType: string = 'range-slider';
}

@Component({
  selector: 'custom-step-form',
  standalone: true,
  imports: [
    ButtonTwoClickComponent,
    ButtonStandardComponent,
    NgIf,
    // CustomStepQuestionComponent,
    // CustomStepOptionComponent,
    InputTextComponent,
    InputTextAreaComponent,
    CustomStepTypeRadioComponent,
    NgFor,
    RangeSliderOptionComponent,
    FreeFormTextOptionComponent,
    MultipleChoiceOptionComponent,
    InputRadioStandardComponent,
  ],
  templateUrl: './custom-step-form.component.html',
  styleUrl: './custom-step-form.component.scss'
})
export class CustomStepFormComponent implements OnInit, OnDestroy {

  questionsService = inject(QuestionsService);
  popupService = inject(PopupService);
  toastService = inject(ToastService);
  loadingService = inject(LoadingService);

  nameControl = this.questionsService.stepForm.controls['name'] as FormControl;
  stepInstructionsControl = this.questionsService.stepForm.controls['stepInstructions'] as FormControl; 
  stepTypeControl = this.questionsService.stepForm.controls['stepType'] as FormControl;
  optionsControl = this.questionsService.stepForm.controls['options'] as FormArray;
  requiredControl = this.questionsService.stepForm.controls['required'] as FormControl;
  
  isSaving = false;

  ngOnInit(){
    if (this.questionsService.editing && this.questionsService.stepKey) {
      this.loadQuestionData();
    } else {
      this.questionsService.resetForm();
    }
    window.addEventListener('beforeunload', this.beforeUnloadHandler);
  }

  ngOnDestroy() {
    window.removeEventListener('beforeunload', this.beforeUnloadHandler);
  }

  beforeUnloadHandler = (event: BeforeUnloadEvent) => {
    if (this.questionsService.editing && this.popupService.popups['customStep']) {
      event.preventDefault();
      event.returnValue = '';
    }
  };

  async loadQuestionData() {
    try {
      this.loadingService.setLoading('constants', true);
      
      if (this.questionsService.stepKey) {
        await this.questionsService.loadQuestionForEditing(this.questionsService.stepKey);
      }
    } catch (error) {
      console.error('Error loading question data:', error);
    } finally {
      this.loadingService.setLoading('constants', false);
    }
  }

  close(){
    this.questionsService.resetForm();
    this.popupService.closeCustomStepForm();
    this.questionsService.editing = false;
    this.questionsService.addingExistingStep = false;
  }

  async saveQuestion() {    
    try {
      this.isSaving = true;
      this.loadingService.setLoading('constants', true);
      await this.questionsService.checkQuestionBeforeSave();
      this.close();
      this.questionsService.resetForm();
    } catch (error) {
      console.error('Error saving question:', error);
      this.toastService.burntToast('There was an error saving the step');
    } finally {
      this.isSaving = false;
      this.loadingService.setLoading('constants', false);
    }
  }

  addOption(){
    this.optionsControl.push(this.questionsService.optionForm);
  }

  removeOption(index: number){
    this.optionsControl.removeAt(index);
  }

  canAddOption(){
    if(this.questionsService.editing){
      return false;
    }
    switch(this.stepTypeControl.value){
      case 'range-slider':
      case 'multiple-choice':
      case 'select-one':
        return true;
      case 'free-form-text':
      case 'sensory-spectrum':
      case 'true-false':
        return false;
      default: return false;
    }
  }

  get addOptionText():Record<string, string>{
    return {
      'range-slider': 'Add slider',
      'multiple-choice': 'Add option',
      'select-one': 'Add option',
    }
  }

  stepTypeNameFromKey(key: string){
    switch(key){
      case 'free-form-text':
        return 'Free Form Text';
      case 'sensory-spectrum':
        return 'Sensory Spectrum';
      default: return key;
    }
  }

  addQuestion(){
    console.log('addQuestion');
    try {
      this.questionsService.addStep(this.questionsService.stepKey);
      this.close();
      this.toastService.goodToast('Step added successfully');
    } catch (error) {
      console.error('Error adding question:', error);
      this.toastService.burntToast('There was an error adding the question');
    }
  }
}
