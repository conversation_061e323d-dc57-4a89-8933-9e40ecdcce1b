import { Component, inject, Input } from '@angular/core';
import { NgFor } from '@angular/common';
import { PanelsTableRowComponent } from '../panels-table-row/panels-table-row.component';
import { PopupService } from '../../services/popup.service';
import { RouterModule } from '@angular/router';
@Component({
  selector: 'panels-table',
  standalone: true,
  imports: [PanelsTableRowComponent, NgFor, RouterModule],
  templateUrl: './panels-table.component.html',
  styleUrl: './panels-table.component.scss'
})
export class PanelsTableComponent {

  @Input() panels: any;

  popupService = inject(PopupService);
}
