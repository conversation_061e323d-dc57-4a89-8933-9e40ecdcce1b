import { Component, inject } from '@angular/core';
import { CommonModule } from '@angular/common';
import { ProductsService } from '../../services/products.service';
import { PopupService } from '../../services/popup.service';
import { ButtonStandardComponent } from '../button-standard/button-standard.component';
import { PopoverComponent } from '../popover/popover.component';
import { fadeScaleInOut } from '../../modules/animations/animations.module';
import { ClipboardModule, ClipboardService } from 'ngx-clipboard';
import { ToastService } from '../../services/toast.service';
import { ResponseDataOverallBlockComponent } from "../response-data-overall-block/response-data-overall-block.component";
import { ConstantsService } from '../../services/constants.service';
import { ResponseDataAromaSpectrumComponent } from "../response-data-aroma-spectrum/response-data-aroma-spectrum.component";
import { ResponseDataAromaCategoryDistributionComponent } from "../response-data-aroma-category-distribution/response-data-aroma-category-distribution.component";
import { ResponseDataTopDescriptorsComponent } from "../response-data-top-descriptors/response-data-top-descriptors.component";
import { ResponseDataDemographicBreakdownComponent } from "../response-data-demographic-breakdown/response-data-demographic-breakdown.component";
import { ResponseDataCommentsComponent } from "../response-data-comments/response-data-comments.component";
import { ActivatedRoute, RouterModule, Router } from '@angular/router';
import { LoadingService } from '../../services/loading.service';
import { StatisticsService } from '../../services/statistics.service';
import { ResponseDataDescriptorsComponent } from "../response-data-descriptors/response-data-descriptors.component";
import { StepTypeIconComponent } from '../step-type-icon/step-type-icon.component';
import { ResponseDataMultipleChoiceComponent } from '../response-data-multiple-choice/response-data-multiple-choice.component';
import { ResponseDataTrueFalseComponent } from '../response-data-true-false/response-data-true-false.component';

@Component({
  selector: 'product-details',
  standalone: true,
  imports: [
    ButtonStandardComponent,
    PopoverComponent,
    CommonModule,
    ClipboardModule,
    ResponseDataOverallBlockComponent,
    ResponseDataAromaSpectrumComponent,
    ResponseDataTopDescriptorsComponent,
    ResponseDataAromaCategoryDistributionComponent,
    ResponseDataDemographicBreakdownComponent,
    ResponseDataCommentsComponent,
    RouterModule,
    ResponseDataDescriptorsComponent,
    StepTypeIconComponent,
    ResponseDataMultipleChoiceComponent,
    ResponseDataTrueFalseComponent,
  ],
  templateUrl: './product-details.component.html',
  styleUrl: './product-details.component.scss',
  animations: [fadeScaleInOut]
})
export class ProductDetailsComponent {

  productsService = inject(ProductsService);
  popupService = inject(PopupService);
  _clipboardService = inject(ClipboardService);
  toastService = inject(ToastService);
  constantsService = inject(ConstantsService);
  route = inject(ActivatedRoute);
  loadingService = inject(LoadingService);
  statisticsService = inject(StatisticsService);
  router = inject(Router);

  product: any; // = this.productsService.editingProduct;
  responseDetails: any; // = this.productsService.responseDetails;
  showActionsPopover = false;
  showPDSPopover = false;
  backButtonData: any;
  panelSteps: any;
  questionDetails: any;

  mockSteps = [
    {
      instructions: 'Select all options that apply',
      isDefault: false,
      name: 'Select all off scents detected',
      options: [
        { id: 0, value: 'asdf' },
        { id: 1, value: 'ghjk' },
        { id: 2, value: 'qwerty' }
      ],
      organization_id: 'GfH6mQwSTDrJsI710S2Z',
      question_id: 'test-choose-multiple',
      type: 'multiple-choice'
    },
    {
      instructions: 'Select the option that applies the best',
      isDefault: false,
      name: 'Select the off scent most strongly detected',
      options: [
        { id: 0, value: 'asdf' },
        { id: 1, value: 'ghjk' },
        { id: 2, value: 'qwerty' }
      ],
      organization_id: 'GfH6mQwSTDrJsI710S2Z',
      question_id: 'test-choose-one',
      type: 'select-one'
    },
    {
      instructions: 'Select true or false',
      isDefault: false,
      name: 'The product had an overall off scent',
      organization_id: 'GfH6mQwSTDrJsI710S2Z',
      question_id: 'test-true-false',
      type: 'true-false'
    }
  ]


  constructor(){
    this.loadingService.setLoading('products', true);
  }

  ngOnInit(){
    this.route.data.subscribe(data => {
      this.product = data['product'];
      this.responseDetails = data['insights'];
      this.backButtonData = data['backButton'];
      this.questionDetails = this.filterForResponses(data['questionDetails']);
      this.loadingService.setLoading('products', false)
      // console.log("RESPONSE DETAILS", this.responseDetails);
      // console.log("CALCULATED STATS", this.statisticsService.calculateStats(this.responseDetails));
      this.statisticsService.calculatedStats = this.statisticsService.calculateStats(this.responseDetails);
      this.buildPanelSteps();
    });
  }

  filterForResponses(questionDetails: any){
    // console.log('questionDetails', questionDetails);
    return questionDetails.questions.filter((question: any) => {
      let options: string[] = [];
      if(question.isDefault){
        switch(question.id){
          case "a9k1bpXX4H57Bdl7XhaX":
            options = Object.keys(this.constantsService.flavorsObject);
            break;
          case "wjA1uhqbIMFuZwAaCoJ8":
            options = Object.keys(this.constantsService.metricsObject);
            break;
          case "awja0ameTALoE0D4GlWD":
            return true;
        }
      }else{
        switch(question.stepType){
          case 'multiple-choice':
          case 'select-one':
          case 'range-slider':
            options = question.optionIds;
            break;
          case 'true-false':
            options = [question.id + '_true', question.id + '_false'];
            break;
          case 'free-form-text':
            return true;
          default:
            return true;
        }
      }
      
      // console.log('question', question.stepType || question.type, question);
      // console.log('options', options);

      // options.forEach((option: string) => {
      //   let tester1 = option + '_count';
      //   let tester2 = question.id + '_' + option + '_count';
      //   console.log('tester1', tester1);
      //   console.log('tester2', tester2);
      //   console.log('this.responseDetails.hasOwnProperty(tester1)', this.responseDetails.hasOwnProperty(tester1));
      //   console.log('this.responseDetails.hasOwnProperty(tester2)', this.responseDetails.hasOwnProperty(tester2));
      // })
      
      

      // options.forEach((option: any) => {
      //   console.log('option', option);
      //   console.log('this.responseDetails.hasOwnProperty(option + "_count")', this.responseDetails.hasOwnProperty(option + "_count"));
      //   console.log('this.responseDetails.hasOwnProperty(question.id + "_" + option + "_count")', this.responseDetails.hasOwnProperty(question.id + "_" + option + "_count"));
      // })
        
      return options.some((option: any) => {
        // console.log('option', option);
        // return (this.responseDetails.hasOwnProperty(option + '_count') || this.responseDetails.hasOwnProperty(question.id + '_' + option + '_count'));
        return true;
      });
      return true;
    });
  }

  buildPanelSteps(){
    const options = {};
    this.panelSteps = this.questionDetails.map((question: any) => {
      if(question.isDefault){
        return this.constantsService.questionsObject[question.question_id];
      }
      return {
        ...question,
        type: question.type || question.stepType,
      };
    });
  }

  //what do I want? 
  // I don't want to show steps that don't have any responses
  // build an options list for each step?
  // check if the response details has a value for at least one of the option ids
  // if it does, show the step
  // if it doesn't, don't show the step
  // I think that's the best way to do it

  getStepTypeLabel(stepType: string){
    switch(stepType){
      case 'multiple-choice':
        return 'Multiple Choice';
      case 'select-one':
        return 'Select One';
      case 'true-false':
        return 'True or False';
      default:
        return stepType;
    }
  }

  get backButtonText(): string {
    return this.backButtonData?.backButtonText || 'Back to Products';
  }

  goBack(): void {
    if (this.backButtonData?.isFromGroup) {
      this.router.navigate([this.backButtonData.backButtonRoute]);
    } else {
      this.router.navigate([this.backButtonData?.backButtonRoute || '/products']);
    }
  }

  close(){
    this.popupService.closeAllPopups();
  }

  toggleActionsPopover(){
    this.showActionsPopover = !this.showActionsPopover;
  }

  downloadPDS(sample_key: string, sample_name: string, pds_type: string = "lite") {
      this.toastService.goodToast(`Fetching Product Data Sheet for ${sample_name}`);
      let unsubscribe = this.productsService.generatePDS(sample_key, pds_type).subscribe((blob: Blob) => {
          const objectUrl = URL.createObjectURL(blob);
          const pdfName = `${sample_name}_${new Date().toISOString().split('T')[0]}_${pds_type}.pdf`;

          // Open in new tab
          const newTab = window.open(objectUrl, '_blank');

          // Create a temporary link to download the file with a controlled name
          const tempLink = document.createElement('a');
          tempLink.href = objectUrl;
          tempLink.download = pdfName;
          tempLink.style.display = 'none';
          document.body.appendChild(tempLink);
          tempLink.click();
          document.body.removeChild(tempLink);

          URL.revokeObjectURL(objectUrl);
      }, error => {
          console.error('Download error:', error);
      });
  }

  redirectToPDS(sample_key: string, sample_name: string, pds_type: string = "lite") {
    // Get the base URL from the service
    const pdsUrl = this.productsService.getPdsUrl(sample_key, pds_type);

    // Open the URL in a new tab
    window.open(pdsUrl, '_blank');
    this.toastService.goodToast(`Opening Product Data Sheet for ${sample_name}`);
  }

  togglePDSPopover() {
    this.showPDSPopover = !this.showPDSPopover;
  }

  fetchPDS(type: 'Basic' | 'Advanced') {
    this.showPDSPopover = false;

    if (!this.product.external_id || this.product.external_id.trim() === '') {
      this.toastService.burntToast('Please fill out the product code to generate PDS');
      return;
    }

    let _type = type === 'Basic' ? 'lite' : 'advanced';
    console.log('Fetching PDS:', _type);
    this.redirectToPDS(this.product.external_id, this.product.name, _type);
    // TODO: Implement PDS fetching logic
  }

  showAssociatedPanels(){
    console.log('showAssociatedPanels');
  }

  deleteProduct(product: any){
    console.log('deleteProduct', product);
  }

  printProductLabel(){
    console.log('printProductLabel');
  }

  replaceSpacesWithUnderscores(str: string){
    return str.replace(/\s+/g, '_');
  }
}
