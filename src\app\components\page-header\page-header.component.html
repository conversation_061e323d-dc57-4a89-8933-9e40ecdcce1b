<ng-content></ng-content>

<div class="page-name">{{pageName}}</div>


<!-- <div class="page-info varient-1" *ngIf="infoVarient === 1">{{info}}</div> -->

<div class="page-info varient-2" *ngIf="infoVarient === 2">
    <div class="number">{{info}}</div>
    <div class="text">Panelists awaiting approval.</div>
    <div class="link-container"><a routerLink="/">Review</a></div>
</div>
<div class="page-info varient-3" *ngIf="infoVarient === 3 && pageName === 'Sensory Panels'">
    <div class="number">{{info}}</div>
    <p > &#8594; </p>
    <div [class.active-url]="url === '/panels'" class="url"><span routerLink="/panels">Created Panels</span></div>
<div [class.active-url]="url === '/templates'" class="url"><span routerLink="/templates">Panel Templates</span></div>

</div>

<!-- <div class="fs filter-container">Filters<svg width="8" height="5" viewBox="0 0 8 5" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path fill-rule="evenodd" clip-rule="evenodd"
        d="M4 5L8 1.00478L6.99484 0L4 2.99841L1.00516 0L2.0903e-07 1.00478L4 5Z" fill="black" />
</svg>
</div> -->

<!-- <div class="fs search-container"><input type="text" placeholder="Search"></div> -->

<div class="add-button-container">
    <button-standard class="{{buttonClass}}" (onAction)="onAdd.emit($event)">
        <svg xmlns="http://www.w3.org/2000/svg" width="10" height="10" viewBox="0 0 10 10" fill="none" style="margin-right: 10px;">
            <path d="M4.11765 0V4.11765H0V5.88235H4.11765V10H5.88235V5.88235H10V4.11765H5.88235V0H4.11765Z" fill="white"/>
        </svg>
        <span>{{addText}}</span>
    </button-standard>
</div>