<div class="logo-container">
    <svg xmlns="http://www.w3.org/2000/svg" width="23" height="26" viewBox="0 0 23 26" fill="none">
        <path d="M10.1782 26C8.16897 26 6.3977 25.7306 4.86437 25.1917C3.33103 24.6349 2.13257 23.8535 1.26897 22.8477C0.422989 21.8418 0 20.6743 0 19.3451C0 18.1596 0.387739 17.1447 1.16322 16.3005C1.9387 15.4563 2.96973 15.0342 4.25632 15.0342C5.43717 15.0342 6.40651 15.4024 7.16437 16.1389C7.93985 16.8753 8.32759 17.8722 8.32759 19.1295C8.32759 20.4048 7.91341 21.3748 7.08506 22.0394C6.27433 22.704 5.31379 23.0363 4.20345 23.0363C3.44559 23.0363 2.74943 22.8746 2.11494 22.5513C1.48046 22.21 0.978161 21.761 0.608046 21.2041C0.237931 20.6473 0.0528735 20.0276 0.0528735 19.3451H1.18966C1.18966 20.5306 1.56858 21.5544 2.32644 22.4166C3.08429 23.2788 4.08889 23.9434 5.34023 24.4104C6.59157 24.8594 7.95747 25.0839 9.43793 25.0839C10.372 25.0839 11.2356 24.8864 12.0287 24.4912C12.8218 24.096 13.4651 23.5661 13.9586 22.9016C14.4521 22.237 14.6989 21.4826 14.6989 20.6383C14.6989 19.6864 14.4257 18.8511 13.8793 18.1326C13.3506 17.4142 12.6632 16.7585 11.8172 16.1658C10.9889 15.5551 10.0989 14.9623 9.14713 14.3876C8.1954 13.8128 7.29655 13.2021 6.45058 12.5554C5.6046 11.9088 4.90843 11.1903 4.36207 10.4C3.83333 9.59171 3.56897 8.6487 3.56897 7.57098C3.56897 6.18791 3.99195 4.92159 4.83793 3.77202C5.70153 2.62245 6.91763 1.70639 8.48621 1.02383C10.0724 0.341278 11.9406 0 14.0908 0C16.2234 0 17.9418 0.296373 19.246 0.889119C20.5678 1.4639 21.5195 2.19136 22.1012 3.0715C22.7004 3.95164 23 4.8228 23 5.68497C23 6.92435 22.6034 7.88532 21.8103 8.56788C21.0349 9.25043 20.1536 9.59171 19.1667 9.59171C18.0739 9.59171 17.087 9.23247 16.2057 8.51399C15.3245 7.77755 14.8839 6.78964 14.8839 5.55026C14.8839 4.52642 15.2452 3.66425 15.9678 2.96373C16.6904 2.26321 17.6421 1.91295 18.823 1.91295C19.9157 1.91295 20.8762 2.26321 21.7046 2.96373C22.533 3.64629 22.9471 4.55337 22.9471 5.68497H21.9425C21.9425 4.89465 21.6605 4.14024 21.0966 3.42176C20.5326 2.68532 19.7307 2.09257 18.6908 1.64352C17.6686 1.17651 16.4261 0.943006 14.9632 0.943006C14.0115 0.943006 13.1655 1.12263 12.4253 1.48187C11.7027 1.84111 11.1299 2.3171 10.7069 2.90985C10.3015 3.50259 10.0989 4.1582 10.0989 4.87668C10.0989 5.81071 10.372 6.62798 10.9184 7.3285C11.4824 8.02902 12.205 8.67565 13.0862 9.26839C13.9674 9.86114 14.9015 10.4449 15.8885 11.0197C16.8755 11.5945 17.8008 12.2231 18.6644 12.9057C19.5456 13.5883 20.2682 14.3696 20.8322 15.2497C21.3962 16.1119 21.6782 17.1447 21.6782 18.3482C21.6782 19.8929 21.1847 21.2401 20.1977 22.3896C19.2284 23.5212 17.8801 24.4104 16.1529 25.057C14.4257 25.6857 12.4341 26 10.1782 26Z" fill="var(--logo-icon-fill)"/>
    </svg>
</div>

<div class="org-selector-container">
    <div class="org-dropdown"
         (click)="toggleOrgDropdown()"
         (keydown.enter)="toggleOrgDropdown()"
         tabindex="0"
         role="button"
         aria-haspopup="true"
         [attr.aria-expanded]="isOrgDropdownOpen">

        <div class="org-display">
            <div class="org-icon">
                <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                    <path d="M3 21h18"/>
                    <path d="M5 21V7l8-4v18"/>
                    <path d="M19 21V11l-6-4"/>
                    <path d="M9 9v.01"/>
                    <path d="M9 12v.01"/>
                    <path d="M9 15v.01"/>
                    <path d="M9 18v.01"/>
                </svg>
            </div>
            <div class="org-info">
                <span class="org-name">{{ currentOrganization?.name || 'Select Organization' }}</span>
                <span class="org-type" *ngIf="currentOrganization?.type">{{ currentOrganization?.type }}</span>
            </div>
            <div class="dropdown-arrow">
                <svg xmlns="http://www.w3.org/2000/svg" width="12" height="12" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" [class.rotated]="isOrgDropdownOpen">
                    <polyline points="6,9 12,15 18,9"></polyline>
                </svg>
            </div>
        </div>

        <div class="org-dropdown-menu" [@fadeScaleInOut] *ngIf="isOrgDropdownOpen">
            <div class="dropdown-header">Organizations</div>
            <div class="org-option"
                 *ngFor="let org of organizations"
                 (click)="selectOrganization(org)"
                 (keydown.enter)="selectOrganization(org)"
                 tabindex="0"
                 role="option"
                 [class.selected]="currentOrganization?.id === org.id">
                <div class="org-option-icon">
                    <svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                        <path d="M3 21h18"/>
                        <path d="M5 21V7l8-4v18"/>
                        <path d="M19 21V11l-6-4"/>
                        <path d="M9 9v.01"/>
                        <path d="M9 12v.01"/>
                        <path d="M9 15v.01"/>
                        <path d="M9 18v.01"/>
                    </svg>
                </div>
                <div class="org-option-info">
                    <span class="org-option-name">{{ org.name }}</span>
                    <span class="org-option-type" *ngIf="org.type">{{ org.type }}</span>
                </div>
                <div class="selected-indicator" *ngIf="currentOrganization?.id === org.id">
                    <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                        <polyline points="20,6 9,17 4,12"></polyline>
                    </svg>
                </div>
            </div>

            <div class="no-orgs-message" *ngIf="organizations.length === 0">
                No organizations available ({{ organizations.length }} found)
            </div>

            <!-- Debug info -->
            <div class="debug-info" style="padding: 10px; font-size: 12px; color: #666; border-top: 1px solid #eee;">
                Debug: {{ organizations.length }} organizations loaded
                <button (click)="loadOrganizations()" style="margin-left: 10px; padding: 2px 6px; font-size: 10px;">
                    Reload Orgs
                </button>
            </div>
        </div>
    </div>
</div>

<div class="nav-links-container">
    <a routerLink="/projects" routerLinkActive="active"><div class="nav-link">Projects</div></a>
    <!-- <a routerLink="/home" routerLinkActive="active"><div class="nav-link">Overview</div></a> -->
    <a routerLink="/panels" routerLinkActive="active"><div class="nav-link">Panels</div></a>
    <!-- <a routerLink="/panelists" routerLinkActive="active"><div class="nav-link">Panelists</div></a> -->
    <a routerLink="/products" routerLinkActive="active"><div class="nav-link">Products</div></a>
    <a routerLink="/groups" routerLinkActive="active"><div class="nav-link">Groups</div></a>
</div>

<div class="user-container">
    <div class="user-icon"
         (click)="togglePopover()"
         (keydown.enter)="togglePopover()"
         tabindex="0"
         role="button"
         aria-haspopup="true"
         [attr.aria-expanded]="isPopoverOpen">
        {{ profileService.getInitials() }}
    </div>

    <!-- <div class="popover-container" [@fadeScale] *ngIf="isPopoverOpen">
        <a routerLink="/settings" (click)="togglePopover()" tabindex="0"><div class="popover-item">Settings</div></a>
        <a role="button" tabindex="0" (click)="logout()" (keydown.enter)="logout()"><div class="popover-item">Logout</div></a>
    </div> -->

    <popover [@fadeScale] *ngIf="isPopoverOpen">
        <a routerLink="/settings" (click)="togglePopover()" tabindex="0"><div class="popover-item">Settings</div></a>
        <a role="button" tabindex="0" (click)="logout()" (keydown.enter)="logout()"><div class="popover-item">Logout</div></a>
    </popover>
</div>