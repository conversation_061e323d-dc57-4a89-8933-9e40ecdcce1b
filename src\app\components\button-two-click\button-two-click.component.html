<button (click)="buttonClick()" (mouseleave)="onMouseLeave()" (mouseenter)="onMouseEnter()">
    <ng-content></ng-content>
    <div class="text-container">
        <span class="hidden-text">{{ buttonPhase === 0 ? initialText : confirmText }}</span>
        <span *ngIf="buttonPhase === 0" [@buttonTextSlide]>{{ initialText }}</span>
        <span *ngIf="buttonPhase === 1" [@buttonTextSlide]>{{ confirmText }}</span>
    </div>
</button>
