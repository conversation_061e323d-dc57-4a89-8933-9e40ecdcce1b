import { Component, HostListener, inject, <PERSON>I<PERSON><PERSON>, <PERSON><PERSON><PERSON>roy, ChangeDetectorRef } from '@angular/core';
import { NgI<PERSON>, <PERSON><PERSON><PERSON>, JsonPipe } from '@angular/common';
import { RouterModule, RouterLink } from '@angular/router';
import { fadeScaleInOut } from '../../modules/animations/animations.module';
import { AuthService } from '../../services/auth.service';
import { ProfileService } from '../../services/profile.service';
import { OrganizationsService, Organization } from '../../services/organizations.service';
import { PopoverComponent } from '../popover/popover.component';
import { Subscription } from 'rxjs';
@Component({
  selector: 'nav',
  standalone: true,
  imports: [RouterModule, RouterLink, NgIf, NgFor, JsonPipe, PopoverComponent],
  templateUrl: './nav.component.html',
  styleUrl: './nav.component.scss',
  animations: [fadeScaleInOut]
})
export class NavComponent implements OnInit, OnD<PERSON>roy {
  isPopoverOpen = false;
  isOrgDropdownOpen = false;
  authService = inject(AuthService);
  profileService = inject(ProfileService);
  organizationsService = inject(OrganizationsService);
  private cdr = inject(ChangeDetectorRef);

  organizations: Organization[] = [];
  currentOrganization: Organization | null = null;
  private subscriptions = new Subscription();

  ngOnInit() {
    this.loadOrganizations();

    // Subscribe to current organization changes
    this.subscriptions.add(
      this.organizationsService.currentOrganization$.subscribe(org => {
        this.currentOrganization = org;
        console.log(org);
      })
    );

    // Subscribe to organizations list changes
    this.subscriptions.add(
      this.organizationsService.organizations$.subscribe(orgs => {
        console.log('Organizations updated in nav component:', orgs);
        console.log('Previous organizations:', this.organizations);
        this.organizations = [...orgs]; // Create new array reference
        console.log('New organizations set:', this.organizations);
        this.cdr.detectChanges(); // Force change detection
      })
    );
  }

  ngOnDestroy() {
    this.subscriptions.unsubscribe();
  }

  async loadOrganizations() {
    try {
      // Get organization IDs from user profile data
      const userOrganizations = this.profileService.profileData?.organizations || [];

      console.log('Profile data:', this.profileService.profileData);
      console.log('userOrganizations', userOrganizations);

      let loadedOrgs: Organization[] = [];

      if (userOrganizations.length > 0) {
        loadedOrgs = await this.organizationsService.loadUserOrganizations(userOrganizations);
      } else {
        console.warn('No organizations found in user profile, creating test data');
        // For testing purposes, create some mock organizations
        const mockOrgIds = ['org1', 'org2', 'org3'];
        loadedOrgs = await this.organizationsService.loadUserOrganizations(mockOrgIds);
      }

      console.log('Loaded organizations from service:', loadedOrgs);

      // Manually set the organizations array to ensure UI updates
      this.organizations = [...loadedOrgs];

      // Force change detection after loading
      this.cdr.detectChanges();
      console.log('Organizations after manual assignment:', this.organizations);

      // Also try with a small delay to ensure async operations complete
      setTimeout(() => {
        this.organizations = [...loadedOrgs];
        this.cdr.detectChanges();
        console.log('Organizations after timeout:', this.organizations);
      }, 100);
    } catch (error) {
      console.error('Error loading organizations:', error);
    }
  }

  // Add a method to manually set test data
  setTestOrganizations() {
    const testOrgs: Organization[] = [
      { id: 'test1', name: 'Test Organization 1', type: 'company' },
      { id: 'test2', name: 'Test Organization 2', type: 'research' },
      { id: 'test3', name: 'Test Organization 3', type: 'university' }
    ];

    console.log('Setting test organizations:', testOrgs);
    this.organizations = testOrgs;
    this.organizationsService['organizationsSubject'].next(testOrgs);
    this.cdr.detectChanges();
  }

  // TrackBy function for ngFor
  trackByOrgId(_index: number, org: Organization): string {
    return org.id;
  }

  // Method to sync organizations from service
  syncOrganizationsFromService() {
    const serviceOrgs = this.organizationsService['organizationsSubject'].value;
    console.log('Service organizations:', serviceOrgs);
    this.organizations = [...serviceOrgs];
    this.cdr.detectChanges();
    console.log('Synced organizations:', this.organizations);
  }

  @HostListener('document:keydown.escape')
  handleEscape() {
    if (this.isPopoverOpen) {
      this.isPopoverOpen = false;
    }
    if (this.isOrgDropdownOpen) {
      this.isOrgDropdownOpen = false;
    }
  }

  @HostListener('document:click', ['$event'])
  handleDocumentClick(event: Event) {
    const target = event.target as HTMLElement;

    // Close org dropdown if clicking outside
    if (this.isOrgDropdownOpen && !target.closest('.org-selector-container')) {
      this.isOrgDropdownOpen = false;
    }

    // Close user popover if clicking outside
    if (this.isPopoverOpen && !target.closest('.user-container')) {
      this.isPopoverOpen = false;
    }
  }

  togglePopover() {
    this.isPopoverOpen = !this.isPopoverOpen;
    // Close org dropdown when opening user popover
    if (this.isPopoverOpen) {
      this.isOrgDropdownOpen = false;
    }
  }

  toggleOrgDropdown() {
    this.isOrgDropdownOpen = !this.isOrgDropdownOpen;
    // Close user popover when opening org dropdown
    if (this.isOrgDropdownOpen) {
      this.isPopoverOpen = false;
    }
  }

  selectOrganization(organization: Organization) {
    this.organizationsService.setCurrentOrganization(organization);
    this.isOrgDropdownOpen = false;

    // Optionally reload the page or emit an event to refresh data
    // window.location.reload();
  }

  logout() {
    console.log('logout');
    this.organizationsService.clearCurrentOrganization();
    this.authService.logout();
  }
}
