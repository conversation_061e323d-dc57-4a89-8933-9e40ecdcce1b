import { Component, HostListener, inject } from '@angular/core';
import { NgIf } from '@angular/common';
import { RouterModule, RouterLink } from '@angular/router';
import { fadeScaleInOut } from '../../modules/animations/animations.module';
import { AuthService } from '../../services/auth.service';
import { ProfileService } from '../../services/profile.service';
import { PopoverComponent } from '../popover/popover.component';
@Component({
  selector: 'nav',
  standalone: true,
  imports: [RouterModule, RouterLink, NgIf, PopoverComponent],
  templateUrl: './nav.component.html',
  styleUrl: './nav.component.scss',
  animations: [fadeScaleInOut]
})
export class NavComponent {
  isPopoverOpen = false;
  authService = inject(AuthService);
  profileService = inject(ProfileService);
  @HostListener('document:keydown.escape')
  handleEscape() {
    if (this.isPopoverOpen) {
      this.isPopoverOpen = false;
    }
  }

  togglePopover() {
    this.isPopoverOpen = !this.isPopoverOpen;
  }

  logout() {
    console.log('logout');
    this.authService.logout();
  }
}
