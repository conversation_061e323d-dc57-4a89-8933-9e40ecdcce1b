import { Component, HostListener, inject, OnInit, OnDestroy } from '@angular/core';
import { NgIf, NgFor } from '@angular/common';
import { RouterModule, RouterLink } from '@angular/router';
import { fadeScaleInOut } from '../../modules/animations/animations.module';
import { AuthService } from '../../services/auth.service';
import { ProfileService } from '../../services/profile.service';
import { OrganizationsService, Organization } from '../../services/organizations.service';
import { PopoverComponent } from '../popover/popover.component';
import { Subscription } from 'rxjs';
import { LambGPIconComponent } from '@lamb-sensory/lamb-component-library';
@Component({
  selector: 'nav',
  standalone: true,
  imports: [LambGPIconComponent, RouterModule, RouterLink, NgIf, NgFor, PopoverComponent],
  templateUrl: './nav.component.html',
  styleUrl: './nav.component.scss',
  animations: [fadeScaleInOut]
})
export class NavComponent implements OnInit, OnDestroy {
  isPopoverOpen = false;
  isOrgDropdownOpen = false;
  authService = inject(AuthService);
  profileService = inject(ProfileService);
  organizationsService = inject(OrganizationsService);

  organizations: Organization[] = [];
  currentOrganization: Organization | null = null;
  private subscriptions = new Subscription();

  ngOnInit() {
    this.loadOrganizations();

    // Subscribe to current organization changes
    this.subscriptions.add(
      this.organizationsService.currentOrganization$.subscribe(org => {
        this.currentOrganization = org;
      })
    );

    // Subscribe to organizations list changes
    this.subscriptions.add(
      this.organizationsService.organizations$.subscribe(orgs => {
        this.organizations = orgs;
      })
    );
  }

  ngOnDestroy() {
    this.subscriptions.unsubscribe();
  }

  async loadOrganizations() {
    try {
      // Get organization IDs from user profile data
      const userOrganizations = this.profileService.profileData?.organizations || [];

      if (userOrganizations.length > 0) {
        await this.organizationsService.loadUserOrganizations(userOrganizations);
      }
    } catch (error) {
      console.error('Error loading organizations:', error);
    }
  }

  @HostListener('document:keydown.escape')
  handleEscape() {
    if (this.isPopoverOpen) {
      this.isPopoverOpen = false;
    }
    if (this.isOrgDropdownOpen) {
      this.isOrgDropdownOpen = false;
    }
  }

  @HostListener('document:click', ['$event'])
  handleDocumentClick(event: Event) {
    const target = event.target as HTMLElement;

    // Close org dropdown if clicking outside
    if (this.isOrgDropdownOpen && !target.closest('.org-selector-container')) {
      this.isOrgDropdownOpen = false;
    }

    // Close user popover if clicking outside
    if (this.isPopoverOpen && !target.closest('.user-container')) {
      this.isPopoverOpen = false;
    }
  }

  togglePopover() {
    this.isPopoverOpen = !this.isPopoverOpen;
    // Close org dropdown when opening user popover
    if (this.isPopoverOpen) {
      this.isOrgDropdownOpen = false;
    }
  }

  toggleOrgDropdown() {
    this.isOrgDropdownOpen = !this.isOrgDropdownOpen;
    // Close user popover when opening org dropdown
    if (this.isOrgDropdownOpen) {
      this.isPopoverOpen = false;
    }
  }

  selectOrganization(organization: Organization) {
    this.organizationsService.setCurrentOrganization(organization);
    this.isOrgDropdownOpen = false;

    // Optionally reload the page or emit an event to refresh data
    // window.location.reload();
  }

  logout() {
    console.log('logout');
    this.organizationsService.clearCurrentOrganization();
    this.authService.logout();
  }
}
