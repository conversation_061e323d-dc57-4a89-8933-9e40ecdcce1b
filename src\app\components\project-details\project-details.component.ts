import { Component, inject, ElementRef, ViewChild, TemplateRef, HostListener } from '@angular/core';
import { <PERSON>I<PERSON>, <PERSON>F<PERSON> } from '@angular/common';
import { RouterModule, Router } from '@angular/router';
import { ButtonStandardComponent } from '../button-standard/button-standard.component';
import { PopoverComponent } from '../popover/popover.component';
import { fadeScaleInOut, fadeInOut, slideInOut } from '../../modules/animations/animations.module';
import { LoadingService } from '../../services/loading.service';
import { ActivatedRoute } from '@angular/router';
import { ProjectDetailsAdapter } from '../../adapters/project.adapter';
import { LambTableComponent, HeaderCell, LambGPIconComponent, GraphTooltipComponent } from '@lamb-sensory/lamb-component-library';
import { TABLE_STYLES } from '../../config/table-styles.config';
import { PanelAdapter } from '../../adapters/panel.adapter';
import { ApiService } from '../../services/api.service';
import { PaginationComponent } from "../pagination/pagination.component";
import { PaginationState } from '../../interfaces/pagination.interface';
import { PanelsService } from '../../services/panels.service';
import { firstValueFrom, take, Subscription } from 'rxjs';
import { ToastService } from '../../services/toast.service';
import { ProfileService } from '../../services/profile.service';
import { ReactiveFormsModule, FormControl, FormGroup } from '@angular/forms';
import { InputTextComponent } from '../input-text/input-text.component';
import { InputRadioBlockComponent } from '../input-radio-block/input-radio-block.component';
import { InputBsDatepickerComponent } from '../input-bs-datepicker/input-bs-datepicker.component';
import { InputRadioStandardComponent } from '../input-radio-standard/input-radio-standard.component';
import { ProjectsService } from '../../services/projects.service';

@Component({
  selector: 'project-details',
  standalone: true,
  imports: [RouterModule, ButtonStandardComponent, PopoverComponent, LambTableComponent, NgIf, NgFor, LambTableComponent, LambGPIconComponent, GraphTooltipComponent, PaginationComponent, ReactiveFormsModule, InputTextComponent, InputRadioBlockComponent, InputBsDatepickerComponent, InputRadioStandardComponent],
  templateUrl: './project-details.component.html',
  styleUrl: './project-details.component.scss',
  animations: [fadeScaleInOut, fadeInOut, slideInOut]
})
export class ProjectDetailsComponent {

  route = inject(ActivatedRoute);
  loadingService = inject(LoadingService);
  apiService = inject(ApiService);
  router = inject(Router);
  panelsService = inject(PanelsService);
  toastService = inject(ToastService);
  profileService = inject(ProfileService);
  projectsService = inject(ProjectsService);
  showActionsPopover = false;
  project: any;
  tableStyles = structuredClone(TABLE_STYLES);

  panelsTableHeaders: HeaderCell[] = [
    {
      value: 'Status',
      key: 'status',
    },
    {
      value: 'Panel Name',
      key: 'panelName',
    },
    {
      value: 'Sample',
      key: 'productName',
    },
    {
      value: '# Responses',
      key: 'responses',
    },
    {
      value: '',
      key: 'button',
    },    
  ]
  allPanelsTableHeaders: HeaderCell[] = [
    {
      value: 'Select',
      key: 'select',
      style: {textAlign: 'center', width: '8ch'}
    },
    {
      value: 'Status',
      key: 'status'
    },
    {
      value: 'Name',
      key: 'panelName'
    },
    {
      value: 'Sample',
      key: 'productName'
    }
  ];

  panelsTableData: any[] = [];
  allPanelsTableData: any[] = [];
  panelsDrawerOpen: boolean = false;
  panelsDrawerTableData: any[] = [];
  panelsDrawerTableStyle = structuredClone(TABLE_STYLES);
  isLoadingMorePanels = false;
  panelsState = {
    panels: [] as any[],
    totalCount: 0,
    hasMore: true,
    lastLoadedPage: 0,
    pageSize: 15
  };

  collaboratorsModalOpen: boolean = false;
  modalOffset: {x: number, y: number} = {x: 0, y: 0};
  usersHeaders: HeaderCell[] = [
    {
      value: 'Select',
      key: 'select',
      style: {textAlign: 'center', width: '8ch'}
    },
    {
      value: 'Name',
      key: 'name'
    }
  ]
  mockUsers:any[] = [
    {
      name: 'John Doe',
      email: '<EMAIL>',
      role: 'Admin'
    },
    {
      name: 'Jane Smith',
      email: '<EMAIL>',
      role: 'User'
    },
    {
      name: 'Bob Johnson',
      email: '<EMAIL>',
      role: 'User'
    },
    {
      name: 'Alice Brown',
      email: '<EMAIL>',
      role: 'User'
    },
    {
      name: 'Charlie Davis',
      email: '<EMAIL>',
      role: 'User'
    },
    {
      name: 'David Wilson',
      email: '<EMAIL>',
      role: 'User'
    }
  ]
  users: any[] = [];
  loadingUsers: boolean = false;
  userTableStyles = structuredClone(TABLE_STYLES);
  selectedUsersArray: any[] = [];
  usersSubscription: Subscription = new Subscription();
  activeTooltipUser: any = null;
  activeTooltipText: string = '';
  responsesDrawerOpen: boolean = false;
  editDrawerOpen: boolean = false;
  responsesTableHeaders: HeaderCell[] = [
    {
      value: 'Panel',
      key: 'panel',
    },
    {
      value: 'Panelist',
      key: 'panelist',
    },
    {
      value: 'Received',
      key: 'date',
    }
  ]
  responsesTableData: any[] = []; 
  responsesTableStyle = structuredClone(TABLE_STYLES);
  // responsesPaginationState: PaginationState = {
  //   currentPage: 1,
  //   totalPages: 10,
  //   totalItems: 100,
  //   itemsPerPage: 10,
  //   itemsPerPageOptions: [10, 20, 50, 100]
  // }
  mockResponses: any[] = [
    {
      panel: 'Flavor Panel A',
      panelist: 'John Smith',
      date: '2023-10-15'
    },
    {
      panel: 'Aroma Panel B',
      panelist: 'Sarah Johnson',
      date: '2023-10-16'
    },
    {
      panel: 'Texture Panel C',
      panelist: 'Michael Brown',
      date: '2023-10-17'
    },
    {
      panel: 'Flavor Panel A',
      panelist: 'Emily Davis',
      date: '2023-10-18'
    },
    {
      panel: 'Aroma Panel B',
      panelist: 'Robert Wilson',
      date: '2023-10-19'
    },
    {
      panel: 'Texture Panel C',
      panelist: 'Jennifer Lee',
      date: '2023-10-20'
    },
    {
      panel: 'Flavor Panel A',
      panelist: 'David Miller',
      date: '2023-10-21'
    },
    {
      panel: 'Aroma Panel B',
      panelist: 'Lisa Anderson',
      date: '2023-10-22'
    },
    {
      panel: 'Texture Panel C',
      panelist: 'James Taylor',
      date: '2023-10-23'
    },
    {
      panel: 'Flavor Panel A',
      panelist: 'Patricia Martinez',
      date: '2023-10-24'
    }
  ];

  tooltipPosition = { x: 0, y: 0 };
  activeTooltipPanel: any = null;

  editProjectForm = this.projectsService.form;
  projectNameControl = this.projectsService.form.get('projectName')! as FormControl;
  descriptionControl = this.projectsService.form.get('description')! as FormControl;
  startOptionControl = this.projectsService.form.get('startOption')! as FormControl;
  startDateControl = this.projectsService.form.get('startDate')! as FormControl;
  endOptionControl = this.projectsService.form.get('endOption')! as FormControl;
  endDateControl = this.projectsService.form.get('endDate')! as FormControl;

  formErrors: any = {
    projectName: false,
    description: false,
    startOption: false,
    startDate: false,
    endOption: false,
    endDate: false,
  };
  errorMessage: string = '';

  @ViewChild('modalButton') modalButton!: ElementRef;
  @ViewChild('selectUserCellTemplate') selectUserCellTemplate!: TemplateRef<any>;
  @ViewChild('collaboratorsModal') collaboratorsModal!: ElementRef;
  @ViewChild('viewResponsesButtonCell') viewResponsesButtonCell!: TemplateRef<any>;
  @ViewChild('panelSampleCellTemplate') panelSampleCellTemplate!: TemplateRef<any>;
  @ViewChild('panelSampleTooltip') panelSampleTooltip!: GraphTooltipComponent;
  @ViewChild('panelistCellTemplate') panelistCellTemplate!: TemplateRef<any>;
  @ViewChild('actionsPopover') actionsPopover!: ElementRef;
  @ViewChild('selectCellTemplate') selectCellTemplate!: TemplateRef<any>;


  @HostListener('document:click', ['$event'])
  handleClickOutside(event: MouseEvent) {
    // Check if collaborators modal is open
    if (this.collaboratorsModalOpen) {
      const modalElement = this.collaboratorsModal?.nativeElement;
      const isClickOutside = modalElement && !modalElement.contains(event.target);
      if (isClickOutside) {
        this.collaboratorsModalOpen = false;
      }
    }

    // Check if actions popover is open
    if (this.showActionsPopover) {
      const popoverElement = this.actionsPopover?.nativeElement;
      const isClickOutside = popoverElement && !popoverElement.contains(event.target);
      if (isClickOutside) {
        this.showActionsPopover = false;
      }
    }
  }
  
  constructor(){
  }

  buttonState: 0 | 1 | 2 = 0;
  buttonText: string[] = ['No Changes', 'Save', 'Saving...'];
  initialFormValues: any;

  ngOnInit(){
    this.loadingService.setLoading('projects', true);
    this.route.data.subscribe(async (data) => {     
      console.log('data', data);
      await this.setProject(data);
      await this.setPanelsTableData();
      this.loadingService.setLoading('projects', false);
      console.log('project', this.project);    
      this.initializeEditForm();
      this.editProjectForm.valueChanges.subscribe((value) => {
        this.resetErrors();
        if(this.compareObjects(this.initialFormValues, value)){
          this.buttonState = 0;
        }else {
          this.buttonState = 1;
        }
      });
    });
  }

  compareObjects(obj1: any, obj2: any){
    // Recursively compare two objects for deep equality
    if (obj1 === obj2) return true;
    if (!obj1 || !obj2) return false;
    if (typeof obj1 !== 'object' || typeof obj2 !== 'object') return obj1 === obj2;

    const keys1 = Object.keys(obj1);
    const keys2 = Object.keys(obj2);

    if (keys1.length !== keys2.length) return false;

    for (const key of keys1) {
      if (!keys2.includes(key)) return false;
      if (!this.compareObjects(obj1[key], obj2[key])) return false;
    }

    return true;
  }

  initializeEditForm(){   
    this.editProjectForm.patchValue({
      projectName: this.project?.projectName || '',
      description: this.project?.description || '',
      startDate: this.getDateForInput(this.project?.startDate) || 'undefined',
      endDate: this.getDateForInput(this.project?.endDate) || 'undefined',
      endOption: this.getEndOption(this.project?.endDate),
    });
    this.initialFormValues = structuredClone(this.editProjectForm.value);
  }

  getDateForInput(date: string){
    if(!date) return 'undefined';
    const dateObject = new Date(date);
    return `${dateObject.getFullYear()}-${(dateObject.getMonth() + 1).toString().padStart(2, '0')}-${dateObject.getDate().toString().padStart(2, '0')}`;
  }

  getEndOption(endDate: string){
    const defaultEndDate = new Date();
    defaultEndDate.setFullYear(defaultEndDate.getFullYear() + 100);
    const date = new Date(endDate);
    if (date.getFullYear() > 4000 || !endDate) {
      return "never";
    }
    return 'custom';
  }

  get isEnded(){
    if (!this.project.endDate) return 's';
    const endDate = new Date(this.project.endDate);
    const today = new Date();
    return endDate < today ? 'ed' : 's';

  }

  openResponseDetails(panel: any){
    console.log('openResponseDetails', panel);
    this.router.navigate([`/panels/${panel.panelId}`], {
      queryParams: { 
        from: 'project',
        projectId: this.project.id
      }
    });
  }

  openResponsesDrawer(){
    this.responsesDrawerOpen = true;
    this.responsesTableData = this.mockResponses;
    this.insertPanelistCells(this.responsesTableData);
  }

  openEditDrawer(){
    this.editDrawerOpen = true;
  }

  closeEditDrawer(){
    this.editDrawerOpen = false;
  }

  insertPanelistCells(data: any){
    data.forEach((response:any) => {
      response['panelist__template'] = this.panelistCellTemplate;
      response['panelist__templateContext'] = response;
    });
  }

  closeResponsesDrawer(){
    this.responsesDrawerOpen = false;
  }

  toggleActionsPopover(){
    this.showActionsPopover = !this.showActionsPopover;
  }

  async setProject(data: any){
    const adapter = new ProjectDetailsAdapter();
    const project = await adapter.adaptDetails(data['project']);
    this.project = project;
  }

  async setPanelsTableData(){
    // if(this.project.associatedPanels[0].hasOwnProperty('product')){
      const panelAdapter = new PanelAdapter(this.apiService);
      this.panelsTableData = await panelAdapter.adapt(this.project.associatedPanels);
      // this.selectedPanelsArray = this.panelsTableData;
      // }else {
    //   const panelsAdapter = new ProjectPanelsAdapter();
    //   const panels = await this.apiService.getProjectPanels(this.project.id);      
    //   this.panelsTableData = await panelsAdapter.adapt(panels);
    // }
    this.insertButtonCells(this.panelsTableData);
    this.insertPanelSampleCells(this.panelsTableData);
  }

  insertButtonCells(data: any[]){
    data.forEach(panel => {
      panel['button__template'] = this.viewResponsesButtonCell;
      panel['button__templateContext'] = panel;
    });
  }

  insertPanelSampleCells(data: any[]){
    data.forEach(panel => {
      panel['productName__template'] = this.panelSampleCellTemplate;
      panel['productName__templateContext'] = panel;
    });
  }

  openCollaboratorsModal() {
    console.log('modalButton', this.modalButton);
    const modalButton = this.modalButton.nativeElement;
    this.modalOffset = { x: 0, y: modalButton.offsetTop};
    this.loadingUsers = true;
    this.getUsers();
    this.collaboratorsModalOpen = true;
  }

  getUsers() {
    this.profileService.getUsersListInMyOrg().then((users$) => {
      this.usersSubscription = users$.subscribe((usersData) => {
        const users = usersData as any[];
        this.users = users.map(user => ({
          name: `${user.first_name} ${user.last_name}`,
          email: user.email,
          role: this.formatRoles(user.roles),
          uid: user.uid
        }));
        this.users = this.insertCustomUserSelectCells(this.users);
        this.loadingUsers = false;
        
        // Mark existing collaborators as selected
        if (this.project.collaborators && this.project.collaborators.length > 0) {
          const collaboratorIds = this.project.collaborators.map((c: any) => c.uid);
          this.selectedUsersArray = this.users.filter(user => collaboratorIds.includes(user.uid));
        }
      });
    }).catch(error => {
      console.error('Error loading users', error);
      this.loadingUsers = false;
    });
  }

  formatRoles(roles: string[]): string {
    if (!roles || !Array.isArray(roles)) return '';
    return roles
      .map(role => role.charAt(0).toUpperCase() + role.slice(1))
      .join(', ');
  }

  insertCustomUserSelectCells(data: any[]) {
    return data.map((user: any) => {
      user['select__template'] = this.selectUserCellTemplate;
      user['select__templateContext'] = user;
      user['select__style'] = {textAlign: 'center'};
      return user;
    });
  }

  toggleUserSelection(selectedUser: any) {
    const isSelected = this.selectedUsersArray.some(user => user.uid === selectedUser.uid);
    
    // Set loading state for this specific user
    selectedUser.loading = true;
    
    if (isSelected) {
      // Remove collaborator from project
      this.apiService.removeCollaboratorFromProject(this.project.id, selectedUser.uid)
        .pipe(take(1))
        .subscribe({
          next: (res: any) => {
            console.log('Collaborator removed:', res);
            this.selectedUsersArray = this.selectedUsersArray.filter(user => user.uid !== selectedUser.uid);
            this.toastService.goodToast('Collaborator removed from project');
            selectedUser.loading = false;
          },
          error: (error: any) => {
            console.error('Error removing collaborator:', error);
            this.toastService.burntToast('Failed to remove collaborator');
            selectedUser.loading = false;
          }
        });
    } else {
      // Add collaborator to project
      this.apiService.addCollaboratorToProject(this.project.id, selectedUser.uid)
        .pipe(take(1))
        .subscribe({
          next: (res: any) => {
            console.log('Collaborator added:', res);
            this.selectedUsersArray.push(selectedUser);
            this.toastService.goodToast('Collaborator added to project');
            selectedUser.loading = false;
          },
          error: (error: any) => {
            console.error('Error adding collaborator:', error);
            this.toastService.burntToast('Failed to add collaborator');
            selectedUser.loading = false;
          }
        });
    }
  }

  showUserTooltip(event: MouseEvent, user: any) {
    const element = event.target as HTMLElement;
    const rect = element.getBoundingClientRect();
    
    this.tooltipPosition = {
      x: rect.left + 15,
      y: rect.top - 30 // Position above the element with more space
    };
    this.activeTooltipUser = user;
    this.activeTooltipText = user.name;
  }

  hideUserTooltip() {
    this.activeTooltipUser = null;
    this.activeTooltipText = '';
  }

  get privacySummary(){
    // need to look at the privacy of each panel and return a summary
    if(!this.project.associatedPanels.length) return 'No Panels';
    let hasPrivate = false;
    let hasPublic = false;
    this.project.associatedPanels.forEach((panel: any) => {
      if(panel.isPublic === true) hasPublic = true;
      if(panel.isPublic === false) hasPrivate = true;
    })
    if(hasPrivate && hasPublic) return 'Private and Public';
    if(hasPrivate && !hasPublic) return 'Private';
    if(!hasPrivate && hasPublic) return 'Public';
    return '';
  }

  getInitialsFromString(string: string, slice: number = 2){
    if(!string) return '';
    return string.split(' ').map(name => name[0]).join('').slice(0, slice).toUpperCase();
  }

  renameProject(){
    console.log('renameProject');
  }

  deleteProject() {
    if (confirm('Are you sure you want to delete this project? This action cannot be undone.')) {
      this.apiService.deleteProject(this.project.id)
        .pipe(take(1))
        .subscribe({
          next: () => {
            this.toastService.goodToast('Project deleted successfully');
            this.router.navigate(['/projects']);
          },
          error: (error: any) => {
            console.error('Error deleting project:', error);
            this.toastService.burntToast('Failed to delete project');
          }
        });
    }
  }

  showPanelSampleTooltip(event: MouseEvent, panel: any) {
    const element = event.target as HTMLElement;
    const rect = element.getBoundingClientRect();
    
    // Position directly above the element with fixed offsets
    this.tooltipPosition = {
      x: rect.left + window.scrollX + 15,  // Small horizontal offset
      y: rect.top + window.scrollY - 30   // Position well above the element
    };
    
    this.activeTooltipPanel = panel;
  }

  hidePanelSampleTooltip() {
    this.activeTooltipPanel = null;
  }

  isUserSelected(user: any): boolean {
    return this.selectedUsersArray.some(selectedUser => selectedUser.uid === user.uid);
  }

  async openAddPanelDrawer(){
    this.panelsDrawerOpen = true;
    if (this.allPanelsTableData.length === 0) {
      await this.loadMorePanels();
    }
    this.panelsDrawerTableData = this.allPanelsTableData;
  }

  insertSelectCells(data: any[]){
    data.forEach(panel => {
      panel['select__template'] = this.selectCellTemplate;
      panel['select__templateContext'] = panel;
      panel['select__style'] = {textAlign: 'center'};
    });
  }

  async loadMorePanels() {
    try {
      this.isLoadingMorePanels = true;
      const nextPage = this.panelsState.lastLoadedPage + 1;
      const response: any = await firstValueFrom(
        this.panelsService.getPanels({
          page: nextPage,
          limit: this.panelsState.pageSize
        })
      );

      // Update panels state
      this.panelsState.panels = [...this.panelsState.panels, ...response.panels];
      this.panelsState.lastLoadedPage = nextPage;
      this.panelsState.hasMore = response.hasMore;
      this.panelsState.totalCount = response.totalCount;

      // Adapt and add custom cells for new panels
      const adapter = new PanelAdapter(this.apiService);
      const adaptedNewPanels = await adapter.adapt(response.panels);
      const newPanelsWithCells = this.insertCustomCells(adaptedNewPanels);
      this.allPanelsTableData = [...this.allPanelsTableData, ...newPanelsWithCells];
      this.panelsDrawerTableData = this.allPanelsTableData;
    } catch (error) {
      console.error('Error loading more panels:', error);
    } finally {
      this.isLoadingMorePanels = false;
    }
  }

  insertCustomCells(data: any[]) {
    return data.map((panel: any) => {
      panel['productName__template'] = this.panelSampleCellTemplate;
      panel['productName__templateContext'] = panel;
      panel['select__template'] = this.selectCellTemplate;
      panel['select__templateContext'] = panel;
      panel['select__style'] = {textAlign: 'center'};
      return panel;
    });
  }

  closePanelsDrawer(){
    this.panelsDrawerOpen = false;
  }

  // selectedPanelsArray: any[] = [];

  togglePanelSelection(panel: any){
    console.log('panel', panel);
    
    if(this.isPanelSelected(panel)) {
      console.log('removing panel');
      this.apiService.removePanelFromProject(this.project.id, panel.panelId).pipe(take(1)).subscribe((res: any) => {
        console.log('res', res);
        this.panelsTableData = this.panelsTableData.filter(p => p !== panel);
        this.panelsTableData = [...this.panelsTableData];
        this.toastService.goodToast('Panel removed from project');
      });
    } else {
      console.log('adding panel');
      this.apiService.addPanelToProject(this.project.id, panel.panelId).pipe(take(1)).subscribe((res: any) => {
        console.log('res', res);
        this.insertButtonCells([panel]);
        this.panelsTableData.push(panel);
        this.panelsTableData = [...this.panelsTableData];
        this.toastService.goodToast('Panel added to project');
      });
    }

    // console.log('this.selectedPanelsArray', this.selectedPanelsArray);
  }

  isPanelSelected(panel: any){
    let panelId = panel.panelId;
    return this.panelsTableData.some(p => p.panelId === panelId);
  }

  ngOnDestroy() {
    if (this.usersSubscription) {
      this.usersSubscription.unsubscribe();
    }
  }

  resetErrors(){
    Object.keys(this.formErrors).forEach((x: any) => {
      this.formErrors[x] = false;
    })
  }

  saveProjectChanges() {
    if(this.buttonState === 0 || this.buttonState === 2) return;
    this.buttonState = 2;
    if (!this.validateForm()) {
      console.log('form is invalid', this.formErrors);
      
      this.buttonState = 1;
      return;
    }

    const defaultEndDate = new Date();
    defaultEndDate.setFullYear(defaultEndDate.getFullYear() + 100);
        
    const formValues = this.editProjectForm.value;
    console.log('formValues', formValues);
    const projectData = {
      name: formValues.projectName,
      description: formValues.description,
      start_date: formValues.startDate,
      end_date: formValues.endOption === 'custom' ? formValues.endDate : defaultEndDate.toISOString()
    };
    console.log('projectData', projectData);
    this.apiService.updateProject(this.project.id, projectData)
      .pipe(take(1))
      .subscribe({
        next: (response) => {
          // Update the local project data
          this.project.projectName = formValues.projectName;
          this.project.description = formValues.description;
          this.project.startDate = formValues.startDate;
          this.project.endDate = formValues.endDate;
          
          this.toastService.goodToast('Project updated successfully');
          this.closeEditDrawer();
        },
        error: (error) => {
          this.buttonState = 1;
          console.error('Error updating project:', error);
          this.toastService.burntToast('Failed to update project');
        }
      });
  }

  validateForm(): boolean {
    // Reset previous errors
    for (const key in this.formErrors) {
      this.formErrors[key] = false;
    }
    
    let isValid = true;
    
    // Validate project name
    if (!this.editProjectForm.get('projectName')?.value) {
      this.formErrors.projectName = true;
      this.errorMessage = 'Project name is required';
      isValid = false;
    }

    // Validate description
    if (!this.editProjectForm.get('description')?.value) {
      this.formErrors.description = true;
      this.errorMessage = 'Description is required';
      isValid = false;
    }

    // Validate start date
    if (!this.editProjectForm.get('startDate')?.value) {
      this.formErrors.startDate = true;
      this.errorMessage = 'Start date is required';
      isValid = false;
    }
  
    
    // Validate end date if custom option is selected
    if (this.editProjectForm.get('endOption')?.value === 'custom' && 
        !this.editProjectForm.get('endDate')?.value) {
      this.formErrors.endDate = true;
      this.errorMessage = 'End date is required';
      isValid = false;
    }
    
    return isValid;
  }
}
