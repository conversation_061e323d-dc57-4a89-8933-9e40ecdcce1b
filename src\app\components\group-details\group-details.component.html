<a [routerLink]="['/groups']">
    <button-standard class="back-button">
        <svg xmlns="http://www.w3.org/2000/svg" width="16" height="13" viewBox="0 0 16 13" fill="none">
            <path d="M15.2134 5.54389H2.66023L6.93207 1.33322C7.24094 1.03018 7.24248 0.535256 6.93668 0.230712C6.63088 -0.0753749 6.13144 -0.0768952 5.82413 0.226144L0.198231 5.75837C0.161351 5.7934 0.129081 5.83299 0.0998831 5.87563V5.89847C0.0752961 5.9335 0.056854 5.97157 0.0399538 6.01116V6.03857C0.0399538 6.07816 0.0122934 6.11623 0.00461006 6.15582C-0.00153669 6.2076 -0.00153669 6.25938 0.00461006 6.31115V6.35074C0.00307347 6.38882 0.00307347 6.42537 0.00461006 6.46343C0.00461006 6.49846 0.0245871 6.53044 0.0322704 6.56089C0.035344 6.57764 0.035344 6.5944 0.0322704 6.61115L0.0829826 6.70404L0.106032 6.74363H0.10757C0.13523 6.78322 0.165964 6.82129 0.198234 6.85784L5.79935 12.4283C6.10976 12.7085 6.58766 12.6978 6.88424 12.4039C7.18082 12.11 7.19465 11.638 6.91344 11.3288L2.64907 7.10294H15.2132C15.6481 7.10294 16 6.75421 16 6.32325C16 5.8923 15.6482 5.54389 15.2134 5.54389Z" fill="black"/>
        </svg>
        <span>Back to Groups</span>
    </button-standard>
</a>

<div class="group-name-row">
    <div class="group-name-grid-area">
        <div class="group-name">{{ group.name }}</div>
        <div class="group-product-container">
            <div class="arrow-container">
                <svg xmlns="http://www.w3.org/2000/svg" width="16" height="22" viewBox="0 0 16 22" fill="none">
                    <path d="M15.3536 18.3536C15.5488 18.1583 15.5488 17.8417 15.3536 17.6464L12.1716 14.4645C11.9763 14.2692 11.6597 14.2692 11.4645 14.4645C11.2692 14.6597 11.2692 14.9763 11.4645 15.1716L14.2929 18L11.4645 20.8284C11.2692 21.0237 11.2692 21.3403 11.4645 21.5355C11.6597 21.7308 11.9763 21.7308 12.1716 21.5355L15.3536 18.3536ZM0.5 0V12H1.5V0H0.5ZM7 18.5H15V17.5H7V18.5ZM0.5 12C0.5 15.5899 3.41015 18.5 7 18.5V17.5C3.96243 17.5 1.5 15.0376 1.5 12H0.5Z" fill="#C6C6C6"/>
                </svg>
            </div>
            <span>{{group.description}}</span>
        </div>
    </div>

    <div class="buttons-grid-area">
        <div class="actions-button-container">
            <button-standard class="actions-button no-padding" (onAction)="toggleActionsPopover()">
                <svg xmlns="http://www.w3.org/2000/svg" width="39" height="39" viewBox="0 0 39 39" fill="none">
                    <path d="M21.5 20.1111C21.5 21.277 20.6045 22.2222 19.5 22.2222C18.3955 22.2222 17.5 21.277 17.5 20.1111C17.5 18.9452 18.3955 18 19.5 18C20.6045 18 21.5 18.9452 21.5 20.1111Z" fill="black"/>
                    <path d="M14 20.1111C14 21.277 13.1045 22.2222 12 22.2222C10.8955 22.2222 10 21.277 10 20.1111C10 18.9452 10.8955 18 12 18C13.1045 18 14 18.9452 14 20.1111Z" fill="black"/>
                    <path d="M29 20.1111C29 21.277 28.1045 22.2222 27 22.2222C25.8955 22.2222 25 21.277 25 20.1111C25 18.9452 25.8955 18 27 18C28.1045 18 29 18.9452 29 20.1111Z" fill="black"/>
                </svg>
            </button-standard>
            <popover *ngIf="showActionsPopover" [@fadeScale]>
                <div class="popover-item" (click)="popupService.editGroup(group, 1)">Edit</div>
                <div class="popover-item" (click)="deleteGroup(group)">Delete</div>
            </popover>
        </div>
    </div>
</div>

<div class="group-summary-row">
    <div class="group-summary-section">
        <div class="section-label">Number of Panels</div>
        <div class="section-content">{{group.panels.length}}</div>
    </div>

    <div class="group-summary-section">
        <div class="section-label">Number of Samples</div>
        <div class="section-content">{{group.samples.length}}</div>
    </div>

    <div class="group-summary-section">
        <div class="section-label">Number of Panelists</div>
        <div class="section-content">{{group.users.length}}</div>
    </div>

    <div class="group-summary-section">
        <div class="section-label">Group Owner</div>
        <div class="section-content">
            <lamb-gp-icon 
             [userInitials]="getOwnerInitials(group)"></lamb-gp-icon>
            <div class="owner-name-container">{{group.owner.first_name}} {{group.owner.last_name}}</div>
        </div>
    </div>
</div>

<div class="group-details-header">
    <div class="header-label">Associated Entities</div>
    <div class="header-elem">
        <div class="search-container">
            <input-text
              [control]="searchControl"
              class="search-input"
              [placeholder]="'Search'"
            >
            </input-text>
        </div>
        <div class="filter-container">
            <input-text 
            [control]="filterControl" 
            class="filter-input" 
            [placeholder]="'Filters'"
            > </input-text>
        </div>
    </div>
</div>

<div class="table-container">
    <span class="table-label panels"><div class="table-panel-text">Panels</div><div class="decorator-line"></div></span>
    <lamb-table 
        [headers]="panelsTableHeaders" 
        [data]="panelsTableData" 
        [styles]="panelsTableStyles">
    </lamb-table>
</div>

<div class="table-container">
    <span class="table-label samples"><div class="table-panel-text">Samples</div><div class="decorator-line"></div></span>
    <lamb-table 
        [headers]="samplesTableHeaders" 
        [data]="samplesTableData" 
        [styles]="samplesTableStyles" 
        [clickableRows]="true" 
        (rowClick)="onSampleRowClick($event)">
    </lamb-table>
</div>

<div class="table-container">
    <span class="table-label panelists"><div class="table-panel-text">Panelists</div><div class="decorator-line"></div></span>
    <lamb-table 
        [headers]="usersTableHeaders" 
        [data]="usersTableData" 
        [styles]="usersTableStyles">
    </lamb-table>
</div>

<lamb-graph-tooltip 
    *ngIf="activeTooltipItem"
    [style.position]="'fixed'"
    [style.left.px]="tooltipPosition.x"
    [style.top.px]="tooltipPosition.y"
    [config]="{mainText: {content: tooltipContent}}"
    [@fadeInOut]
    #tooltip>
</lamb-graph-tooltip>

<ng-template #statusCellTemplate let-status>
    <div class="status-cell">
        <div class="color-dot" [style.background-color]="getColorFromStatus(status)"></div>
        <div class="status-text">{{status.status}}</div>
    </div>
</ng-template>

<ng-template #viewResponsesButtonCell let-panel>
    <button-standard class="view-responses-button" (onAction)="openResponseDetails(panel)"><svg xmlns="http://www.w3.org/2000/svg" width="12" height="11" viewBox="0 0 12 11" fill="none">
        <path d="M0 0H3.20026V11H0V0Z" fill="#292727"/>
        <path d="M4.40026 2.0373H7.60051V11H4.40026V2.0373Z" fill="#292727"/>
        <path d="M8.79974 3.66667H12V11H8.79974V3.66667Z" fill="#292727"/>
      </svg><span>View Responses</span></button-standard>
</ng-template>

<ng-template #panelSampleCellTemplate let-panel>
    <div class="panel-sample-cell" 
         (mouseenter)="showPanelSampleTooltip($event, panel)"
         (mouseleave)="hidePanelSampleTooltip()">
        <lamb-gp-icon [userInitials]="getInitialsFromString(panel?.sample, 1)" [imageUrl]="panel.sampleImage"></lamb-gp-icon>
    </div>
</ng-template>

<ng-template #userIconCellTemplate let-user>
    <div class="user-icon-cell">
        <lamb-gp-icon [userInitials]="getInitialsFromString(user?.name, 2)"></lamb-gp-icon>
    </div>
</ng-template>