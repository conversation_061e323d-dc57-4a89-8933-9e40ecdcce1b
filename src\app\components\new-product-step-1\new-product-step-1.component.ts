import { Component, inject } from '@angular/core';
import { FormControl, FormGroup, ReactiveFormsModule } from '@angular/forms';
import { ProductsService } from '../../services/products.service';
import { InputImgComponent } from '../input-img/input-img.component';
import { InputTextComponent } from '../input-text/input-text.component';
import { InputTextAreaComponent } from '../input-text-area/input-text-area.component';
import { InputSelectComponent } from '../input-select/input-select.component';
import { ConstantsService } from '../../services/constants.service';

@Component({
  selector: 'new-product-step-1',
  standalone: true,
  imports: [ReactiveFormsModule, InputImgComponent, InputTextComponent, InputTextAreaComponent, InputSelectComponent],
  templateUrl: './new-product-step-1.component.html',
  styleUrl: './new-product-step-1.component.scss'
})
export class NewProductStep1Component {

  productsService = inject(ProductsService);
  constantsService = inject(ConstantsService);
  form: FormGroup = this.productsService.step1Form;

  matrixOptions = this.constantsService.matrix;

  nameControl: FormControl = this.form.get('name') as FormControl;
  descriptionControl: FormControl = this.form.get('description') as FormControl;
  codeControl: FormControl = this.form.get('productCode') as FormControl;
  typeControl: FormControl = this.form.get('type') as FormControl;
  subtypeControl: FormControl = this.form.get('subtype') as FormControl;
  imageControl: FormControl = this.form.get('image') as FormControl;

  ngOnInit(){
    console.log(this.imageControl.value);
  }
}
