import { Routes } from '@angular/router';
import { LoginComponent } from './components/login/login.component';
import { MainComponent } from './components/main/main.component';
import { authGuard } from './guards/auth.guard';
import { redirectLoggedInGuard } from './guards/redirect-logged-in.guard';
import { NotFoundComponent } from './components/not-found/not-found.component';
import { profileResolver } from './resolvers/profile.resolver';
import { constantsResolver } from './resolvers/constants.resolver';
import { DashboardComponent } from './components/dashboard/dashboard.component';
import { PanelsComponent } from './components/panels/panels.component';
import { PanelistsComponent } from './components/panelists/panelists.component';
import { ProductsComponent } from './components/products/products.component';
import { SettingsComponent } from './components/settings/settings.component';
import { SettingsProfileComponent } from './components/settings-profile/settings-profile.component';
import { SettingsEmailComponent } from './components/settings-email/settings-email.component';
import { SettingsPasswordComponent } from './components/settings-password/settings-password.component';
import { GroupsComponent } from './components/groups/groups.component';

import { productDetailsResolver, productInsightsResolver, productsResolver, productCommentsResolver, productBackButtonResolver, productQuestionDetailsResolver } from './resolvers/products.resolver';
import { panelDetailsResolver, panelInsightsResolver, panelCommentsResolver, backButtonResolver } from './resolvers/panels.resolver';
import { PanelDetailsComponent } from './components/panel-details/panel-details.component';
import { ProductDetailsComponent } from './components/product-details/product-details.component';
import { GroupDetailsComponent } from './components/group-details/group-details.component';
import { groupPanelsResolver, groupSamplesResolver, groupsResolver, groupUsersResolver } from './resolvers/groups.resolver';
import { ProjectsComponent } from './components/projects/projects.component';
import { ProjectDetailsComponent } from './components/project-details/project-details.component';
import { projectDetailsResolver } from './resolvers/projects.resolver';
import { CanDeactivateCustomStepGuard } from './guards/can-deactivate-custom-step.guard';
import { SignupComponent } from './components/signup/signup.component';
import { TemplatesComponent } from './components/templates/templates.component';
import { TemplateDetailsComponent } from './components/template-details/template-details.component';
import { templateDetailsResolver } from './resolvers/templates.resolver';

export const routes: Routes = [
    { path: '', component: MainComponent, canActivate: [authGuard], resolve: [profileResolver, constantsResolver],
        children: [
            { path: '', redirectTo: 'panels', pathMatch: 'full'},
            { path: 'home', component: DashboardComponent},
            { path: 'panels', component: PanelsComponent, resolve: {products: productsResolver}, canDeactivate: [CanDeactivateCustomStepGuard]},
            { path: 'panels/:id', component: PanelDetailsComponent, resolve: {
                panel: panelDetailsResolver,
                insights: panelInsightsResolver,
                comments: panelCommentsResolver,
                backButton: backButtonResolver,
            }},
            { path: 'panelists', component: PanelistsComponent},
            { path: 'products', component: ProductsComponent},
            { path: 'products/:id', component: ProductDetailsComponent, resolve: {
                product: productDetailsResolver,
                insights: productInsightsResolver,
                comments: productCommentsResolver,
                backButton: productBackButtonResolver,
                questionDetails: productQuestionDetailsResolver
            }},
            {path: 'groups', component: GroupsComponent},
            {path: 'groups/:id', component: GroupDetailsComponent, resolve: {
                group: groupsResolver,
                panels: groupPanelsResolver,
                samples: groupSamplesResolver,
                users: groupUsersResolver
            }},
            { path: 'projects', component: ProjectsComponent},
            { path: 'projects/:id', component: ProjectDetailsComponent, resolve: {project: projectDetailsResolver}},
            { path: 'settings', component: SettingsComponent, children: [
                {path: '', redirectTo: 'profile', pathMatch: 'full'},
                {path: 'profile', component: SettingsProfileComponent},
                {path: 'email', component: SettingsEmailComponent},
                {path: 'password', component: SettingsPasswordComponent},
                // {path: 'manage-affiliates', component: SettingsManageAffiliatesComponent},
                // {path: 'notifications', component: SettingsGeneralNotificationsComponent},
                // {path: 'privacy', component: SettingsGeneralPrivacyComponent},
                // {path: 'terms-and-conditions', component: SettingsGeneralTAndCComponent},
                // {path: 'privacy-policy', component: SettingsGeneralPrivacyComponent},
                // {path: 'delete-account', component: SettingsGeneralDeleteAccountComponent},
            ] },
            {path:'templates', component: TemplatesComponent},
            {path:'templates/:id', component: TemplateDetailsComponent, resolve: {
                template: templateDetailsResolver
            }}
        ]
    },
    { path: 'login', component: LoginComponent, canActivate: [redirectLoggedInGuard]},
    { path: 'signup', component: SignupComponent, canActivate: [redirectLoggedInGuard]},
    { path: '**', component: NotFoundComponent}
];
