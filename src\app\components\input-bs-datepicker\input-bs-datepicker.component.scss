@import '../../../_colors';

:host{
    display: block;
    width: 125px;
    height: 40px;

    input{
        appearance: none;
        -webkit-appearance: none;
        -moz-appearance: none;
        border: none;
        outline: none;
        background: none;
        padding: 0;
        margin: 0;
        border-radius: 0;
        font-family: inherit;
        font-size: inherit;
        color: inherit;
        box-sizing: border-box;

        background-color: $inputBackground;
        outline: $inputBorder;
        width: 100%;
        height: 100%;
        padding: 7px 10px;
        border-radius:10px;
        font-size: .75rem;
        color: $appText;

        &:focus{
            outline: $inputFocusBorder;
        }

        &::placeholder{
            color: $placeholderText;
            font-size: .75rem;
            font-weight: 400;
        }
    }
}