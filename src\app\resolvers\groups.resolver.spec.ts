import { TestBed } from '@angular/core/testing';
import { ResolveFn } from '@angular/router';

import { groupsResolver } from './groups.resolver';

describe('groupsResolver', () => {
  const executeResolver: ResolveFn<boolean> = (...resolverParameters) => 
      TestBed.runInInjectionContext(() => groupsResolver(...resolverParameters));

  beforeEach(() => {
    TestBed.configureTestingModule({});
  });

  it('should be created', () => {
    expect(executeResolver).toBeTruthy();
  });
});
