import { Component, inject } from '@angular/core';
import { NgIf } from '@angular/common';
// import { BackButtonComponent } from '../back-button/back-button.component';
import { RouterLink } from '@angular/router';
import { FormBuilder, FormGroup, ReactiveFormsModule, FormControl } from '@angular/forms';
import { ProfileService } from '../../services/profile.service';
import { ButtonStandardComponent } from "../button-standard/button-standard.component";
import { AuthService } from '../../services/auth.service';
import { fadeIn } from '../../modules/animations/animations.module';
import { ToastService } from '../../services/toast.service';
import { firstValueFrom } from 'rxjs';
import { ApiService } from '../../services/api.service';

@Component({
  selector: 'settings-email',
  standalone: true,
  imports: [NgIf, RouterLink, ReactiveFormsModule, ButtonStandardComponent],
  templateUrl: './settings-email.component.html',
  styleUrl: './settings-email.component.scss',
  animations: [fadeIn]
})
export class SettingsEmailComponent {

  fb = inject(FormBuilder);
  profileService = inject(ProfileService);
  authService = inject(AuthService);
  toastService = inject(ToastService);
  apiService = inject(ApiService);
  
  emailForm: FormGroup = this.fb.group({
    new_email: new FormControl(''),
    confirm_new_email: new FormControl('')
  });
  emailUpdated = false;

  ngOnInit(){
    console.log('authUser:', this.profileService.authUser);
  }

  dismissEmailUpdated(){
    this.emailUpdated = false;
  } 

  async sendVerificationEmail(){
    try {
      await this.authService.sendVerificationEmail();
      this.toastService.goodToast('Verification email sent successfully!');
    } catch (error: any) {
      this.toastService.burntToast('An error occurred while sending verification email');
    }
  }

  async updateEmail(){
    console.log('Updating email:', this.emailForm.value);
    if(this.emailForm.value.new_email === this.emailForm.value.confirm_new_email){
      try {
        const res: any = await firstValueFrom(this.apiService.updateEmail(this.emailForm.value.new_email));
        this.emailUpdated = true;
        console.log('Email updated:', res);
        this.toastService.goodToast('Email updated successfully!');
      } catch (error: any) {
        this.toastService.burntToast('An error occurred while updating email');
      }
    }
  }

}
