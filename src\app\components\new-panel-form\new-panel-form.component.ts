import { Component, inject, ElementRef } from '@angular/core';
import { NgIf } from '@angular/common';
import { PopupService } from '../../services/popup.service';
import { PanelsService } from '../../services/panels.service';
import { FormHeaderComponent } from '../form-header/form-header.component';
import { NewPanelStep1Component } from '../new-panel-step-1/new-panel-step-1.component';
import { NewPanelStep2Component } from '../new-panel-step-2/new-panel-step-2.component';
import { NewPanelStep3Component } from '../new-panel-step-3/new-panel-step-3.component';
import { NewPanelStep4Component } from '../new-panel-step-4/new-panel-step-4.component';
import { NewPanelStep5Component } from '../new-panel-step-5/new-panel-step-5.component';
import { NewPanelStep6Component } from '../new-panel-step-6/new-panel-step-6.component';
import { FormFooterComponent } from '../form-footer/form-footer.component';
import { ButtonTwoClickComponent } from '../button-two-click/button-two-click.component';
import { Subscription } from 'rxjs';
import { NewPanelStep2TtComponent } from '../new-panel-step-2-tt/new-panel-step-2-tt.component';
import { NewPanelStep3TtComponent } from '../new-panel-step-3-tt/new-panel-step-3-tt.component';

@Component({
  selector: 'new-panel-form',
  standalone: true,
  imports: [
    FormHeaderComponent, 
    NewPanelStep1Component, 
    NewPanelStep2Component, 
    NewPanelStep2TtComponent, 
    NewPanelStep3Component, 
    NewPanelStep4Component, 
    NewPanelStep5Component, 
    NewPanelStep6Component, 
    FormFooterComponent, 
    NgIf, 
    ButtonTwoClickComponent,
    NewPanelStep3TtComponent
  ],
  templateUrl: './new-panel-form.component.html',
  styleUrl: './new-panel-form.component.scss'
})
export class NewPanelFormComponent {
  private elementRef = inject(ElementRef);
  popupService = inject(PopupService);
  panelsService = inject(PanelsService);

  subscription: Subscription = new Subscription();

  ngOnInit() {
    this.subscription = this.panelsService.scrollResetSubject.subscribe(() => {
      this.elementRef.nativeElement.scrollTop = 0;
    });

    console.log('Initial experiment type:', this.experimentType);

    // Subscribe to experiment type changes
    const experimentTypeControl = this.panelsService.step1Form.get('experiment_type');
    if (experimentTypeControl) {
      this.experimentTypeSubscription = experimentTypeControl.valueChanges.subscribe(value => {
        console.log('Experiment type changed to:', value);
        console.log('Experiment type getter returns:', this.experimentType);
      });
    }
  }


  get experimentType(): string {
    const type = this.panelsService.step1Form.get('experiment_type')?.value;
    console.log('current type', type);

    // Handle different formats of experiment type
    if (type === 'normal_sensory' || type === 'Normal Sensory') {
      return 'Normal Sensory';
    } else if (type === 'triangle_test' || type === 'Triangle Test') {
      return 'Triangle Test';
    }

    return type || '';
  }

  // Store the experiment type value changes subscription
  private experimentTypeSubscription: Subscription | null = null;

  ngOnDestroy() {
    // Clean up subscriptions to prevent memory leaks
    this.subscription.unsubscribe();

    if (this.experimentTypeSubscription) {
      this.experimentTypeSubscription.unsubscribe();
    }
  }

  // @HostListener('window:beforeunload', ['$event'])
  // onBeforeUnload(event: BeforeUnloadEvent) {
  //   // Cancel the event and show confirmation dialog
  //   event.preventDefault();
  //   // Chrome requires returnValue to be set
  //   // @ts-ignore
  //   event.returnValue = '';

  //   // The browser will handle showing a standard confirmation dialog
  //   return '';
  // }

  close(){
    this.popupService.closeNewPanel();
  }


}
