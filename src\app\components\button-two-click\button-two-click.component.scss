@import '../../../_colors.scss';

:host{
    display: flex;
    margin:0px auto;
    width: fit-content;
    height: 40px;
    position: relative;
    transition: all 0.3s ease;


    button{
        background-color: $lightestGray;
        color: $buttonTextColor;
        border-radius: 10px;
        cursor: pointer;
        transition: all 0.2s ease;
        width: 100%;
        height: 100%;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 12px;
        font-weight: 600;
        box-sizing: border-box;
        padding: 0px 10px;
        border: 1px solid $mediumLightGray;
        white-space: nowrap;
        transition: all 0.3s ease;
        position: relative;
        overflow: hidden;

        &:hover{
            filter: brightness(1.05);
            transform: translateY(-1px);
        }

        &:active{
            filter: brightness(.95);
            transform: translateY(0px);

        }

        .text-container {
            position: relative;
            display: inline-flex;
            height: inherit;
            align-items: center;
            // min-width: 50px;
            
            span {
                position: absolute;
                left: 0;
                white-space: nowrap;

            }

            .hidden-text {
                position: relative;
                visibility: hidden;
                height: 0;
            }
        }
    }

    &.light-bg{
        button{
            &:hover{
                filter: brightness(.95);
                transform: translateY(-1px);
            }

            &:active{
                filter: brightness(.90);
                transform: translateY(0px);
            }
        }
    }

    // &.yellow{
    //     button{
    //         background-color: $yellow;
    //         color: #000;
    //     }
    // }

    // &.start {
    //     height: 65px;
    //     button {
    //         background-color: $yellow;
    //         color: #000;
    //         border-radius: 18px;
    //     }
    // }

    // &.blue{
    //     button{
    //         background-color: $blue;
    //         color: #fff;
    //     }
    // }

    // &.red{
    //     button{
    //         background-color: $red;
    //         color: #fff;
    //     }
    // }

    // &.disabled{
    //     button{
    //         background-color: $lightestGray;
    //         color: $mediumDarkGray;
    //     }
    // }

    // &.green{
    //     button{
    //         background-color: $green;
    //         color: #fff;
    //     }
    // }
}