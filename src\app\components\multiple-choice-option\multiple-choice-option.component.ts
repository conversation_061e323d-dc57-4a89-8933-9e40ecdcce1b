import { Component, Input, Output, EventEmitter } from '@angular/core';
import { NgIf } from '@angular/common';
import { InputTextComponent } from '../input-text/input-text.component';
import { InputTextAreaComponent } from '../input-text-area/input-text-area.component';
import { DigitToWordPipe } from '../../pipes/digit-to-word.pipe';

@Component({
  selector: 'multiple-choice-option',
  standalone: true,
  imports: [
    InputTextComponent,
    InputTextAreaComponent,
    DigitToWordPipe,
    NgIf,
  ],
  templateUrl: './multiple-choice-option.component.html',
  styleUrl: './multiple-choice-option.component.scss'
})
export class MultipleChoiceOptionComponent {

  @Input() option: any;
  @Input() index: number = 0;
  @Input() editing: boolean = false;

  @Output() removeOptionEvent = new EventEmitter<number>();

  removeOption(){
    this.removeOptionEvent.emit(this.index);
  }

  showRemoveButton(){
    return !this.editing && this.index > 0;
  }
}
