import { Component, inject } from '@angular/core';
import { FormGroup, FormControl, ReactiveFormsModule } from '@angular/forms';
import { ProductsService } from '../../services/products.service';
import { InputTextComponent } from '../input-text/input-text.component';
import { InputTextAreaComponent } from '../input-text-area/input-text-area.component';
import { InputSelectComponent } from '../input-select/input-select.component';
import { InputBsDatepickerComponent } from '../input-bs-datepicker/input-bs-datepicker.component';
import { ConstantsService } from '../../services/constants.service';

@Component({
  selector: 'new-product-step-2',
  standalone: true,
  imports: [ReactiveFormsModule, InputTextComponent, InputTextAreaComponent, InputSelectComponent, InputBsDatepickerComponent],
  templateUrl: './new-product-step-2.component.html',
  styleUrl: './new-product-step-2.component.scss'
})
export class NewProductStep2Component {

  productsService = inject(ProductsService);
  constantsService = inject(ConstantsService);

  form: FormGroup = this.productsService.step2Form;
  packagingOptions = this.constantsService.packaging;
  packagingControl: FormControl = this.form.get('packaging') as FormControl;
  lotNumberControl: FormControl = this.form.get('lotNumber') as FormControl;
  manuDateControl: FormControl = this.form.get('manuDate') as FormControl;
  packageDateControl: FormControl = this.form.get('packageDate') as FormControl;
  producerControl: FormControl = this.form.get('producer') as FormControl;
  notesControl: FormControl = this.form.get('notes') as FormControl;
}
