<div class="pagination-wrap">
    <div class="pagination">
        <div class="main">
            <div class="navigate" [class.inactive]="state?.currentPage === 1" (click)="goToFirst()" id="start">
                <svg width="12" height="10" viewBox="0 0 12 10" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path d="M11 1L7 5L11 9" stroke="#262626" stroke-width="1.33333" stroke-linecap="round" stroke-linejoin="round" />
                    <path d="M5 1L1 5L5 9" stroke="#262626" stroke-width="1.33333" stroke-linecap="round" stroke-linejoin="round" />
                </svg>
            </div>
            <div class="navigate" [class.inactive]="state?.currentPage === 1" (click)="previousPage()" id="back">
                <svg width="6" height="11" viewBox="0 0 6 11" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path d="M4.97998 9.5L0.97998 5.5L4.97998 1.5" stroke="#262626" stroke-width="1.33333" stroke-linecap="round"
                        stroke-linejoin="round" />
                </svg>
            </div>
            <div class="specific-page">
                <div *ngFor="let page of pages" 
                     [class.active]="page === state?.currentPage"
                     (click)="goToPage(page)">
                    {{ page }}
                </div>
            </div>
            <div class="navigate" [class.inactive]="!state?.hasMore" (click)="nextPage()" id="next">
                <svg width="6" height="11" viewBox="0 0 6 11" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path d="M1 9.5L5 5.5L1 1.5" stroke="#262626" stroke-width="1.33333" stroke-linecap="round"
                        stroke-linejoin="round" />
                </svg>
            </div>
            <div class="navigate" [class.inactive]="state?.currentPage === state?.totalPages" (click)="goToLast()" id="end">
                <svg width="12" height="11" viewBox="0 0 12 11" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path d="M1 9.5L5 5.5L1 1.5" stroke="#262626" stroke-width="1.33333" stroke-linecap="round"
                        stroke-linejoin="round" />
                    <path d="M7 9.5L11 5.5L7 1.5" stroke="#262626" stroke-width="1.33333" stroke-linecap="round"
                        stroke-linejoin="round" />
                </svg>
            </div>
        </div>
        <div class="jump">
            Go to page <input type="number" [min]="1" [max]="state?.totalPages" 
                            (keyup.enter)="goToPage($any($event.target).value)" />
        </div>
    </div>
</div>