@import "../../../_colors.scss";

:host {
  --page-name-color: #000;
  --page-name-size: 1.2rem;
  --page-name-weight: 700;

  display: flex;
  align-items: center;
  justify-content: flex-start;

  .page-name {
    color: var(--page-name-color);
    font-size: var(--page-name-size);
    font-weight: var(--page-name-weight);
    margin-right: 15px;
  }

  .page-info {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    font-size: 11px;
    font-weight: 600;
    color: $mediumDarkGray;
  }

  .fs {
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 12px 13px;
    border: 1px solid $mediumLightGray;
    background-color: $lightestGray;
    border-radius: 12px;
    cursor: pointer;
    font-size: 13px;
  }

  .filter-container {
    margin-left: auto;
    margin-right: 15px;

    svg {
      margin-left: 10px;
      margin-right: 0;
    }
  }

  .search-container {
    margin-right: 15px;

    input {
      background-image: url("../../../assets/images/search.svg");
      outline: none;
      padding: 0 0 0 20px;
      background-size: 10px;
      background-repeat: no-repeat;
      background-position: 0px 50%;
      cursor: pointer;
      height: auto;
      font-size: 14px;
      width: 200px;

      &::placeholder {
        color: black;
      }
    }

    &:has(input:focus) {
      box-shadow: 0 0 6px $focusBlue;
    }
  }

  .add-button-container {
    margin-right: 15px;
    margin-left: auto;

    button-standard {
      height: 42px;
      width: fit-content;
    }
  }
  .varient-3 {
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    align-items: center;
    gap: 10px;

    .line {
      width: 20px;
      top: 23px;
      left: 237px;
      border-width: 1px;
      border: 1px solid #9d9d9d;
    }

    .url {
      width: 103;
      height: 16;
      top: 14px;
      left: 393px;
      font-family: Inter;
      font-weight: 500;
      font-size: 13px;
      line-height: 100%;
      letter-spacing: 0%;
      color: #505050;

      :hover {
        cursor: pointer;
      }
    }

    .active-url {
      font-family: Inter;
      font-weight: 700;
      font-size: 13px;
      line-height: 100%;
      letter-spacing: 0%;
      color: #292727;
    }
  }
}
