import { Injectable } from '@angular/core';

type TriangleTest = {
  pair: [string, string];
  permutationId: string;
  sampleCodes: string[];
  trueMapping: Record<string, string>;
  oddSamplePosition: number;
};

@Injectable({
  providedIn: 'root'
})
export class TriangleTestService {

  constructor() { }
  
  private shuffleArray<T>(array: T[], seed?: number): T[] {
    const result = array.slice();
    let random = Math.random;
  
    // Deterministic seeding if provided
    if (seed !== undefined) {
      let s = seed;
      random = () => {
        // Xorshift simple seedable RNG
        s ^= s << 13;
        s ^= s >> 17;
        s ^= s << 5;
        return ((s < 0 ? ~s + 1 : s) % 1000) / 1000;
      };
    }
  
    for (let i = result.length - 1; i > 0; i--) {
      const j = Math.floor(random() * (i + 1));
      [result[i], result[j]] = [result[j], result[i]];
    }
    return result;
  }
  
  /**
   * Generates a set of sample codes large enough for all tests
   * @param neededCodes Number of unique codes needed
   * @returns Array of unique sample codes
   */
  private generateSampleCodes(neededCodes: number): string[] {
    // Base label pool
    const letters = ['A', 'B', 'C', 'D', 'E', 'F', 'G', 'H', 'J', 'K', 'L', 'M', 'N', 'P', 'Q', 'R', 'S', 'T'];
    const numbers = ['1', '2', '3', '4', '5', '6', '7', '8', '9'];
    
    // Generate all possible combinations of letter+number
    const allCodes: string[] = [];
    
    for (const letter of letters) {
      for (const number of numbers) {
        allCodes.push(`${letter}${number}`);
      }
    }
    
    // If we need more codes than available, duplicate some with another format
    if (neededCodes > allCodes.length) {
      for (const letter of letters) {
        for (const number of numbers) {
          allCodes.push(`${number}${letter}`);
          if (allCodes.length >= neededCodes) break;
        }
        if (allCodes.length >= neededCodes) break;
      }
    }
    
    // Shuffle and return only the needed number of codes
    return this.shuffleArray(allCodes).slice(0, neededCodes);
  }
  
  generateTriangleTests(products: string[], seed?: number): TriangleTest[] {
    if (products.length < 2) {
      throw new Error("At least two products are required for triangle testing.");
    }
    
    // Calculate total number of test cases to determine how many sample codes we need
    let totalTestCases = 0;
    for (let i = 0; i < products.length; i++) {
      for (let j = i + 1; j < products.length; j++) {
        // For each product pair, we'll generate up to 6 test cases (3 positions × 2 scenarios)
        totalTestCases += 6;
      }
    }
    
    // Generate enough unique sample codes for all test cases (3 codes per test)
    const sampleCodePool = this.generateSampleCodes(totalTestCases * 3);
    let sampleCodeIndex = 0;
    
    const testCases: TriangleTest[] = [];
  
    for (let i = 0; i < products.length; i++) {
      for (let j = i + 1; j < products.length; j++) {
        const product1 = products[i];
        const product2 = products[j];
  
        // For both (2x A, 1x B) and (2x B, 1x A)
        for (const [majority, minority] of [[product1, product2], [product2, product1]]) {
          const triangle = [majority, majority, minority];
          const permutations = this.permute(triangle);
  
          const seenPositions = new Set<number>();
  
          for (const perm of permutations) {
            const oddPos = perm.findIndex(p => p !== majority);
            if (seenPositions.has(oddPos)) continue;
            seenPositions.add(oddPos);
  
            // Get 3 unique sample codes for this test case
            const sampleCodes = sampleCodePool.slice(sampleCodeIndex, sampleCodeIndex + 3);
            sampleCodeIndex += 3;
            
            const trueMapping: Record<string, string> = {
              [sampleCodes[0]]: perm[0],
              [sampleCodes[1]]: perm[1],
              [sampleCodes[2]]: perm[2],
            };
  
            testCases.push({
              pair: [product1, product2],
              permutationId: `${product1}_${product2}_${perm.join("")}_${oddPos}`,
              sampleCodes,
              trueMapping,
              oddSamplePosition: oddPos,
            });
          }
        }
      }
    }
  
    return testCases;
  }
  
  private permute<T>(arr: T[]): T[][] {
    if (arr.length <= 1) return [arr];
  
    const results: T[][] = [];
    const used = new Array(arr.length).fill(false);
  
    const backtrack = (current: T[]) => {
      if (current.length === arr.length) {
        results.push(current.slice());
        return;
      }
  
      for (let i = 0; i < arr.length; i++) {
        if (used[i]) continue;
        used[i] = true;
        current.push(arr[i]);
        backtrack(current);
        current.pop();
        used[i] = false;
      }
    };
  
    backtrack([]);
    return results;
  }
  
  //  sampleProducts = ['0DfRwGErEjR1rBJyv7I3', '4ul7N7Ui4uMlklMsy70Z'];
  //  tests = this.generateTriangleTests(this.sampleProducts, 42);
  // tests.forEach(test => {
  //   console.log(`Pair: ${test.pair}`);
  //   console.log(`  Sample Codes: ${test.sampleCodes}`);
  //   console.log(`  Mapping:`, test.trueMapping);
  //   console.log(`  Odd sample at position ${test.oddSamplePosition + 1}`);
  //   console.log(`  Permutation ID: ${test.permutationId}`);
  //   console.log();
  // });
}
