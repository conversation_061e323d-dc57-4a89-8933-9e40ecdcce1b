import { Component, inject } from '@angular/core';
import { TemplateFormService } from '../../services/template-form.service';
import { FormGroup, ReactiveFormsModule } from '@angular/forms';
import { CommonModule } from '@angular/common';
import { ConstantsService } from '../../services/constants.service';
import { ProfileService } from '../../services/profile.service';

@Component({
  selector: 'new-template-step-6',
  standalone: true,
  imports: [ReactiveFormsModule, CommonModule],
  templateUrl: './new-template-step-6.component.html',
  styleUrl: './new-template-step-6.component.scss'
})
export class NewTemplateStep6Component {
  templateFormService = inject(TemplateFormService);
  constantsService = inject(ConstantsService);
  profileService = inject(ProfileService);
  formData: any;

  ngOnInit(): void {
    this.formData = this.getFormData();
    console.log('Template form data:', this.formData);
  }

  getFormData() {
    const formData = {
      ...this.templateFormService.step1Form.value,
      ...this.templateFormService.step2Form.value,
      ...this.templateFormService.step3Form.value,
      ...this.templateFormService.step4Form.value,
    };
    return formData;
  }

  getQuestions() {
    return this.formData.questions || [];
  }

  getQuestionName(questionId: string) {
    if (this.constantsService.questionsObject && this.constantsService.questionsObject[questionId]) {
      return this.constantsService.questionsObject[questionId].name || questionId;
    }
    const question = this.constantsService.questions.find((q: any) => q.id === questionId);
    return question ? question.name : questionId;
  }

  getConsumptionMethodName(methodId: string) {
    if (!methodId) return 'Not specified';

    const method = this.constantsService.consumption.find((m: any) => m.id === methodId);
    return method ? method.name : methodId;
  }

  getExperimentTypeName(typeId: string) {
    if (!typeId) return 'Normal Sensory';

    if (typeId === 'normal_sensory') return 'Normal Sensory';
    if (typeId === 'triangle_test') return 'Triangle Test';

    return typeId;
  }

  get startDate() {
    if(this.formData.startOption === 'now') {
      return 'Immediately';
    }
    return new Date(this.formData.start_date).toLocaleDateString();
  }

  get endDate() {
    if(this.formData.endOption === 'never') {
      return 'Ongoing, no set end date';
    }
    return new Date(this.formData.end_date).toLocaleDateString();
  }

  get restrictionDescription() {
    if(this.formData.panelistPool === 'public') {
      return 'Public panel, no restrictions';
    } else if(this.formData.panelistPool === 'private') {
      if(this.formData.restricted) {
        return 'Offer to invited only who have achieved level ' + this.formData.restrictionLevel;
      } else {
        return 'Offer to all eligible panelists on platform';
      }
    }
    return 'No restrictions';
  }

  editGeneral(event: any) {
    event.preventDefault();
    this.templateFormService.setFormStep(1);
  }

  editQuestions(event: any) {
    event.preventDefault();
    this.templateFormService.setFormStep(2);
  }

  editTargetPanelists(event: any) {
    event.preventDefault();
    this.templateFormService.setFormStep(3);
  }

  editBudget(event: any) {
    event.preventDefault();
    this.templateFormService.setFormStep(4);
  }
}
