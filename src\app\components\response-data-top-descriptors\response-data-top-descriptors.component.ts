import { Component, Input, inject } from '@angular/core';
import { CommonModule } from '@angular/common';
import { ResponseDataDescriptorBlockComponent } from "../response-data-descriptor-block/response-data-descriptor-block.component";
import { PanelsService } from '../../services/panels.service';
import { StatisticsService } from '../../services/statistics.service';
@Component({
  selector: 'response-data-top-descriptors',
  standalone: true,
  imports: [ResponseDataDescriptorBlockComponent, CommonModule],
  templateUrl: './response-data-top-descriptors.component.html',
  styleUrl: './response-data-top-descriptors.component.scss'
})
export class ResponseDataTopDescriptorsComponent {


  statisticsService = inject(StatisticsService);
  @Input() responseDetails: any;
  topDescriptors: any[] = [];
  panelsService = inject(PanelsService);
  async ngOnInit(){
    this.topDescriptors = await this.getTopDescriptors();
  }

  async getTopDescriptors(){
    // console.log('responseDetails', this.responseDetails);
    
    let descriptors = await this.panelsService.getResponseDescriptors(this.responseDetails);    
    let averages = descriptors.map((descriptor: any) => {
      return {
        ...descriptor,
        "avg": this.statisticsService.calculatedStats[descriptor.descriptor.option_id].shrinkageEstimate, 
      };
    });    
    let sorted = averages
    .sort((a:any,b:any) => {
      return b.avg - a.avg;
    })
    .slice(0, 3);

    //averages = this.statisticsService.calculatedStats;

    sorted.forEach((descriptor: any) => {
      descriptor.avg = Math.round(descriptor.avg);
    });
    // console.log('sorted', sorted);
    
    return sorted;
  }
}

