@import '../../../_colors.scss';

:host.form-body {
    display: block;
    

    .triangle-test-layout{
        display: flex;
        flex-direction: row;

        form {
            // flex:4;
            .form-step-title {
                margin-bottom: 20px;
                font-weight: bold;
                font-size: 1.2rem;
            }

            .tt-container {
                display: flex;
                gap: 30px;
                width: 100%;

                // Left side: Products section
                .products-section {
                    flex: 1;
                    max-width: 70%;

                    .search-filters {
                        display: flex;
                        justify-content: space-between;
                        margin-bottom: 15px;

                        .search-box {
                            position: relative;
                            width: 200px;

                            .search-input {
                                width: 100%;
                                padding: 8px 30px 8px 10px;
                                border: 1px solid #ddd;
                                border-radius: 4px;
                                font-size: 14px;
                            }

                            svg {
                                position: absolute;
                                right: 10px;
                                top: 50%;
                                transform: translateY(-50%);
                            }
                        }

                        .filter-buttons {
                            display: flex;
                            gap: 10px;

                            .filter-button {
                                background: none;
                                border: 1px solid #ddd;
                                border-radius: 4px;
                                padding: 8px;
                                cursor: pointer;
                                display: flex;
                                align-items: center;
                                justify-content: center;

                                &:hover {
                                    background-color: #f5f5f5;
                                }
                            }
                        }
                    }

                    .selected-count {
                        font-weight: bold;
                        margin-bottom: 20px;
                        padding: 8px 16px;
                        border-radius: 4px;
                        background-color: #f8f9fa;
                        display: inline-block;

                        &.error {
                            color: #dc3545;
                            background-color: #f8d7da;
                            border: 1px solid #f5c6cb;
                        }

                        &.complete {
                            color: #28a745;
                            background-color: #d4edda;
                            border: 1px solid #c3e6cb;
                        }
                    }

                    .product-grid {
                        display: grid;
                        grid-template-columns: repeat(3, 1fr);
                        gap: 15px;
                        margin-bottom: 20px;

                        .product-item {
                            .custom-radio-wrapper {
                                width: 300px;;
                                height: 87%;
                                display: flex;
                                align-items: center;
                                justify-content: center;
                                // border: 1px solid #ddd;
                                border-radius: 12px;
                                padding: 7px 10px;
                                cursor: pointer;
                                transition: all 0.2s ease;
                                position: relative;
                                background-color: $inputBackground;
                                outline: $inputBorder;
                                color: $appText;

                                &:focus{
                                    outline: $inputFocusBorder;
                                }



                                &.active {
                                    border: 1px solid $green;
                                    // background-color: $blue;
                                    // color: #fff;
                                    box-shadow: 0px 4px 10px rgba(0, 0, 0, 0.18);
                                                            }

                                .custom-radio {
                                    width: 20px;
                                    height: 20px;
                                    border-radius: 50%;
                                    border: 2px solid #ddd;
                                    margin-right: 10px;
                                    display: flex;
                                    align-items: center;
                                    justify-content: center;
                                    flex-shrink: 0;

                                    .radio-inner {
                                        width: 10px;
                                        height: 10px;
                                        border-radius: 50%;
                                        background-color: $green;
                                    }
                                }
                            }
                        }
                    }

                    .no-products {
                        text-align: center;
                        padding: 20px;
                        color: #666;
                        font-style: italic;
                    }
                }

            }
        }
        // Right side: Selected products section
        .selected-products-section {
            // flex:2;
            width: 300px;
            // border-left: 1px solid #eee;
            padding-left: 20px;
            padding-right:20px;

            .section-header {
                display: flex;
                align-items: center;
                margin-bottom: 15px;
                
                .section-title {
                    font-size: 16px;
                    font-weight: 600;
                    margin-bottom: 0;
                    color: #333;
                    margin-right: 8px;
                }
                
                .info-icon-wrapper {
                    position: relative;
                    
                    .info-icon {
                        display: flex;
                        align-items: center;
                        justify-content: center;
                        width: 18px;
                        height: 18px;
                        border-radius: 50%;
                        background-color: $blue;
                        color: white;
                        font-size: 12px;
                        font-weight: bold;
                        cursor: help;
                        font-style: italic;
                    }
                    
                    .tooltip-content {
                        position: absolute;
                        top: calc(100% + 5px);
                        left: 50%;
                        transform: translateX(-50%);
                        width: 250px;
                        padding: 10px;
                        background-color: #f0f7ff;
                        border-radius: 8px;
                        font-size: 13px;
                        color: #505050;
                        line-height: 1.4;
                        border-left: 3px solid $blue;
                        box-shadow: 0 3px 8px rgba(0, 0, 0, 0.1);
                        z-index: 100;
                        display: none;
                        
                        &::before {
                            content: '';
                            position: absolute;
                            top: -5px;
                            left: 50%;
                            transform: translateX(-50%);
                            width: 0;
                            height: 0;
                            border-left: 5px solid transparent;
                            border-right: 5px solid transparent;
                            border-bottom: 5px solid #f0f7ff;
                        }
                    }
                    
                    &:hover {
                        .tooltip-content {
                            display: block;
                        }
                    }
                }
            }

            .info-text {
                font-size: 13px;
                color: #505050;
                background-color: #f0f7ff;
                border-radius: 8px;
                padding: 10px;
                margin-bottom: 15px;
                line-height: 1.4;
                border-left: 3px solid $blue;
            }
            
            .seed-info {
                margin-top: 15px;
                padding: 10px;
                background-color: #f5f5f5;
                border-radius: 6px;
                font-size: 12px;
                color: #666;
                position: relative;
                border-left: 2px solid #ddd;
                
                .seed-label {
                    font-weight: 600;
                    margin-right: 5px;
                }
                
                .seed-value {
                    font-family: monospace;
                    background-color: #e9e9e9;
                    padding: 2px 4px;
                    border-radius: 3px;
                }
                
                .seed-tooltip {
                    font-style: italic;
                    margin-top: 5px;
                    font-size: 11px;
                    color: #888;
                }
                
                &:hover {
                    background-color: #efefef;
                }
            }

            .selected-product-item {
                margin-bottom: 25px;
                padding-bottom: 15px;
                border-bottom: 1px solid #eee;

                &:last-child {
                    border-bottom: none;
                }

                .selected-product-header {
                    margin-bottom: 10px;

                    .product-name {
                        display: block;
                        font-weight: 600;
                        font-size: 16px;
                        margin-bottom: 4px;
                    }

                    .product-type {
                        display: block;
                        font-size: 14px;
                        color: #666;
                    }
                }

                .sample-labels {
                    .sample-label-row {
                        display: flex;
                        flex-direction: column;
                        margin-bottom: 10px;

                        .label-text {
                            font-size: 14px;
                            color: #666;
                            margin-bottom: 8px;
                        }

                        .sample-badges {
                            display: flex;
                            align-items: center;
                            flex-wrap: wrap;
                            gap: 8px;
                            max-width: 100%;
                            margin-bottom: 5px;

                            .sample-badge {
                                background-color: #e83e8c;
                                color: white;
                                padding: 4px 8px;
                                border-radius: 4px;
                                font-size: 12px;
                                font-weight: 500;
                            }

                            .and-text {
                                font-size: 12px;
                                color: #666;
                                margin: 0 -4px;
                            }
                            
                            .no-samples {
                                font-style: italic;
                                color: #666;
                                font-size: 12px;
                            }
                        }
                    }
                }
            }

            .no-selection-message {
                color: #666;
                font-style: italic;
                text-align: center;
                padding: 20px 0;
            }
        }
    }

}