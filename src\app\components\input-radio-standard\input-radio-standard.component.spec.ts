import { ComponentFixture, TestBed } from '@angular/core/testing';

import { InputRadioStandardComponent } from './input-radio-standard.component';

describe('InputRadioStandardComponent', () => {
  let component: InputRadioStandardComponent;
  let fixture: ComponentFixture<InputRadioStandardComponent>;

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      imports: [InputRadioStandardComponent]
    })
    .compileComponents();

    fixture = TestBed.createComponent(InputRadioStandardComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
