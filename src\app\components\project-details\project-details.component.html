<a [routerLink]="['/projects']">
    <button-standard class="back-button">
        <svg xmlns="http://www.w3.org/2000/svg" width="16" height="13" viewBox="0 0 16 13" fill="none">
            <path d="M15.2134 5.54389H2.66023L6.93207 1.33322C7.24094 1.03018 7.24248 0.535256 6.93668 0.230712C6.63088 -0.0753749 6.13144 -0.0768952 5.82413 0.226144L0.198231 5.75837C0.161351 5.7934 0.129081 5.83299 0.0998831 5.87563V5.89847C0.0752961 5.9335 0.056854 5.97157 0.0399538 6.01116V6.03857C0.0399538 6.07816 0.0122934 6.11623 0.00461006 6.15582C-0.00153669 6.2076 -0.00153669 6.25938 0.00461006 6.31115V6.35074C0.00307347 6.38882 0.00307347 6.42537 0.00461006 6.46343C0.00461006 6.49846 0.0245871 6.53044 0.0322704 6.56089C0.035344 6.57764 0.035344 6.5944 0.0322704 6.61115L0.0829826 6.70404L0.106032 6.74363H0.10757C0.13523 6.78322 0.165964 6.82129 0.198234 6.85784L5.79935 12.4283C6.10976 12.7085 6.58766 12.6978 6.88424 12.4039C7.18082 12.11 7.19465 11.638 6.91344 11.3288L2.64907 7.10294H15.2132C15.6481 7.10294 16 6.75421 16 6.32325C16 5.8923 15.6482 5.54389 15.2134 5.54389Z" fill="black"/>
        </svg>
        <span>Back to Projects</span>
    </button-standard>
</a>

<div class="panel-name-row">
    <div class="panel-name-grid-area">
        <div class="panel-name">{{project.projectName}}</div>
    </div>
    <div class="buttons-grid-area">
        <div class="actions-button-container">
            <button-standard class="actions-button no-padding" (onAction)="toggleActionsPopover()">
                <svg xmlns="http://www.w3.org/2000/svg" width="39" height="39" viewBox="0 0 39 39" fill="none">
                    <path d="M21.5 20.1111C21.5 21.277 20.6045 22.2222 19.5 22.2222C18.3955 22.2222 17.5 21.277 17.5 20.1111C17.5 18.9452 18.3955 18 19.5 18C20.6045 18 21.5 18.9452 21.5 20.1111Z" fill="black"/>
                    <path d="M14 20.1111C14 21.277 13.1045 22.2222 12 22.2222C10.8955 22.2222 10 21.277 10 20.1111C10 18.9452 10.8955 18 12 18C13.1045 18 14 18.9452 14 20.1111Z" fill="black"/>
                    <path d="M29 20.1111C29 21.277 28.1045 22.2222 27 22.2222C25.8955 22.2222 25 21.277 25 20.1111C25 18.9452 25.8955 18 27 18C28.1045 18 29 18.9452 29 20.1111Z" fill="black"/>
                </svg>
            </button-standard>
            <popover *ngIf="showActionsPopover" [@fadeScale] #actionsPopover>
                <div class="popover-item" (click)="openEditDrawer()">Edit Project Configuration</div>
                <div class="popover-item" (click)="deleteProject()">Delete Project</div>
            </popover>
        </div>
    </div>
</div>

<div class="panel-summary-row">
    <div class="panel-summary-section">
        <div class="section-label">Number of Panels</div>
        <div class="section-content">{{project?.associatedPanelCount}}</div>
    </div>

    <div class="panel-summary-section response-count-section">
        <div class="section-label">Number of Responses</div>
        <div class="section-content">{{project?.responseCount}}</div>
        <!-- <button type="button" (click)="openResponsesDrawer()">View All</button> -->
    </div>

    <div class="panel-summary-section">
        <div class="section-label">Started</div>
        <div class="section-content">{{project?.startDate || 'undefined'}}</div>
    </div>

    <div class="panel-summary-section">
        <div class="section-label">End{{isEnded ? 'ed' : 's'}}</div>
        <div class="section-content">{{project?.endDate || 'undefined'}}</div>
    </div>

    <div class="panel-summary-section">
        <div class="section-label">Samples</div>
        <div class="section-content">
            <div class="sample-icon-container" *ngFor="let sample of project?.associatedSamples">
                <lamb-gp-icon [userInitials]="getInitialsFromString(sample.name)" [imageUrl]="sample.image"></lamb-gp-icon>
                <lamb-graph-tooltip [config]="{mainText: {content: sample.name}}"></lamb-graph-tooltip>
            </div>
        </div>
    </div>

    <div class="panel-summary-section">
        <div class="section-label">Collaborators</div>
        <div class="section-content">
            <div class="collaborator-icon-container" *ngFor="let collaborator of project?.collaborators">
                <lamb-gp-icon 
                    [userInitials]="getInitialsFromString(collaborator.first_name + ' ' + collaborator.last_name)"
                    (mouseenter)="showUserTooltip($event, collaborator)"
                    (mouseleave)="hideUserTooltip()">
                </lamb-gp-icon>
                <lamb-graph-tooltip 
                    [config]="{mainText: {content: collaborator.first_name + ' ' + collaborator.last_name}}">
                </lamb-graph-tooltip>
            </div>

            <button type="button" class="add-collaborator-btn" (click)="openCollaboratorsModal()" #modalButton><svg xmlns="http://www.w3.org/2000/svg" width="10" height="10" viewBox="0 0 10 10" fill="none">
                <path d="M4.11765 0V4.11765H0V5.88235H4.11765V10H5.88235V5.88235H10V4.11765H5.88235V0H4.11765Z" fill="#292727"/>
            </svg></button>

            <div class="collaborators-modal" [style.top]="modalOffset.y + 22 + 'px'" *ngIf="collaboratorsModalOpen" [@fadeScale] #collaboratorsModal>
                <lamb-table *ngIf="!loadingUsers" [headers]="usersHeaders" [data]="users" [styles]="userTableStyles"></lamb-table>
                <div *ngIf="loadingUsers" class="loading-spinner">
                    <svg class="spinner" viewBox="0 0 50 50">
                        <circle class="path" cx="25" cy="25" r="20" fill="none" stroke-width="5"></circle>
                    </svg>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="associated-panels-header">
    <div class="table-title">Associated Panels<span class="table-title-subtext">{{project?.associatedPanelCount}}</span></div>
    <div class="summary-and-actions">
        <div class="arrow-container">
            <svg xmlns="http://www.w3.org/2000/svg" width="16" height="28" viewBox="0 0 16 28" fill="none">
                <path d="M15.3536 24.3536C15.5488 24.1583 15.5488 23.8417 15.3536 23.6464L12.1716 20.4645C11.9763 20.2692 11.6597 20.2692 11.4645 20.4645C11.2692 20.6597 11.2692 20.9763 11.4645 21.1716L14.2929 24L11.4645 26.8284C11.2692 27.0237 11.2692 27.3403 11.4645 27.5355C11.6597 27.7308 11.9763 27.7308 12.1716 27.5355L15.3536 24.3536ZM0.5 0V18H1.5V0H0.5ZM7 24.5H15V23.5H7V24.5ZM0.5 18C0.5 21.5899 3.41015 24.5 7 24.5V23.5C3.96243 23.5 1.5 21.0376 1.5 18H0.5Z" fill="#C6C6C6"/>
            </svg>
        </div>
        <div class="summary-container">
            <div class="response-count">{{project?.responseCount}} Responses</div>
            <div class="privacy-summary">{{privacySummary}}</div>
        </div>
        <div class="add-panel-container">
            <button-standard class="add-panel-button" (click)="openAddPanelDrawer()"><svg xmlns="http://www.w3.org/2000/svg" width="10" height="10" viewBox="0 0 10 10" fill="none">
                <path d="M4.11765 0V4.11765H0V5.88235H4.11765V10H5.88235V5.88235H10V4.11765H5.88235V0H4.11765Z" fill="#292727"/>
              </svg><span>Add Panel to Project</span></button-standard>
        </div>
    </div>
</div>

<div class="associated-panels-table">
    <lamb-graph-tooltip 
        *ngIf="activeTooltipPanel"
        [style.position]="'fixed'"
        [style.left.px]="tooltipPosition.x"
        [style.top.px]="tooltipPosition.y"
        [config]="{mainText: {content: activeTooltipPanel?.productName || 'No sample name'}}"
        [@fadeInOut]
        #panelSampleTooltip>
    </lamb-graph-tooltip>
    <lamb-table [styles]="tableStyles" [headers]="panelsTableHeaders" [data]="panelsTableData"></lamb-table>
</div>

<!-- <div class="responses-drawer-overlay" *ngIf="responsesDrawerOpen" [@fadeInOut] #responsesDrawerOverlay (click)="closeResponsesDrawer()"></div> -->

<!-- <div class="responses-drawer" *ngIf="responsesDrawerOpen" [@slideInOut] #responsesDrawer> -->
    <!-- <button-standard (onAction)="closeResponsesDrawer()"><svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 14 14" fill="none"> -->
        <!-- <path d="M7 5.58549L12.2929 0.292606C12.683 -0.0975353 13.3164 -0.0975353 13.7074 0.292606C14.0975 0.683605 14.0975 1.31695 13.7074 1.70711L8.41451 7L13.7074 12.2929C14.0975 12.683 14.0975 13.3164 13.7074 13.7074C13.3164 14.0975 12.683 14.0975 12.2929 13.7074L7 8.41451L1.70711 13.7074C1.31697 14.0975 0.683627 14.0975 0.292606 13.7074C-0.0975353 13.3164 -0.0975353 12.683 0.292606 12.2929L5.58549 7L0.292606 1.70711C-0.0975353 1.31697 -0.0975353 0.683627 0.292606 0.292606C0.683605 -0.0975353 1.31695 -0.0975353 1.70711 0.292606L7 5.58549Z" fill="black"/> -->
        <!-- </svg><span>Close</span></button-standard> -->
    <!-- <div class="responses-drawer-header">All Responses</div> -->
    <!-- <div class="responses-drawer-content"> -->
        <!-- <lamb-table [styles]="responsesTableStyle" [headers]="responsesTableHeaders" [data]="responsesTableData"></lamb-table> -->
        <!-- <pagination [state]="responsesPaginationState"></pagination> -->
    <!-- </div> -->
<!-- </div> -->

<div class="panels-drawer-overlay" *ngIf="panelsDrawerOpen" [@fadeInOut] #panelsDrawerOverlay (click)="closePanelsDrawer()"></div>

<div class="panels-drawer" *ngIf="panelsDrawerOpen" [@slideInOut] #panelsDrawer>
    <button-standard (onAction)="closePanelsDrawer()"><svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 14 14" fill="none">
        <path d="M7 5.58549L12.2929 0.292606C12.683 -0.0975353 13.3164 -0.0975353 13.7074 0.292606C14.0975 0.683605 14.0975 1.31695 13.7074 1.70711L8.41451 7L13.7074 12.2929C14.0975 12.683 14.0975 13.3164 13.7074 13.7074C13.3164 14.0975 12.683 14.0975 12.2929 13.7074L7 8.41451L1.70711 13.7074C1.31697 14.0975 0.683627 14.0975 0.292606 13.7074C-0.0975353 13.3164 -0.0975353 12.683 0.292606 12.2929L5.58549 7L0.292606 1.70711C-0.0975353 1.31697 -0.0975353 0.683627 0.292606 0.292606C0.683605 -0.0975353 1.31695 -0.0975353 1.70711 0.292606L7 5.58549Z" fill="black"/>
        </svg><span>Close</span></button-standard>
    <div class="panels-drawer-header">All Panels</div>
    <div class="panels-drawer-content">
        <lamb-table [styles]="panelsDrawerTableStyle" [headers]="allPanelsTableHeaders" [data]="panelsDrawerTableData"></lamb-table>
        <button
            *ngIf="panelsState.hasMore && !isLoadingMorePanels"
            (click)="loadMorePanels()"
            class="load-more-btn">
                Load More Panels
        </button>
        <div *ngIf="isLoadingMorePanels" class="loading-spinner">
            <svg class="spinner" viewBox="0 0 50 50">
                <circle class="path" cx="25" cy="25" r="20" fill="none" stroke-width="5"></circle>
            </svg>
        </div>
    </div>
</div>

<div class="edit-drawer-overlay" *ngIf="editDrawerOpen" [@fadeInOut] #editDrawerOverlay (click)="closeEditDrawer()"></div>

<div class="edit-drawer" *ngIf="editDrawerOpen" [@slideInOut] #editDrawer>
    <button-standard class="exit-button" (onAction)="closeEditDrawer()"><svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 14 14" fill="none">
        <path d="M7 5.58549L12.2929 0.292606C12.683 -0.0975353 13.3164 -0.0975353 13.7074 0.292606C14.0975 0.683605 14.0975 1.31695 13.7074 1.70711L8.41451 7L13.7074 12.2929C14.0975 12.683 14.0975 13.3164 13.7074 13.7074C13.3164 14.0975 12.683 14.0975 12.2929 13.7074L7 8.41451L1.70711 13.7074C1.31697 14.0975 0.683627 14.0975 0.292606 13.7074C-0.0975353 13.3164 -0.0975353 12.683 0.292606 12.2929L5.58549 7L0.292606 1.70711C-0.0975353 1.31697 -0.0975353 0.683627 0.292606 0.292606C0.683605 -0.0975353 1.31695 -0.0975353 1.70711 0.292606L7 5.58549Z" fill="black"/>
        </svg><span>Close</span></button-standard>
    <div class="edit-drawer-header">
        <span>Edit Project</span>
        <button-standard class="save-button {{buttonState === 1 || buttonState === 2 ? 'green' : ''}}" 
            (onAction)="saveProjectChanges()">
            {{buttonText[buttonState]}}
        </button-standard>
    </div>
    <div class="edit-drawer-content">
        <form [formGroup]="editProjectForm">
            <div class="input-container name" [class.error-shake]="formErrors.projectName">
                <label for="project-name">Project Name</label>
                <input-text [control]="projectNameControl" [id]="'project-name'" [placeholder]="'project name'"></input-text>
                <!-- <div class="error-message" *ngIf="formErrors.projectName">{{errorMessage}}</div> -->
            </div>
        
            <div class="input-container name" [class.error-shake]="formErrors.description">
                <label for="project-description">Description</label>
                <input-text [control]="descriptionControl" [id]="'project-description'" [placeholder]="'project description'"></input-text>
                <!-- <div class="error-message" *ngIf="formErrors.description">{{errorMessage}}</div> -->
            </div>
                
            <div class="input-container date-picker-container standalone-datepicker" [class.error-shake]="formErrors.startDate">
                <label>Start</label>
                <input-bs-datepicker [control]="startDateControl"></input-bs-datepicker>
                <!-- <div class="error-message" *ngIf="formErrors.startDate">{{errorMessage}}</div> -->
            </div>
        
            <div class="input-container radio-container">
                <label>End</label>
                <input-radio-block [control]="endOptionControl" [id]="'end-option-control-never'" [value]="'never'" [name]="'end-option-control'" [class.active]="editProjectForm.get('endOption')?.value === 'never'">No End Date</input-radio-block>
                <input-radio-block [control]="endOptionControl" [id]="'end-option-control-custom'" [value]="'custom'" [name]="'end-option-control'" [class.active]="editProjectForm.get('endOption')?.value === 'custom'">Custom End Date</input-radio-block>
            </div>
        
            <div class="input-container date-picker-container" *ngIf="editProjectForm.get('endOption')?.value === 'custom'" [class.error-shake]="formErrors.endDate">
                <svg xmlns="http://www.w3.org/2000/svg" width="18" height="34" viewBox="0 0 18 34" fill="none">
                    <path d="M17.3536 30.3536C17.5488 30.1583 17.5488 29.8417 17.3536 29.6464L14.1716 26.4645C13.9763 26.2692 13.6597 26.2692 13.4645 26.4645C13.2692 26.6597 13.2692 26.9763 13.4645 27.1716L16.2929 30L13.4645 32.8284C13.2692 33.0237 13.2692 33.3403 13.4645 33.5355C13.6597 33.7308 13.9763 33.7308 14.1716 33.5355L17.3536 30.3536ZM0.5 0V20H1.5V0H0.5ZM11 30.5H17V29.5H11V30.5ZM0.5 20C0.5 25.799 5.20101 30.5 11 30.5V29.5C5.75329 29.5 1.5 25.2467 1.5 20H0.5Z" fill="#9D9D9D"/>
                </svg>
                <input-bs-datepicker [control]="endDateControl"></input-bs-datepicker>
                <!-- <div class="error-message" *ngIf="formErrors.endDate">{{errorMessage}}</div> -->
            </div>
        </form>
    </div>
</div>

<ng-template #viewResponsesButtonCell let-panel>
<button-standard class="view-responses-button" (onAction)="openResponseDetails(panel)"><svg xmlns="http://www.w3.org/2000/svg" width="12" height="11" viewBox="0 0 12 11" fill="none">
        <path d="M0 0H3.20026V11H0V0Z" fill="#292727"/>
        <path d="M4.40026 2.0373H7.60051V11H4.40026V2.0373Z" fill="#292727"/>
        <path d="M8.79974 3.66667H12V11H8.79974V3.66667Z" fill="#292727"/>
      </svg><span>View Responses</span></button-standard>
</ng-template>

<ng-template #selectCellTemplate let-item>
    <div class="select-cell">
        <!-- <input-radio-standard [control]="item.selectControl" [id]="item.selectId" [value]="item.selectValue" [name]="item.selectName"></input-radio-standard> -->
        <input type="checkbox" (change)="togglePanelSelection(item)" [checked]="isPanelSelected(item)">
    </div>
</ng-template>


<ng-template #selectUserCellTemplate let-user>
    <div class="select-cell">
        <input type="checkbox" (change)="toggleUserSelection(user)" [checked]="isUserSelected(user)" [disabled]="user.loading">
        <!-- <div *ngIf="user.loading" class="checkbox-spinner">
            <svg class="spinner small" viewBox="0 0 50 50">
                <circle class="path" cx="25" cy="25" r="20" fill="none" stroke-width="5"></circle>
            </svg>
        </div> -->
    </div>
</ng-template>

<ng-template #panelSampleCellTemplate let-panel>
    <div class="panel-sample-cell" 
         (mouseenter)="showPanelSampleTooltip($event, panel)"
         (mouseleave)="hidePanelSampleTooltip()">
        <lamb-gp-icon [userInitials]="getInitialsFromString(panel?.productName, 1)" [imageUrl]="panel.productImage"></lamb-gp-icon>
    </div>
</ng-template>

<ng-template #panelistCellTemplate let-response>
    <div class="panelist-cell">
        <lamb-gp-icon [userInitials]="getInitialsFromString(response.panelist)"></lamb-gp-icon>
        <div class="panelist-name">{{response.panelist}}</div>
    </div>
</ng-template>

<!-- <lamb-graph-tooltip 
    *ngIf="activeTooltipUser"
    [style.position]="'absolute'"
    [style.left.px]="tooltipPosition.x"
    [style.top.px]="tooltipPosition.y"
    [config]="{mainText: {content: activeTooltipText}}"
    [@fadeInOut]
    #userTooltip>
</lamb-graph-tooltip> -->