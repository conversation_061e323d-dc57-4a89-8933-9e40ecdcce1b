<!-- <div class="custom-step-list-popover-container"> -->
  <div class="popover-header">
    <button type="button" (click)="back.emit()">
        <svg xmlns="http://www.w3.org/2000/svg" width="16" height="13" viewBox="0 0 16 13" fill="none">
            <path d="M15.2134 5.54389H2.66023L6.93207 1.33322C7.24094 1.03018 7.24248 0.535256 6.93668 0.230712C6.63088 -0.0753749 6.13144 -0.0768952 5.82413 0.226144L0.198231 5.75837C0.161351 5.7934 0.129081 5.83299 0.0998831 5.87563V5.89847C0.0752961 5.9335 0.056854 5.97157 0.0399538 6.01116V6.03857C0.0399538 6.07816 0.0122934 6.11623 0.00461006 6.15582C-0.00153669 6.2076 -0.00153669 6.25938 0.00461006 6.31115V6.35074C0.00307347 6.38882 0.00307347 6.42537 0.00461006 6.46343C0.00461006 6.49846 0.0245871 6.53044 0.0322704 6.56089C0.035344 6.57764 0.035344 6.5944 0.0322704 6.61115L0.0829826 6.70404L0.106032 6.74363H0.10757C0.13523 6.78322 0.165964 6.82129 0.198234 6.85784L5.79935 12.4283C6.10976 12.7085 6.58766 12.6978 6.88424 12.4039C7.18082 12.11 7.19465 11.638 6.91344 11.3288L2.64907 7.10294H15.2132C15.6481 7.10294 16 6.75421 16 6.32325C16 5.8923 15.6482 5.54389 15.2134 5.54389Z" fill="#292727"/>
        </svg>
    </button>
    <span>Use a Previously Created Step</span>
  </div>
  <div class="search-row">
    <input-text
      class="search-input"
      [control]="searchControl"
      placeholder="Search..."
      type="text"
      id="step-search"
      inputMode="text"
      aria-label="Search steps"
    ></input-text>
  </div>
  <lamb-table
    [headers]="tableHeaders"
    [data]="tableData"
    [styles]="tableStyles"
    [clickableRows]="true"
    (rowClick)="onRowClick($event)"
  >
  </lamb-table>
<!-- </div>  -->

<ng-template #viewButtonTemplate let-step>
    <button class="view-step-btn" type="button" (click)="viewStep.emit(step); $event.stopPropagation()">
        <svg xmlns="http://www.w3.org/2000/svg" width="18" height="10" viewBox="0 0 18 10" fill="none">
            <path d="M8.63636 0C5.13738 0 2.03155 1.83283 0.105733 4.65002C-0.0352442 4.85784 -0.0352442 5.13596 0.105733 5.34378C2.03135 8.16099 5.13738 10 8.63636 10C12.1353 10 15.2412 8.16093 17.167 5.34378C17.308 5.13596 17.308 4.85784 17.167 4.65002C15.2414 1.83281 12.1353 0 8.63636 0ZM8.63636 1.2C11.6 1.2 14.2266 2.68283 15.9611 5.00002C14.2266 7.31483 11.5982 8.80003 8.63636 8.80003C5.6745 8.80003 3.04609 7.31487 1.31167 5.00002C3.04613 2.68281 5.67297 1.2 8.63636 1.2ZM8.63636 2.00001C7.05264 2.00001 5.75686 3.35001 5.75686 5.00002C5.75686 6.65003 7.05264 8.00003 8.63636 8.00003C10.2201 8.00003 11.5159 6.65003 11.5159 5.00002C11.5159 3.35001 10.2201 2.00001 8.63636 2.00001ZM8.63636 3.20001C9.5977 3.20001 10.3641 3.99846 10.3641 5.00002C10.3641 6.00158 9.5977 6.80003 8.63636 6.80003C7.67503 6.80003 6.90866 6.00158 6.90866 5.00002C6.90866 3.99846 7.67503 3.20001 8.63636 3.20001Z" fill="#292727"/>
        </svg>
    </button>
</ng-template>