import { inject, Injectable } from '@angular/core';
import {
  BehaviorSubject,
  Observable,
  catchError,
  firstValueFrom,
  of,
  switchMap,
} from 'rxjs';
import { ApiService } from './api.service';
import { Template } from '../interfaces/template';
import { PaginationParams } from '../interfaces/paginationParams';

@Injectable({
  providedIn: 'root',
})
export class TemplatesService {
  apiService = inject(ApiService);

  constructor() {}

  private pageSubject = new BehaviorSubject<number>(1);
  private limitSubject = new BehaviorSubject<number>(10);
  public templates$ = this.createTemplatesStream();


  page: number = 1;
  limit: number = 10;

  createTemplatesStream():Observable<any> {
    return this.pageSubject.pipe(
      switchMap((page) =>
         this.getTemplates({
          page,
          limit: this.limitSubject.getValue(),
        }).pipe(
          catchError((error) => {
            console.error('Error fetching templates:', error);
            return of({ templates: [], total: 0 });
          })
        )
      )
    );
  }

  setPage(page: number): void {
    this.page = page;
    this.pageSubject.next(page);
  }

  setLimit(limit: number): void {
    this.limit = limit;
    this.limitSubject.next(limit);
    // Reset to first page when changing limit
    this.setPage(1);
  }

  getTemplates(params: PaginationParams = { page: 1, limit: 10 } ): Observable<any> {
    return this.apiService.getTemplates(params);
  }

  async getTemplateById(id: string): Promise<Template> {
    const response = await this.apiService.getTemplateById(id);
    return response as Template;
  }

  updatePanelTemplate(id:string,templateData: any) {
    return this.apiService.updateTemplate(id,templateData);
  }

  removePanelTemplate(id: string) {
    return this.apiService.deleteTemplate(id);
  }

  createPanelTemplate(templateData: any) {
    return this.apiService.createTemplate(templateData);
  }

  getQuestionById(id: string): Observable<any> {
    return this.apiService.getQuestionById(id);
  }

  async getQuestionByIdAsync(id: string): Promise<any> {
    const response = await firstValueFrom(this.apiService.getQuestionById(id));
    return response;
  }
}
