@import '../../../_colors';

:host {
    display: flex;
    border: none;
    background-color: $inputBackground;
    outline: $inputBorder;
    width: fit-content;
    height: 180px;
    width: 180px;
    border-radius:10px;
    font-size: 1rem;
    color: $appText;
    justify-content: center;
    align-items: center;
    position: relative;

    &:focus{
        outline: $inputFocusBorder;
    }

    input{
        appearance: none;
        -webkit-appearance: none;
        -moz-appearance: none;
        border: none;
        outline: none;
        background: none;
        padding: 0;
        margin: 0;
        border-radius: 0;
        font-family: inherit;
        font-size: inherit;
        color: inherit;
        box-sizing: border-box;
        width: 0;
        height: 0;
        opacity: 0;
        position: absolute;
        z-index: -1;
    }

    .remove-photo{
        position: absolute;
        top: 15px;
        left: 10px;
        z-index: 11;
        background-color: #fff;
        border-radius: 30px;
        padding: 5px;
        box-shadow: 0px 2px 8px rgba(0,0,0,0.25);
        display: flex;
        align-items: center;
        justify-content: center;
        flex-flow: row nowrap;

        svg{
            fill: #6d6d6d;
            opacity: .5;
            width: 9px;
            height: 9px;
            display: inline-block;
            vertical-align: middle;
            transition: opacity 200ms;
        }

        span{
            color: #6d6d6d;
            opacity: .7;
            font-size: .6rem;
            line-height: .5rem;
            font-weight: 700;
            display: inline-block;
            vertical-align: middle;
            transition: opacity 200ms;
            margin-left:5px;
        }

        &:hover{
            cursor: pointer;

            svg{
                opacity: 1;
            }

            span{
                opacity: 1;
            }
        }
    }

    label{
        width: 100%;
        height: 100%;
        display: flex;
        align-items: center;
        justify-content: center;
        flex-flow: column nowrap;
        position: absolute;
        top: 0;
        left: 0;
        z-index: 10;
        gap: 10px;
        cursor: pointer;
        overflow: hidden;

        .image-preview {
            max-width: 100%;
            object-fit: contain;
            border-radius: 8px;
            width: inherit;
            height: inherit;
        }

        &.dragging {
            border-color: #2196F3;
            background-color: rgba(33, 150, 243, 0.1);
        }

        .drop-text{
            font-size: .75rem;
            color: #6d6d6d;
            opacity: .7;
            font-weight: 700;
            width: 120px;
            text-align: center;
        }

        .choose-text{
            font-size: .6875rem;
            color: #6d6d6d;
            opacity: .7;
            font-weight: 500;
            text-decoration: underline;
        }
    }

   
}
