<div class="response-details-header tt-response-details-header">
    <svg
      width="18"
      height="20"
      viewBox="0 0 12 13"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M8.45242 6.88591C9.19977 6.21706 9.64274 5.27345 9.68039 4.27138C9.71723 3.26931 9.34556 2.29527 8.64948 1.5736C7.9542 0.851882 6.99458 0.443359 5.99168 0.443359C4.98879 0.443359 4.02919 0.851882 3.33389 1.5736C2.63781 2.29531 2.26613 3.26935 2.30298 4.27138C2.34063 5.27345 2.7836 6.21706 3.53095 6.88591C2.50005 7.24717 1.6069 7.91924 0.974041 8.80916C0.342042 9.69991 0.00159948 10.7645 0 11.8562C0 12.2054 0.282759 12.4882 0.631199 12.4882C0.980439 12.4882 1.2632 12.2054 1.2632 11.8562C1.264 10.7932 1.68694 9.77356 2.4391 9.0214C3.19127 8.26923 4.21015 7.8463 5.27387 7.8455H6.72531C7.78907 7.8463 8.80873 8.26923 9.5609 9.0214C10.3123 9.77356 10.7352 10.7933 10.7368 11.8562C10.7368 12.2054 11.0196 12.4882 11.368 12.4882C11.7172 12.4882 12 12.2054 12 11.8562C11.9968 10.762 11.654 9.69585 11.0187 8.80588C10.3827 7.91514 9.48638 7.24389 8.45219 6.88583L8.45242 6.88591ZM5.99168 1.73846C6.96892 1.73846 7.84925 2.32722 8.22336 3.22996C8.59744 4.1327 8.39078 5.17164 7.6995 5.86212C7.00901 6.55341 5.97009 6.76007 5.06733 6.38599C4.16459 6.01192 3.57584 5.13159 3.57584 4.15431C3.57584 2.82061 4.65723 1.73846 5.99168 1.73846Z"
        fill="black"
      />
    </svg>

    <div class="header-label">Sample vs. Sample Results</div>
</div>
    
    <div *ngIf="triangleTestAggregates" class="triangle-test-summary">
      <span>Overall Accuracy: {{triangleTestAggregates.overallAccuracy}}%</span>
      <span>({{triangleTestAggregates.correctCount}} / {{triangleTestAggregates.totalResponses}} correct)</span>
    </div>
    
    <div class="border-line"></div>

    <div
    class="tt-response-details-section"
    *ngFor="let test of summerizedTTData | keyvalue"
  >
    <div class="tt-details-header">
      <div class="tt-product-names">
        <ng-container
          *ngFor="let productName of getTriangleTestProducts(test.key)"
        >
          <div class="divider-block tt-divider-block">
            <span class="name-initial">
              {{ getInitial(productName) }}
            </span>
          </div>
          <div>{{ productName }}</div>
        </ng-container>
      </div>
      
      <div class="p-value" [ngClass]="{'significant': test.value.isSignificant}">
        <svg
          width="12"
          height="14"
          viewBox="0 0 12 14"
          fill="none"
          xmlns="http://www.w3.org/2000/svg"
        >
          <path
            d="M11.0001 8.03943H6.51325L7.9498 5.96331H11C11.5527 5.96331 11.9999 5.49829 11.9999 4.92449C11.9999 4.35143 11.5527 3.8864 11 3.8864H9.38629L10.9375 1.64452C11.2602 1.17876 11.1575 0.529786 10.7089 0.195429C10.2603 -0.139657 9.63522 -0.0330758 9.31317 0.43268L6.92393 3.88632H0.999887C0.44721 3.88632 0 4.35135 0 4.92442C0 5.49821 0.44721 5.96324 0.999887 5.96324H5.48675L4.0502 8.03935H0.999977C0.4473 8.03935 8.99543e-05 8.50438 8.99543e-05 9.07745C8.99543e-05 9.65124 0.4473 10.1155 0.999977 10.1155H2.61371L1.06255 12.3582C0.741919 12.8239 0.845279 13.4707 1.29247 13.8051C1.74038 14.1387 2.36407 14.0336 2.68683 13.57L5.07607 10.1164H11.0001C11.5528 10.1164 12 9.65133 12 9.07754C12 8.50447 11.5528 8.03943 11.0001 8.03943Z"
            fill="#11295F"
          />
        </svg>

        <span>p-value {{ formatPValue(test.value.pValue) }}</span>
        <span *ngIf="test.value.isSignificant" class="significance-badge">Significant</span>
      </div>

      <svg
        width="11"
        height="6"
        viewBox="0 0 11 6"
        fill="none"
        xmlns="http://www.w3.org/2000/svg"
        *ngIf="!test.value.expanded"
        (click)="expandForDetails(test.key)"
        class="expand-icon"
      >
        <path
          d="M5.50008 6L0 1.35316L1.00236 0L5.5 3.80196L9.99765 0L11 1.35316L5.50008 6Z"
          fill="black"
        />
      </svg>

      <svg
        width="11"
        height="6"
        viewBox="0 0 11 6"
        fill="none"
        xmlns="http://www.w3.org/2000/svg"
        *ngIf="test.value.expanded"
        (click)="expandForDetails(test.key)"
        class="expand-icon"
      >
        <path
          d="M5.49992 0L11 4.64684L9.99764 6L5.5 2.19804L1.00235 6L0 4.64684L5.49992 0Z"
          fill="black"
        />
      </svg>
    </div>

    <div class="tt-data-details" *ngIf="test.value.expanded">
      <div class="tt-data-description">
        <div>
          <svg
            width="12"
            height="14"
            viewBox="0 0 12 14"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
          >
            <path
              d="M11.0001 8.03943H6.51325L7.9498 5.96331H11C11.5527 5.96331 11.9999 5.49829 11.9999 4.92449C11.9999 4.35143 11.5527 3.8864 11 3.8864H9.38629L10.9375 1.64452C11.2602 1.17876 11.1575 0.529786 10.7089 0.195429C10.2603 -0.139657 9.63522 -0.0330758 9.31317 0.43268L6.92393 3.88632H0.999887C0.44721 3.88632 0 4.35135 0 4.92442C0 5.49821 0.44721 5.96324 0.999887 5.96324H5.48675L4.0502 8.03935H0.999977C0.4473 8.03935 8.99543e-05 8.50438 8.99543e-05 9.07745C8.99543e-05 9.65124 0.4473 10.1155 0.999977 10.1155H2.61371L1.06255 12.3582C0.741919 12.8239 0.845279 13.4707 1.29247 13.8051C1.74038 14.1387 2.36407 14.0336 2.68683 13.57L5.07607 10.1164H11.0001C11.5528 10.1164 12 9.65133 12 9.07754C12 8.50447 11.5528 8.03943 11.0001 8.03943Z"
              fill="#11295F"
            />
          </svg>

          <p *ngIf="test.value.isSignificant" class="significant-result">
            <strong>Statistically significant difference detected</strong> between samples (p-value {{ formatPValue(test.value.pValue) }})
          </p>
          <p *ngIf="!test.value.isSignificant">
            No statistically significant difference between samples detected (p-value {{ formatPValue(test.value.pValue) }})
          </p>
        </div>

        <div class="tt-stats">
          <div class="tt-stat-item">
            <span class="stat-label">Chi-squared value:</span>
            <span class="stat-value">{{ test.value.chiSquared?.toFixed(3) || 'N/A' }}</span>
          </div>
          <div class="tt-stat-item">
            <span class="stat-label">p-value:</span>
            <span class="stat-value">{{ formatPValue(test.value.pValue) }}</span>
          </div>
          <div class="tt-stat-item">
            <span class="stat-label">Degrees of freedom:</span>
            <span class="stat-value">{{ test.value.degreesOfFreedom }}</span>
          </div>
          <div class="tt-stat-item">
            <span class="stat-label">Statistical inference:</span>
            <span class="stat-value">
              {{ test.value.isSignificant ? 'Reject null hypothesis' : 'Fail to reject null hypothesis' }}
            </span>
          </div>
        </div>
      </div>

      <div class="tt-data-breakdown">
        <div class="breakdown-container">
          <div class="donut-chart-container">
            <div
              class="donut-chart"
              [style.background]="
                getDonutGradient(test.value.correct, test.value.count)
              "
            ></div>
          </div>

          <table class="response-breakdown-table">
            <thead>
              <tr>
                <th>Response Breakdown</th>
                <th>Correct Responses</th>
                <th>Total Responses</th>
              </tr>
            </thead>
            <tbody>
              <tr>
                <td>
                  <div class="donut-chart-container">
                    <div class="donut-legend">
                      <div class="legend-item">
                        <div class="color-dot incorrect"></div>
                        <div class="legend-text">Incorrect</div>
                        <div class="legend-percentage">
                          {{
                            getIncorrectPercentage(
                              test.value.correct,
                              test.value.count
                            )
                          }}%
                        </div>
                      </div>
                      <div class="legend-item">
                        <div class="color-dot correct"></div>
                        <div class="legend-text">Correct</div>
                        <div class="legend-percentage">
                          {{
                            getCorrectPercentage(
                              test.value.correct,
                              test.value.count
                            )
                          }}%
                        </div>
                      </div>
                    </div>
                  </div>
                </td>
                <td>
                  <div class="stat-value">{{ test.value.correct }}</div>
                </td>
                <td>
                  <div class="stat-value">{{ test.value.count }}</div>
                </td>
              </tr>
            </tbody>
          </table>
        </div>
      </div>
    </div>
  </div>
  