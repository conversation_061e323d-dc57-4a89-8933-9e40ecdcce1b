import { ProjectInterface } from "../interfaces/project.interface";
import { PanelInterface } from "../interfaces/panel.interface";

export class ProjectDetailsAdapter {
    async adaptDetails(project: any): Promise<ProjectInterface> {
        return {
            id: project.project_id || '',
            projectName: project.name || '',            
            associatedPanels: project.panels || [],
            associatedPanelCount: project.assigned_panels?.length || 0,
            collaborators: project.collaborator_details || project.collaborators || [],
            associatedSamples: project.samples || [],
            associatedSampleCount: project.assigned_samples?.length || 0,
            associatedGroups: project.assigned_groups || [],
            associatedGroupCount: project.assigned_groups?.length || 0,
            status: project.status || '',
            privacy: project.is_public ? 'Public' : 'Private',
            completionPercentage: (project.completion_percentage || 0) + "%",
            responseCount: project.total_responses || 0,
            owner: this.getOwnerName(project.owner_details),
            ownerInitials: this.getOwnerInitials(project.owner_details),
            startDate: this.getDateFromTimestamp(project.start_date),
            endDate: this.getDateFromTimestamp(project.end_date),
        };
    }

    getOwnerName(ownerDetails: any): string {
        if (!ownerDetails) return '';
        const firstName = ownerDetails.first_name || '';
        const lastName = ownerDetails.last_name || '';
        return firstName + (firstName && lastName ? ' ' : '') + lastName;
    }

    getOwnerInitials(ownerDetails: any): string {
        if (!ownerDetails) return '';
        const firstName = ownerDetails.first_name || '';
        const lastName = ownerDetails.last_name || '';
        return (firstName.charAt(0) || '') + (lastName.charAt(0) || '');
    }

    getDateFromTimestamp(timestamp: unknown){
        if (!timestamp) return '';
        
        try {
            if (typeof timestamp === 'object' && timestamp !== null && '_seconds' in timestamp && typeof timestamp['_seconds'] === 'number') {
                const _seconds = timestamp['_seconds'];
                return new Date(_seconds * 1000).toLocaleDateString();
            } else if (typeof timestamp === 'number') {
                return new Date(timestamp).toLocaleDateString();
            } else if (typeof timestamp === 'string') {
                const date = new Date(timestamp);
                if (!isNaN(date.getTime())) {
                    return date.toLocaleDateString();
                }
            }
            return '';
        } catch (error) {
            console.error('Error formatting date:', error, timestamp);
            return '';
        }
    }
}

export class ProjectAdapter extends ProjectDetailsAdapter {
    async adapt(data: any): Promise<ProjectInterface[]> {
        return await Promise.all(data.map((project:any) => super.adaptDetails(project)));
    }
}