export interface TriangleTestData {
  results: any[];
  expanded: boolean;
  count: number;
  correct: number;
  incorrect: number;
  pValue: number | null;
  chiSquared: number | null;
  degreesOfFreedom: number;
  isSignificant: boolean;
}

export interface TriangleTestAggregate {
  panelId: string;
  totalTests: number;
  totalResponses: number;
  correctCount: number;
  overallAccuracy: number;
  productPairs: {
    [key: string]: {
      totalResponses: number;
      correctResponses: number;
      incorrectResponses: number;
      correctPercentage: number;
      expectedCorrect: number;
      expectedIncorrect: number;
      chiSquared: number;
      degreesOfFreedom: number;
      products: string[];
      pValue?: number;
      isSignificant?: boolean;
    };
  };
}
