import { Pipe, PipeTransform } from '@angular/core';

@Pipe({
  name: 'digitToWord',
  standalone: true
})
export class DigitToWordPipe implements PipeTransform {

  private words: { [key: number]: string } = {
    0: 'zero',
    1: 'one',
    2: 'two',
    3: 'three',
    4: 'four',
    5: 'five',
    6: 'six',
    7: 'seven',
    8: 'eight',
    9: 'nine',
    // extend as needed
  };

  transform(value: number, capitalize: boolean = false): string {
    if(capitalize){
      return this.words[value].charAt(0).toUpperCase() + this.words[value].slice(1);
    }
    return this.words[value] || value.toString();
  }

}
