import { Component, OnInit, Input } from '@angular/core';
import { CommonModule } from '@angular/common';
import { PercentageBarComponent } from '../percentage-bar/percentage-bar.component';
import { PanelStep } from '../../interfaces/panel-step.interface';

interface BarPiece {
  value: number;
  color?: string;
}

@Component({
  selector: 'response-data-true-false',
  standalone: true,
  imports: [CommonModule, PercentageBarComponent],
  templateUrl: './response-data-true-false.component.html',
  styleUrl: './response-data-true-false.component.scss'
})
export class ResponseDataTrueFalseComponent implements OnInit {
  truePercentage = 0;
  falsePercentage = 0;
  barPieces: BarPiece[] = [];

  @Input() responseDetails: any = null;
  @Input() step: PanelStep | null = null;

  ngOnInit(): void {
    // console.log('responseDetails', this.responseDetails, this.step);
    
    // Create the bar pieces with dummy data
    let trueCount = 0;
    let falseCount = 0;
    if(this.responseDetails && this.responseDetails.hasOwnProperty(this.step?.id + '_true_count')) {
      trueCount = this.responseDetails[this.step?.id + '_true_count'];
    }
    if(this.responseDetails && this.responseDetails.hasOwnProperty(this.step?.id + '_false_count')) {
      falseCount = this.responseDetails[this.step?.id + '_false_count'];
    }
    const totalCount = trueCount + falseCount;
    this.truePercentage = Math.round((trueCount / totalCount) * 100);
    this.falsePercentage = Math.round((falseCount / totalCount) * 100);
    this.barPieces = [
      { value: this.truePercentage, color: '#7CBB7D' },  // Green for true
      { value: this.falsePercentage, color: '#C97F7F' }  // Red for false
    ];
  }
}
