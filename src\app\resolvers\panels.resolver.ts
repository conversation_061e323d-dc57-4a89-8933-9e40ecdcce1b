import { inject } from '@angular/core';
import { ResolveFn } from '@angular/router';
import { PanelsService  } from '../services/panels.service';
import { LoadingService } from '../services/loading.service';
import { firstValueFrom } from 'rxjs';
import { finalize } from 'rxjs/operators';

export const panelsResolver: ResolveFn<any> = (route, state) => {
    const panelsService = inject(PanelsService);
    const loadingService = inject(LoadingService);
    const page = Number(route.queryParams['page']) || 1;
    const limit = Number(route.queryParams['limit']) || 10;
    console.log(page, limit);

    loadingService.setLoading('panels', true);

    return firstValueFrom(panelsService.getPanels({page, limit})
    .pipe(
        finalize(() => {loadingService.setLoading('panels', false)})
    ));
};

export const panelDetailsResolver: ResolveFn<any> = (route, state) => {
    const panelsService = inject(PanelsService);
    const loadingService = inject(LoadingService);
    const panelId = route.params['id'];
    loadingService.setLoading('panels', true);
    // if(panelsService.panelsObject.hasOwnProperty(panelId)){
    //     return panelsService.panelsObject[panelId];
    // }
    return panelsService.getPanel(panelId).then(panel => {
        loadingService.setLoading('panels', false);
        return panel;
    });
};

export const panelInsightsResolver: ResolveFn<any> = (route, state) => {
    const panelsService = inject(PanelsService);
    const loadingService = inject(LoadingService);
    const panelId = route.params['id'];
    loadingService.setLoading('panels', true);
    return panelsService.getPanelInsights(panelId).then(insights => {
        loadingService.setLoading('panels', false);
        return insights;
    });
};

export const panelCommentsResolver: ResolveFn<any> = (route, state) => {
    const panelsService = inject(PanelsService);
    const loadingService = inject(LoadingService);
    const panelId = route.params['id'];
    loadingService.setLoading('panels', true);
    return panelsService.getPanelComments(panelId).then(comments => {
        loadingService.setLoading('panels', false);
        return comments;
    });
};

export const backButtonResolver: ResolveFn<any> = (route, state) => {
    // Check if we're coming from a project via query param
    const fromQueryParam = route.queryParams['from'];
    const projectId = route.queryParams['projectId'];
    const groupId = route.queryParams['groupId'];
    // Check if this is from a project and if we have a project ID
    const isFromProject = fromQueryParam === 'project' && projectId;
    const isFromGroup = fromQueryParam === 'group' && groupId;
    const projectUrl = isFromProject ? `/projects/${projectId}` : '/projects';
    const groupUrl = isFromGroup ? `/groups/${groupId}` : '/groups';

    const getBackButtonRoute = () => {
        if(isFromProject){
            return projectUrl;
        } else if(isFromGroup){
            return groupUrl;
        }else{
            return '/panels';
        }
    }

    const getBackButtonText = () => {
        if(isFromProject){
            return 'Back to Project';
        } else if(isFromGroup){
            return 'Back to Group';
        } else{
            return 'Back to Panels';
        }
    }

    return {
        backButtonText: getBackButtonText(),
        backButtonRoute: getBackButtonRoute(),
        isFromProject: isFromProject,
        isFromGroup: isFromGroup,
        projectId: projectId,
        groupId: groupId
    };
};