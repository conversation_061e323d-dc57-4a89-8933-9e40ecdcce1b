import { Pipe, PipeTransform } from '@angular/core';
import { AbstractControl } from '@angular/forms';

@Pipe({
  name: 'searchPipe',
  standalone: true
})
export class SearchPipePipe implements PipeTransform {

  transform(controls: AbstractControl[], searchTerm: any): AbstractControl[] {
    if(!searchTerm) return controls;

    return controls.filter(control => {
      const name = control.get('name')?.value
      return name && name.toLowerCase().includes(searchTerm.toLowerCase())});
  }

}
