@import '../../../_colors';

:host{
    // host styles in styles.scss .full-screen-form
    position: fixed;
    z-index: 999998;
    width: 100%;
    height: 100%;
    top: 0;
    left: 0;
    background-color: #fff;
    display: block;
    padding: 15px;
    box-sizing: border-box;
    overflow-y: auto;

    @mixin border() {
        border: 1px solid $mediumLightGray;
        border-radius: 18px; 
    }

    a{
        text-decoration: none;
        color: unset;
    }

    button-standard{
    
        span {
            margin-left: 10px;
        }

        &.back-button{
            position: absolute;
            top: 15px;
            left: 15px;
        }
    }


    &>div{
        // border: 1px solid red;
        --panel-details-width: calc(100% - 30px);
        width: var(--panel-details-width);
        --panel-details-max-width: 932px;
        max-width: var(--panel-details-max-width);
        margin: 0 auto;
    }

    .panel-name-row{
        margin-top: 70px;
        margin-bottom: 30px;
        display: grid;
        grid-template-columns: 2fr 1fr;
        align-items: flex-start;

        .panel-name-grid-area{

            .panel-name{
                --panel-name-font-size: 1.625rem;
                font-size: var(--panel-name-font-size);
                font-weight: 700;
            }

            .panel-product-container{
                display: flex;
                align-items: center;
                justify-content: flex-start;
                flex-direction: row;
                flex-wrap: nowrap;
                --panel-product-container-font-size: 0.875rem;
                font-size: var(--panel-product-container-font-size);
                line-height: var(--panel-product-container-font-size);
                color: $darkestGray;
                margin-top: 10px;
            }
        }

        .buttons-grid-area{
            display: flex;
            flex-direction: row;
            flex-wrap: wrap;
            justify-content: flex-end;
            align-items: center;
            gap: 10px;

            .actions-button-container{
                position: relative;
                width: fit-content;
                height: fit-content;

                .actions-button{
                    margin: 0;
    
                    svg{
                    }
    
                    span{
                    }
                }

                popover{
                    --router-link-color: #{$darkGray};
                    --router-link-size: .9rem;
                    --router-link-hover-color: #{$blue};
                    z-index: 999999;

                    .popover-item{
                        text-decoration: none;
                        color: var(--router-link-color);
                        font-size: var(--router-link-size);
                        transition: color 0.2s ease;
                        cursor: pointer;
                        width: fit-content;
                        white-space: nowrap;
        
                        &:hover{
                            color: var(--router-link-hover-color);
                        }
                    }
                }
            }
        }
    }

    .panel-summary-row{
        @include border();
        display: flex;
        flex-flow: row nowrap;
        margin-bottom: 45px;

        .panel-summary-section{
            flex-grow: 1;
            padding:  12px 15px 15px;

            &:not(:last-child){
                border-right: 1px solid $mediumLightGray;
            }

            &.response-count-section{
                display: grid;
                grid-template-columns: 1fr 1fr;
                align-items: center;
                justify-content: space-between;
                
                .section-label{
                    grid-column: span 2;
                }

                button{
                    cursor: pointer;
                    text-decoration: underline;
                    color: #636363;
                    font-weight: 500;
                    font-size: .75rem;

                    &:hover{
                        color: #292727;
                    }
                }
            }

            .section-label{
                font-size: .75rem;
                font-weight: 500;
                color: $darkGray;
                margin-bottom: 12px;
                display: flex;

                span{
                    margin-left: auto;
                    text-decoration: underline;
                    cursor: pointer;
                }
            }

            .section-content{
                font-weight: 700;
                display: flex;
                font-size: 1rem;
                align-items: center;
                position: relative;

                .sample-icon-container,
                .collaborator-icon-container{
                    position: relative;
                    display: inline-flex;
                    justify-content: center;
                    
                    lamb-graph-tooltip{
                        position:absolute;
                        top: auto;
                        bottom: calc(100% + 10px);
                        left: 50%;
                        transform: translateX(-50%);
                        opacity: 0;
                        z-index: -1;
                        transition: opacity 0.2s ease, z-index 0s 0.2;
                    }

                    lamb-gp-icon:hover + lamb-graph-tooltip{
                        opacity: 1;
                        z-index: 999999;
                        transition: opacity 0.2s ease, z-index 0s 0s;
                    }
                }

                .add-collaborator-btn{
                    cursor:pointer;
                    margin-left: 10px;
                }

                .collaborators-modal{
                    position: absolute;
                    right: 0;
                    width: 267px;
                    height: 287px;
                    background-color: #fff;
                    box-shadow: 0px 4px 10px 0px rgba(0, 0, 0, 0.25);
                    border-radius: 12px;
                    padding: 15px;
                    z-index: 999999;
                    overflow: auto;
                }
            }
        }
    }

    .associated-panels-header{

        .table-title{
            font-size: 1.125rem;
            font-weight: 700;
            display: flex;
            align-items: center;
            
            span{
                color: #8A8A8A;
                font-size: 0.8125rem;
                font-weight: 500;
                margin-left: 5px;
            }
        }

        .summary-and-actions{
            display: grid;
            grid-template-columns: auto auto 1fr;
            align-items: center;
            margin-bottom: 25px;

            .arrow-container{
                margin-left: 5px;
            }
    
            .summary-container{
                border: 1px solid #E3E3E3;
                border-radius: 12px;
                margin-top: 16px;
                display: flex;
                flex-flow: row nowrap;
                align-items: center;
                margin-left: 3px;

                .response-count{
                    font-size: 0.75rem;
                    font-weight: 600;
                    padding: 10px;
                    border-right: 1px solid #E3E3E3;
                }

                .privacy-summary{
                    font-size: 0.75rem;
                    font-weight: 600;
                    padding: 10px;
                }
            }
    
            .add-panel-container{
                justify-self: flex-end;
            }
        }
    
    
    }

    .associated-panels-table{
        position: relative;
    }

    .responses-drawer-overlay{
        position: fixed;
        inset: 0;
        background-color: rgba(0, 0, 0, 0.5);
        z-index: 999999;
        width: 100%;
        height: 100%;
        margin: 0;
    }
    
    .responses-drawer{
        position: fixed;
        height: 100%;
        top: 0;
        right: 0;
        width: 500px;
        background-color: #fff;
        z-index: 999999;
        padding: 15px;

        button-standard{
            margin-left: 0px
        }

        .responses-drawer-header{
            font-size: 1.125rem;
            font-weight: 700;
            margin-left: 10px;
            margin-top: 20px;
            margin-bottom: 20px;
        }
        
        .responses-drawer-content{
            overflow: auto;
            height: calc(100% - 100px);
        }
    }

    .panels-drawer-overlay{
        position: fixed;
        inset: 0;
        background-color: rgba(0, 0, 0, 0.5);
        z-index: 999999;
        width: 100%;
        height: 100%;
        margin: 0;
    }
    
    .panels-drawer{
        position: fixed;
        height: 100%;
        top: 0;
        right: 0;
        width: 500px;
        background-color: #fff;
        z-index: 999999;
        padding: 15px;

        button-standard{
            margin-left: 0px
        }

        .panels-drawer-header{
            font-size: 1.125rem;
            font-weight: 700;
            margin-left: 10px;
            margin-top: 20px;
            margin-bottom: 20px;
        }
        
        .panels-drawer-content{
            overflow: auto;
            height: calc(100% - 100px);

            .load-more-btn {
                display: block;
                width: 100%;
                padding: 10px;
                margin-top: 15px;
                background: none;
                border: none;
                color: #636363;
                font-weight: 500;
                font-size: .875rem;
                cursor: pointer;
                text-decoration: underline;

                &:hover {
                    color: #292727;
                }
            }
        }
    }

    .edit-drawer-overlay{
        position: fixed;
        inset: 0;
        background-color: rgba(0, 0, 0, 0.5);
        z-index: 999999;
        width: 100%;
        height: 100%;
        margin: 0;
    }

    .edit-drawer{
        position: fixed;
        height: 100%;
        width: 500px;
        right: 0;
        top: 0;
        background-color: #fff;
        z-index: 999999;
        padding: 15px;

        button-standard.exit-button{
            margin-left: 0px;
        }

        .edit-drawer-header{
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin: 20px 0px;

            span{
                font-size: 1.125rem;
                font-weight: 700;
            }
            
            button-standard.save-button{
                margin-right: 0px;
            }
        }

        .edit-drawer-content{
            overflow: auto;
            height: calc(100% - 100px);
            padding: 2px;

            form{
                width: 100%;

                .input-container{
                    display: grid;
                    align-items: center;
                    --input-container-label-width: 100px;
        
                    &.name{
                        grid-template-columns: var(--input-container-label-width) 1fr;
        
                        input-text{
                            width: 100%;
                        }
                    }
        
                    &.radio-container{
                        grid-template-columns: var(--input-container-label-width) 1fr 1fr;
        
                        input-radio-block{
                            width: 100%;
                        }
                    }
        
                    &.date-picker-container{
                        grid-template-columns: var(--input-container-label-width) 1fr 1fr;
                        margin-top: -15px;

                        &.standalone-datepicker{
                            margin-top: 0;
                            
                            input-bs-datepicker{
                                grid-column: 2;
                                right: 0;
                            }
                        }
        
                        svg{
                            grid-column: 2;
                            justify-self: end;
                            top: -12px;
                            right: -50px;
                            position: relative;
                        }
        
                        input-bs-datepicker{
                            justify-self: left;
                            grid-column: 3;
                            right: -35px;
                            position: relative;
                            // margin-right: 10px;
                        }
                    }
                }
            }
        }
    }
}

// .panel-sample-cell{
//     position: relative;

//     .icon-container{
//         position: relative;
//         display: inline-flex;
//         justify-content: center;
        
//         lamb-graph-tooltip{
//             top: auto;
//             bottom: calc(100% + 10px);
//             left: 50%;
//             transform: translateX(-50%);
//             z-index: 999999;
//         }   
//     }
// }

.panel-sample-tooltip{
    position: fixed;
    inset: auto;
    z-index: 999999;
}

.view-responses-button{
    margin-right: 0px;
}

.select-cell{
    display: flex;
    align-items: center;
    justify-content: center;
    position: relative;
    
    .checkbox-spinner {
        position: absolute;
        right: -20px;
        
        .spinner.small {
            width: 18px;
            height: 18px;
            animation: spin 1s linear infinite;
            
            .path {
                stroke: #555;
                stroke-linecap: round;
                animation: spinner-dash 1.5s ease-in-out infinite;
            }
        }
    }

    input{
        width: 16px;
            height: 16px;
            background-color: #fff;
            border: 2px solid #fff;
            outline: $inputBorder;
            border-radius: 50%;
            margin-right: 10px;
            cursor: pointer;
            flex-shrink: 0;
    
            &:checked {
                background-color: $blue;
            }
    
            &:focus {
                outline-color: $blue;
            }
    }
}

.panelist-cell{
    display: flex;
    align-items: center;
    gap: 10px;
    
    
}

.loading-spinner {
    display: flex;
    justify-content: center;
    align-items: center;
    width: 30px;
    margin: 0 auto;
    
    .spinner {
      animation: rotate 2s linear infinite;
      width: 20px;
      height: 20px;
      
        & .path {
            stroke: currentColor;
            stroke-linecap: round;
            animation: dash 1.5s ease-in-out infinite;
        }
    }
}

@keyframes rotate {
  100% {
    transform: rotate(360deg);
  }
}

@keyframes dash {
  0% {
    stroke-dasharray: 1, 150;
    stroke-dashoffset: 0;
  }
  50% {
    stroke-dasharray: 90, 150;
    stroke-dashoffset: -35;
  }
  100% {
    stroke-dasharray: 90, 150;
    stroke-dashoffset: -124;
  }
}

@keyframes spin {
  100% {
    transform: rotate(360deg);
  }
}

@keyframes spinner-dash {
  0% {
    stroke-dasharray: 1, 150;
    stroke-dashoffset: 0;
  }
  50% {
    stroke-dasharray: 90, 150;
    stroke-dashoffset: -35;
  }
  100% {
    stroke-dasharray: 90, 150;
    stroke-dashoffset: -124;
  }
}